/**
 * ملف إعداد وتكوين الترجمة واللغات المتعددة
 * يقوم بإعداد وتكوين دعم اللغات المتعددة للتطبيق
 */

const i18next = require('i18next');
const i18nextFsBackend = require('i18next-fs-backend');
const i18nextHttpMiddleware = require('i18next-http-middleware');
const path = require('path');
const fs = require('fs');
const config = require('config');
const logger = require('../utils/logger');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

// اللغة الافتراضية واللغات المدعومة
const defaultLanguage = appConfig.defaultLanguage || 'ar';
const supportedLanguages = appConfig.supportedLanguages || ['ar', 'en'];

/**
 * إنشاء مجلدات الترجمة إذا لم تكن موجودة
 */
const setupLocalesDirectories = () => {
  const localesDir = path.join(process.cwd(), 'locales');
  
  // إنشاء مجلد الترجمة الرئيسي
  if (!fs.existsSync(localesDir)) {
    fs.mkdirSync(localesDir, { recursive: true });
    logger.info(`تم إنشاء مجلد الترجمة: ${localesDir}`);
  }
  
  // إنشاء مجلدات اللغات المدعومة
  supportedLanguages.forEach(lang => {
    const langDir = path.join(localesDir, lang);
    if (!fs.existsSync(langDir)) {
      fs.mkdirSync(langDir, { recursive: true });
      
      // إنشاء ملف ترجمة افتراضي
      const defaultTranslation = {
        translation: {
          common: {
            welcome: lang === 'ar' ? 'مرحبًا بك في نظام المطبعة المتطور' : 'Welcome to Advanced Print Shop System',
            language: lang === 'ar' ? 'اللغة' : 'Language',
            loading: lang === 'ar' ? 'جاري التحميل...' : 'Loading...',
            error: lang === 'ar' ? 'حدث خطأ' : 'An error occurred',
            success: lang === 'ar' ? 'تمت العملية بنجاح' : 'Operation completed successfully',
            save: lang === 'ar' ? 'حفظ' : 'Save',
            cancel: lang === 'ar' ? 'إلغاء' : 'Cancel',
            edit: lang === 'ar' ? 'تعديل' : 'Edit',
            delete: lang === 'ar' ? 'حذف' : 'Delete',
            create: lang === 'ar' ? 'إنشاء' : 'Create',
            search: lang === 'ar' ? 'بحث' : 'Search',
            filter: lang === 'ar' ? 'تصفية' : 'Filter',
            noResults: lang === 'ar' ? 'لا توجد نتائج' : 'No results found',
            confirmDelete: lang === 'ar' ? 'هل أنت متأكد من الحذف؟' : 'Are you sure you want to delete?',
            yes: lang === 'ar' ? 'نعم' : 'Yes',
            no: lang === 'ar' ? 'لا' : 'No',
            back: lang === 'ar' ? 'رجوع' : 'Back',
            next: lang === 'ar' ? 'التالي' : 'Next',
            previous: lang === 'ar' ? 'السابق' : 'Previous',
            submit: lang === 'ar' ? 'إرسال' : 'Submit',
            required: lang === 'ar' ? 'مطلوب' : 'Required',
            optional: lang === 'ar' ? 'اختياري' : 'Optional',
            all: lang === 'ar' ? 'الكل' : 'All',
            actions: lang === 'ar' ? 'إجراءات' : 'Actions',
            details: lang === 'ar' ? 'التفاصيل' : 'Details',
            status: lang === 'ar' ? 'الحالة' : 'Status',
            date: lang === 'ar' ? 'التاريخ' : 'Date',
            time: lang === 'ar' ? 'الوقت' : 'Time',
            name: lang === 'ar' ? 'الاسم' : 'Name',
            description: lang === 'ar' ? 'الوصف' : 'Description',
            price: lang === 'ar' ? 'السعر' : 'Price',
            quantity: lang === 'ar' ? 'الكمية' : 'Quantity',
            total: lang === 'ar' ? 'الإجمالي' : 'Total',
            subtotal: lang === 'ar' ? 'المجموع الفرعي' : 'Subtotal',
            tax: lang === 'ar' ? 'الضريبة' : 'Tax',
            discount: lang === 'ar' ? 'الخصم' : 'Discount',
            active: lang === 'ar' ? 'نشط' : 'Active',
            inactive: lang === 'ar' ? 'غير نشط' : 'Inactive',
            enabled: lang === 'ar' ? 'مفعل' : 'Enabled',
            disabled: lang === 'ar' ? 'معطل' : 'Disabled',
            settings: lang === 'ar' ? 'الإعدادات' : 'Settings',
            profile: lang === 'ar' ? 'الملف الشخصي' : 'Profile',
            logout: lang === 'ar' ? 'تسجيل الخروج' : 'Logout',
            login: lang === 'ar' ? 'تسجيل الدخول' : 'Login',
            register: lang === 'ar' ? 'تسجيل' : 'Register',
            forgotPassword: lang === 'ar' ? 'نسيت كلمة المرور' : 'Forgot Password',
            resetPassword: lang === 'ar' ? 'إعادة تعيين كلمة المرور' : 'Reset Password',
            changePassword: lang === 'ar' ? 'تغيير كلمة المرور' : 'Change Password',
            dashboard: lang === 'ar' ? 'لوحة التحكم' : 'Dashboard',
            reports: lang === 'ar' ? 'التقارير' : 'Reports',
            help: lang === 'ar' ? 'المساعدة' : 'Help',
            about: lang === 'ar' ? 'حول' : 'About',
            contactUs: lang === 'ar' ? 'اتصل بنا' : 'Contact Us',
            termsOfService: lang === 'ar' ? 'شروط الخدمة' : 'Terms of Service',
            privacyPolicy: lang === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy',
            copyright: lang === 'ar' ? 'حقوق النشر' : 'Copyright',
            version: lang === 'ar' ? 'الإصدار' : 'Version',
            poweredBy: lang === 'ar' ? 'مدعوم بواسطة' : 'Powered by',
            madeWith: lang === 'ar' ? 'صنع بـ' : 'Made with',
            and: lang === 'ar' ? 'و' : 'and',
            by: lang === 'ar' ? 'بواسطة' : 'by',
            in: lang === 'ar' ? 'في' : 'in',
            on: lang === 'ar' ? 'على' : 'on',
            at: lang === 'ar' ? 'في' : 'at',
            for: lang === 'ar' ? 'لـ' : 'for',
            from: lang === 'ar' ? 'من' : 'from',
            to: lang === 'ar' ? 'إلى' : 'to',
            of: lang === 'ar' ? 'من' : 'of',
            with: lang === 'ar' ? 'مع' : 'with',
            or: lang === 'ar' ? 'أو' : 'or',
          },
          validation: {
            required: lang === 'ar' ? '{{field}} مطلوب' : '{{field}} is required',
            minLength: lang === 'ar' ? '{{field}} يجب أن يكون على الأقل {{min}} أحرف' : '{{field}} must be at least {{min}} characters',
            maxLength: lang === 'ar' ? '{{field}} يجب أن يكون أقل من {{max}} أحرف' : '{{field}} must be less than {{max}} characters',
            email: lang === 'ar' ? 'يرجى إدخال بريد إلكتروني صالح' : 'Please enter a valid email address',
            number: lang === 'ar' ? '{{field}} يجب أن يكون رقمًا' : '{{field}} must be a number',
            min: lang === 'ar' ? '{{field}} يجب أن يكون على الأقل {{min}}' : '{{field}} must be at least {{min}}',
            max: lang === 'ar' ? '{{field}} يجب أن يكون أقل من {{max}}' : '{{field}} must be less than {{max}}',
            pattern: lang === 'ar' ? '{{field}} غير صالح' : '{{field}} is invalid',
            match: lang === 'ar' ? '{{field}} يجب أن يتطابق مع {{matchField}}' : '{{field}} must match {{matchField}}',
            unique: lang === 'ar' ? '{{field}} مستخدم بالفعل' : '{{field}} is already in use',
          },
          errors: {
            general: lang === 'ar' ? 'حدث خطأ ما. يرجى المحاولة مرة أخرى.' : 'Something went wrong. Please try again.',
            notFound: lang === 'ar' ? 'لم يتم العثور على الصفحة المطلوبة' : 'The requested page was not found',
            unauthorized: lang === 'ar' ? 'غير مصرح لك بالوصول إلى هذه الصفحة' : 'You are not authorized to access this page',
            forbidden: lang === 'ar' ? 'ليس لديك صلاحية للوصول إلى هذه الصفحة' : 'You do not have permission to access this page',
            serverError: lang === 'ar' ? 'حدث خطأ في الخادم' : 'A server error occurred',
            badRequest: lang === 'ar' ? 'طلب غير صالح' : 'Bad request',
            validationError: lang === 'ar' ? 'خطأ في التحقق من الصحة' : 'Validation error',
            networkError: lang === 'ar' ? 'خطأ في الاتصال بالشبكة' : 'Network error',
            timeoutError: lang === 'ar' ? 'انتهت مهلة الطلب' : 'Request timeout',
            conflictError: lang === 'ar' ? 'تعارض في البيانات' : 'Data conflict',
            notImplemented: lang === 'ar' ? 'لم يتم تنفيذ هذه الميزة بعد' : 'This feature is not implemented yet',
            maintenanceMode: lang === 'ar' ? 'النظام في وضع الصيانة' : 'System is in maintenance mode',
            sessionExpired: lang === 'ar' ? 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.' : 'Your session has expired. Please log in again.',
            invalidCredentials: lang === 'ar' ? 'بيانات الاعتماد غير صالحة' : 'Invalid credentials',
            accountLocked: lang === 'ar' ? 'تم قفل الحساب' : 'Account is locked',
            accountDisabled: lang === 'ar' ? 'تم تعطيل الحساب' : 'Account is disabled',
            emailNotVerified: lang === 'ar' ? 'لم يتم التحقق من البريد الإلكتروني' : 'Email is not verified',
            passwordResetRequired: lang === 'ar' ? 'يجب إعادة تعيين كلمة المرور' : 'Password reset is required',
            tooManyRequests: lang === 'ar' ? 'عدد كبير جدًا من الطلبات. يرجى المحاولة مرة أخرى لاحقًا.' : 'Too many requests. Please try again later.',
          },
          auth: {
            login: lang === 'ar' ? 'تسجيل الدخول' : 'Login',
            register: lang === 'ar' ? 'تسجيل' : 'Register',
            logout: lang === 'ar' ? 'تسجيل الخروج' : 'Logout',
            email: lang === 'ar' ? 'البريد الإلكتروني' : 'Email',
            password: lang === 'ar' ? 'كلمة المرور' : 'Password',
            confirmPassword: lang === 'ar' ? 'تأكيد كلمة المرور' : 'Confirm Password',
            forgotPassword: lang === 'ar' ? 'نسيت كلمة المرور؟' : 'Forgot Password?',
            resetPassword: lang === 'ar' ? 'إعادة تعيين كلمة المرور' : 'Reset Password',
            changePassword: lang === 'ar' ? 'تغيير كلمة المرور' : 'Change Password',
            currentPassword: lang === 'ar' ? 'كلمة المرور الحالية' : 'Current Password',
            newPassword: lang === 'ar' ? 'كلمة المرور الجديدة' : 'New Password',
            confirmNewPassword: lang === 'ar' ? 'تأكيد كلمة المرور الجديدة' : 'Confirm New Password',
            rememberMe: lang === 'ar' ? 'تذكرني' : 'Remember Me',
            loginSuccess: lang === 'ar' ? 'تم تسجيل الدخول بنجاح' : 'Logged in successfully',
            logoutSuccess: lang === 'ar' ? 'تم تسجيل الخروج بنجاح' : 'Logged out successfully',
            registerSuccess: lang === 'ar' ? 'تم التسجيل بنجاح' : 'Registered successfully',
            passwordResetSuccess: lang === 'ar' ? 'تم إعادة تعيين كلمة المرور بنجاح' : 'Password reset successfully',
            passwordChangeSuccess: lang === 'ar' ? 'تم تغيير كلمة المرور بنجاح' : 'Password changed successfully',
            passwordResetEmailSent: lang === 'ar' ? 'تم إرسال بريد إلكتروني لإعادة تعيين كلمة المرور' : 'Password reset email sent',
            verificationEmailSent: lang === 'ar' ? 'تم إرسال بريد إلكتروني للتحقق' : 'Verification email sent',
            emailVerified: lang === 'ar' ? 'تم التحقق من البريد الإلكتروني بنجاح' : 'Email verified successfully',
            alreadyLoggedIn: lang === 'ar' ? 'أنت مسجل الدخول بالفعل' : 'You are already logged in',
            notLoggedIn: lang === 'ar' ? 'أنت غير مسجل الدخول' : 'You are not logged in',
            loginRequired: lang === 'ar' ? 'يجب تسجيل الدخول للوصول إلى هذه الصفحة' : 'Login required to access this page',
            verificationRequired: lang === 'ar' ? 'يجب التحقق من البريد الإلكتروني للوصول إلى هذه الصفحة' : 'Email verification required to access this page',
            accountCreated: lang === 'ar' ? 'تم إنشاء الحساب بنجاح' : 'Account created successfully',
            sessionExpired: lang === 'ar' ? 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.' : 'Your session has expired. Please log in again.',
          },
          users: {
            users: lang === 'ar' ? 'المستخدمون' : 'Users',
            user: lang === 'ar' ? 'المستخدم' : 'User',
            addUser: lang === 'ar' ? 'إضافة مستخدم' : 'Add User',
            editUser: lang === 'ar' ? 'تعديل المستخدم' : 'Edit User',
            deleteUser: lang === 'ar' ? 'حذف المستخدم' : 'Delete User',
            userDetails: lang === 'ar' ? 'تفاصيل المستخدم' : 'User Details',
            userList: lang === 'ar' ? 'قائمة المستخدمين' : 'User List',
            name: lang === 'ar' ? 'الاسم' : 'Name',
            email: lang === 'ar' ? 'البريد الإلكتروني' : 'Email',
            role: lang === 'ar' ? 'الدور' : 'Role',
            status: lang === 'ar' ? 'الحالة' : 'Status',
            lastLogin: lang === 'ar' ? 'آخر تسجيل دخول' : 'Last Login',
            createdAt: lang === 'ar' ? 'تاريخ الإنشاء' : 'Created At',
            updatedAt: lang === 'ar' ? 'تاريخ التحديث' : 'Updated At',
            active: lang === 'ar' ? 'نشط' : 'Active',
            inactive: lang === 'ar' ? 'غير نشط' : 'Inactive',
            admin: lang === 'ar' ? 'مدير' : 'Admin',
            manager: lang === 'ar' ? 'مدير' : 'Manager',
            employee: lang === 'ar' ? 'موظف' : 'Employee',
            accountant: lang === 'ar' ? 'محاسب' : 'Accountant',
            userCreated: lang === 'ar' ? 'تم إنشاء المستخدم بنجاح' : 'User created successfully',
            userUpdated: lang === 'ar' ? 'تم تحديث المستخدم بنجاح' : 'User updated successfully',
            userDeleted: lang === 'ar' ? 'تم حذف المستخدم بنجاح' : 'User deleted successfully',
            userActivated: lang === 'ar' ? 'تم تنشيط المستخدم بنجاح' : 'User activated successfully',
            userDeactivated: lang === 'ar' ? 'تم إلغاء تنشيط المستخدم بنجاح' : 'User deactivated successfully',
            confirmDeleteUser: lang === 'ar' ? 'هل أنت متأكد من حذف هذا المستخدم؟' : 'Are you sure you want to delete this user?',
            cannotDeleteSelf: lang === 'ar' ? 'لا يمكنك حذف حسابك الخاص' : 'You cannot delete your own account',
            cannotDeactivateSelf: lang === 'ar' ? 'لا يمكنك إلغاء تنشيط حسابك الخاص' : 'You cannot deactivate your own account',
            profile: lang === 'ar' ? 'الملف الشخصي' : 'Profile',
            editProfile: lang === 'ar' ? 'تعديل الملف الشخصي' : 'Edit Profile',
            profileUpdated: lang === 'ar' ? 'تم تحديث الملف الشخصي بنجاح' : 'Profile updated successfully',
            avatar: lang === 'ar' ? 'الصورة الرمزية' : 'Avatar',
            changeAvatar: lang === 'ar' ? 'تغيير الصورة الرمزية' : 'Change Avatar',
            avatarUpdated: lang === 'ar' ? 'تم تحديث الصورة الرمزية بنجاح' : 'Avatar updated successfully',
            personalInfo: lang === 'ar' ? 'المعلومات الشخصية' : 'Personal Information',
            accountSettings: lang === 'ar' ? 'إعدادات الحساب' : 'Account Settings',
            securitySettings: lang === 'ar' ? 'إعدادات الأمان' : 'Security Settings',
            notificationSettings: lang === 'ar' ? 'إعدادات الإشعارات' : 'Notification Settings',
            phoneNumber: lang === 'ar' ? 'رقم الهاتف' : 'Phone Number',
            address: lang === 'ar' ? 'العنوان' : 'Address',
            language: lang === 'ar' ? 'اللغة' : 'Language',
            timezone: lang === 'ar' ? 'المنطقة الزمنية' : 'Timezone',
            dateFormat: lang === 'ar' ? 'تنسيق التاريخ' : 'Date Format',
            timeFormat: lang === 'ar' ? 'تنسيق الوقت' : 'Time Format',
            theme: lang === 'ar' ? 'السمة' : 'Theme',
            light: lang === 'ar' ? 'فاتح' : 'Light',
            dark: lang === 'ar' ? 'داكن' : 'Dark',
            system: lang === 'ar' ? 'النظام' : 'System',
          },
          customers: {
            customers: lang === 'ar' ? 'العملاء' : 'Customers',
            customer: lang === 'ar' ? 'العميل' : 'Customer',
            addCustomer: lang === 'ar' ? 'إضافة عميل' : 'Add Customer',
            editCustomer: lang === 'ar' ? 'تعديل العميل' : 'Edit Customer',
            deleteCustomer: lang === 'ar' ? 'حذف العميل' : 'Delete Customer',
            customerDetails: lang === 'ar' ? 'تفاصيل العميل' : 'Customer Details',
            customerList: lang === 'ar' ? 'قائمة العملاء' : 'Customer List',
            name: lang === 'ar' ? 'الاسم' : 'Name',
            email: lang === 'ar' ? 'البريد الإلكتروني' : 'Email',
            phone: lang === 'ar' ? 'الهاتف' : 'Phone',
            address: lang === 'ar' ? 'العنوان' : 'Address',
            type: lang === 'ar' ? 'النوع' : 'Type',
            taxId: lang === 'ar' ? 'الرقم الضريبي' : 'Tax ID',
            creditLimit: lang === 'ar' ? 'حد الائتمان' : 'Credit Limit',
            balance: lang === 'ar' ? 'الرصيد' : 'Balance',
            status: lang === 'ar' ? 'الحالة' : 'Status',
            createdAt: lang === 'ar' ? 'تاريخ الإنشاء' : 'Created At',
            updatedAt: lang === 'ar' ? 'تاريخ التحديث' : 'Updated At',
            active: lang === 'ar' ? 'نشط' : 'Active',
            inactive: lang === 'ar' ? 'غير نشط' : 'Inactive',
            individual: lang === 'ar' ? 'فرد' : 'Individual',
            company: lang === 'ar' ? 'شركة' : 'Company',
            government: lang === 'ar' ? 'حكومي' : 'Government',
            customerCreated: lang === 'ar' ? 'تم إنشاء العميل بنجاح' : 'Customer created successfully',
            customerUpdated: lang === 'ar' ? 'تم تحديث العميل بنجاح' : 'Customer updated successfully',
            customerDeleted: lang === 'ar' ? 'تم حذف العميل بنجاح' : 'Customer deleted successfully',
            customerActivated: lang === 'ar' ? 'تم تنشيط العميل بنجاح' : 'Customer activated successfully',
            customerDeactivated: lang === 'ar' ? 'تم إلغاء تنشيط العميل بنجاح' : 'Customer deactivated successfully',
            confirmDeleteCustomer: lang === 'ar' ? 'هل أنت متأكد من حذف هذا العميل؟' : 'Are you sure you want to delete this customer?',
            contactInfo: lang === 'ar' ? 'معلومات الاتصال' : 'Contact Information',
            billingInfo: lang === 'ar' ? 'معلومات الفوترة' : 'Billing Information',
            shippingInfo: lang === 'ar' ? 'معلومات الشحن' : 'Shipping Information',
            contactPerson: lang === 'ar' ? 'الشخص المسؤول' : 'Contact Person',
            contactPersonPhone: lang === 'ar' ? 'هاتف الشخص المسؤول' : 'Contact Person Phone',
            contactPersonEmail: lang === 'ar' ? 'بريد الشخص المسؤول' : 'Contact Person Email',
            website: lang === 'ar' ? 'الموقع الإلكتروني' : 'Website',
            notes: lang === 'ar' ? 'ملاحظات' : 'Notes',
            orders: lang === 'ar' ? 'الطلبات' : 'Orders',
            invoices: lang === 'ar' ? 'الفواتير' : 'Invoices',
            payments: lang === 'ar' ? 'المدفوعات' : 'Payments',
            quotations: lang === 'ar' ? 'عروض الأسعار' : 'Quotations',
            transactions: lang === 'ar' ? 'المعاملات' : 'Transactions',
            history: lang === 'ar' ? 'السجل' : 'History',
          },
          // يمكن إضافة المزيد من الترجمات حسب الحاجة
        }
      };
      
      fs.writeFileSync(
        path.join(langDir, 'translation.json'),
        JSON.stringify(defaultTranslation, null, 2),
        'utf8'
      );
      
      logger.info(`تم إنشاء ملف ترجمة افتراضي للغة: ${lang}`);
    }
  });
  
  return localesDir;
};

/**
 * إعداد i18next
 */
const setupI18n = async () => {
  // إنشاء مجلدات الترجمة
  const localesDir = setupLocalesDirectories();
  
  // إعداد i18next
  await i18next
    .use(i18nextFsBackend)
    .use(i18nextHttpMiddleware.LanguageDetector)
    .init({
      backend: {
        loadPath: path.join(localesDir, '{{lng}}/{{ns}}.json'),
        addPath: path.join(localesDir, '{{lng}}/{{ns}}.missing.json'),
      },
      fallbackLng: defaultLanguage,
      preload: supportedLanguages,
      saveMissing: process.env.NODE_ENV !== 'production',
      debug: process.env.NODE_ENV !== 'production',
      interpolation: {
        escapeValue: false,
      },
      detection: {
        order: ['querystring', 'cookie', 'header'],
        lookupQuerystring: 'lang',
        lookupCookie: 'i18next',
        lookupHeader: 'accept-language',
        caches: ['cookie'],
      },
    });
  
  logger.info(`تم إعداد i18next بنجاح. اللغة الافتراضية: ${defaultLanguage}`);
  
  return i18next;
};

/**
 * إعداد وسيط i18next في التطبيق
 * @param {Object} app - تطبيق Express
 */
const setupI18nMiddleware = (app) => {
  // إضافة وسيط i18next
  app.use(i18nextHttpMiddleware.handle(i18next));
  
  // إضافة مسار للحصول على ترجمات العميل
  app.get('/api/translations/:lng', (req, res) => {
    const lng = req.params.lng;
    
    if (!supportedLanguages.includes(lng)) {
      return res.status(400).json({
        status: 'error',
        message: `اللغة غير مدعومة: ${lng}`,
        supportedLanguages,
      });
    }
    
    const translations = i18next.getResourceBundle(lng, 'translation');
    
    res.json({
      status: 'success',
      data: translations,
    });
  });
  
  // إضافة مسار للحصول على اللغات المدعومة
  app.get('/api/languages', (req, res) => {
    res.json({
      status: 'success',
      data: {
        default: defaultLanguage,
        supported: supportedLanguages,
      },
    });
  });
  
  logger.info('تم إعداد وسيط i18next بنجاح');
};

/**
 * ترجمة نص
 * @param {string} key - مفتاح الترجمة
 * @param {Object} options - خيارات الترجمة
 * @param {string} lng - اللغة (اختياري)
 * @returns {string} النص المترجم
 */
const translate = (key, options = {}, lng = null) => {
  return i18next.t(key, { ...options, lng });
};

module.exports = {
  setupI18n,
  setupI18nMiddleware,
  translate,
  i18next,
  defaultLanguage,
  supportedLanguages,
};