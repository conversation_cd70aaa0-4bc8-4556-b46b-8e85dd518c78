const mongoose = require('mongoose');

/**
 * نموذج المخزون
 * يستخدم لتخزين بيانات المواد الخام والمستلزمات في المطبعة
 */
const InventorySchema = new mongoose.Schema({
  itemCode: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['paper', 'ink', 'cover', 'binding', 'packaging', 'other'],
    required: true
  },
  description: {
    type: String
  },
  unit: {
    type: String,
    required: true,
    enum: ['piece', 'pack', 'box', 'roll', 'ream', 'liter', 'kg', 'meter']
  },
  quantity: {
    type: Number,
    required: true,
    default: 0
  },
  minQuantity: {
    type: Number,
    default: 10
  },
  costPrice: {
    type: Number,
    required: true
  },
  supplier: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'supplier'
  },
  location: {
    type: String
  },
  specifications: {
    // مواصفات خاصة بالورق
    paperType: { type: String },
    paperSize: { type: String },
    paperWeight: { type: String },
    paperColor: { type: String },
    
    // مواصفات خاصة بالأحبار
    inkColor: { type: String },
    inkType: { type: String },
    
    // مواصفات خاصة بالتجليد
    bindingType: { type: String },
    
    // مواصفات عامة
    brand: { type: String },
    dimensions: { type: String },
    additionalInfo: { type: String }
  },
  transactions: [{
    type: {
      type: String,
      enum: ['purchase', 'usage', 'return', 'adjustment'],
      required: true
    },
    quantity: {
      type: Number,
      required: true
    },
    date: {
      type: Date,
      default: Date.now
    },
    reference: {
      type: String
    },
    orderId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'order'
    },
    notes: {
      type: String
    },
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'user'
    }
  }],
  image: {
    type: String
  },
  active: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user'
  }
});

// تحديث تاريخ التعديل عند تحديث البيانات
InventorySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// إنشاء كود المنتج تلقائياً
InventorySchema.pre('save', async function(next) {
  if (this.isNew && !this.itemCode) {
    try {
      // الحصول على آخر عنصر في نفس الفئة
      const lastItem = await this.constructor.findOne(
        { category: this.category },
        {},
        { sort: { 'createdAt': -1 } }
      );
      
      // إنشاء كود المنتج الجديد
      const categoryPrefix = this.category.substr(0, 3).toUpperCase();
      
      if (lastItem && lastItem.itemCode) {
        // استخراج الرقم التسلسلي من آخر عنصر
        const lastNumber = parseInt(lastItem.itemCode.split('-')[1]);
        this.itemCode = `${categoryPrefix}-${(lastNumber + 1).toString().padStart(4, '0')}`;
      } else {
        // إذا لم يكن هناك عناصر سابقة في نفس الفئة
        this.itemCode = `${categoryPrefix}-0001`;
      }
    } catch (err) {
      return next(err);
    }
  }
  next();
});

// إضافة طريقة للتحقق من مستوى المخزون
InventorySchema.methods.isLowStock = function() {
  return this.quantity <= this.minQuantity;
};

// إضافة طريقة لتحديث الكمية
InventorySchema.methods.updateQuantity = function(type, quantity, reference, orderId, notes, userId) {
  // تحديد الكمية الجديدة
  let newQuantity = this.quantity;
  
  switch (type) {
    case 'purchase':
      newQuantity += quantity;
      break;
    case 'usage':
      newQuantity -= quantity;
      break;
    case 'return':
      newQuantity += quantity;
      break;
    case 'adjustment':
      newQuantity = quantity; // تعيين الكمية مباشرة
      break;
  }
  
  // التحقق من أن الكمية لا تكون سالبة
  if (newQuantity < 0) {
    throw new Error('الكمية لا يمكن أن تكون سالبة');
  }
  
  // تحديث الكمية
  this.quantity = newQuantity;
  
  // إضافة المعاملة إلى سجل المعاملات
  this.transactions.push({
    type,
    quantity,
    date: new Date(),
    reference,
    orderId,
    notes,
    performedBy: userId
  });
  
  return this.save();
};

module.exports = mongoose.model('inventory', InventorySchema);