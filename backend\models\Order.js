const mongoose = require('mongoose');

/**
 * نموذج الطلب
 * يستخدم لتخزين بيانات طلبات الطباعة في النظام
 */
const OrderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'customer',
    required: true
  },
  orderDate: {
    type: Date,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: [
      'new',           // جديد
      'design',         // قيد التصميم
      'review',         // قيد المراجعة
      'approved',       // تمت الموافقة
      'production',     // قيد الإنتاج
      'printing',       // قيد الطباعة
      'binding',        // قيد التجليد
      'packaging',      // قيد التغليف
      'ready',          // جاهز للتسليم
      'delivered',      // تم التسليم
      'cancelled',      // ملغي
      'on-hold'         // معلق
    ],
    default: 'new'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  items: [{
    productType: {
      type: String,
      enum: ['book', 'brochure', 'flyer', 'business_card', 'poster', 'sticker', 'banner', 'other'],
      required: true
    },
    description: {
      type: String,
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 1
    },
    paperType: {
      type: String
    },
    paperWeight: {
      type: String
    },
    paperSize: {
      type: String
    },
    colorType: {
      type: String,
      enum: ['black_white', 'full_color', 'spot_color'],
      default: 'full_color'
    },
    sides: {
      type: String,
      enum: ['single', 'double'],
      default: 'single'
    },
    finishing: [{
      type: String,
      enum: ['lamination', 'binding', 'folding', 'cutting', 'stapling', 'hole_drilling', 'other']
    }],
    unitPrice: {
      type: Number,
      required: true
    },
    totalPrice: {
      type: Number,
      required: true
    }
  }],
  files: [{
    name: {
      type: String
    },
    path: {
      type: String
    },
    uploadDate: {
      type: Date,
      default: Date.now
    },
    fileType: {
      type: String
    }
  }],
  notes: {
    type: String
  },
  internalNotes: {
    type: String
  },
  subtotal: {
    type: Number,
    required: true
  },
  taxRate: {
    type: Number,
    default: 15 // ضريبة القيمة المضافة 15%
  },
  taxAmount: {
    type: Number,
    required: true
  },
  discount: {
    type: Number,
    default: 0
  },
  totalAmount: {
    type: Number,
    required: true
  },
  paymentStatus: {
    type: String,
    enum: ['unpaid', 'partial', 'paid'],
    default: 'unpaid'
  },
  paidAmount: {
    type: Number,
    default: 0
  },
  productionStages: [{
    stage: {
      type: String,
      enum: ['design', 'review', 'printing', 'binding', 'packaging']
    },
    startDate: {
      type: Date
    },
    endDate: {
      type: Date
    },
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'user'
    },
    status: {
      type: String,
      enum: ['pending', 'in_progress', 'completed', 'delayed'],
      default: 'pending'
    },
    notes: {
      type: String
    }
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user'
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// تحديث تاريخ التعديل عند تحديث البيانات
OrderSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // حساب المجاميع تلقائياً
  if (this.isModified('items') || this.isNew) {
    // حساب المجموع الفرعي
    this.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
    
    // حساب مبلغ الضريبة
    this.taxAmount = (this.subtotal - this.discount) * (this.taxRate / 100);
    
    // حساب المبلغ الإجمالي
    this.totalAmount = this.subtotal - this.discount + this.taxAmount;
  }
  
  next();
});

// إنشاء رقم الطلب تلقائياً
OrderSchema.pre('save', async function(next) {
  if (this.isNew) {
    try {
      // الحصول على آخر طلب
      const lastOrder = await this.constructor.findOne({}, {}, { sort: { 'orderDate': -1 } });
      
      // إنشاء رقم الطلب الجديد
      const year = new Date().getFullYear().toString().substr(-2);
      const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
      
      if (lastOrder && lastOrder.orderNumber) {
        // استخراج الرقم التسلسلي من آخر طلب
        const lastNumber = parseInt(lastOrder.orderNumber.split('-')[2]);
        this.orderNumber = `ORD-${year}${month}-${(lastNumber + 1).toString().padStart(4, '0')}`;
      } else {
        // إذا لم يكن هناك طلبات سابقة
        this.orderNumber = `ORD-${year}${month}-0001`;
      }
    } catch (err) {
      return next(err);
    }
  }
  next();
});

module.exports = mongoose.model('order', OrderSchema);