const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const userController = require('../controllers/userController');
const auth = require('../middleware/auth');
const checkPermission = require('../middleware/checkPermission');

/**
 * @route   POST api/users
 * @desc    تسجيل مستخدم جديد
 * @access  Private/Admin
 */
router.post(
  '/',
  [
    auth,
    checkPermission('users', 'create'),
    [
      check('name', 'الاسم مطلوب').not().isEmpty(),
      check('email', 'يرجى إدخال بريد إلكتروني صحيح').isEmail(),
      check('password', 'يرجى إدخال كلمة مرور لا تقل عن 6 أحرف').isLength({ min: 6 }),
      check('role', 'الدور مطلوب').not().isEmpty()
    ]
  ],
  userController.registerUser
);

/**
 * @route   GET api/users
 * @desc    الحصول على قائمة المستخدمين
 * @access  Private/Admin
 */
router.get(
  '/',
  [auth, checkPermission('users', 'read')],
  userController.getUsers
);

/**
 * @route   GET api/users/:id
 * @desc    الحصول على مستخدم محدد
 * @access  Private/Admin
 */
router.get(
  '/:id',
  [auth, checkPermission('users', 'read')],
  userController.getUserById
);

/**
 * @route   PUT api/users/:id
 * @desc    تحديث مستخدم
 * @access  Private/Admin
 */
router.put(
  '/:id',
  [
    auth,
    checkPermission('users', 'update'),
    [
      check('name', 'الاسم مطلوب').optional().not().isEmpty(),
      check('email', 'يرجى إدخال بريد إلكتروني صحيح').optional().isEmail(),
      check('role', 'الدور مطلوب').optional().not().isEmpty()
    ]
  ],
  userController.updateUser
);

/**
 * @route   PUT api/users/password/:id
 * @desc    تغيير كلمة مرور مستخدم
 * @access  Private/Admin
 */
router.put(
  '/password/:id',
  [
    auth,
    checkPermission('users', 'update'),
    [
      check('password', 'يرجى إدخال كلمة مرور لا تقل عن 6 أحرف').isLength({ min: 6 })
    ]
  ],
  userController.changePassword
);

/**
 * @route   DELETE api/users/:id
 * @desc    حذف مستخدم
 * @access  Private/Admin
 */
router.delete(
  '/:id',
  [auth, checkPermission('users', 'delete')],
  userController.deleteUser
);

/**
 * @route   GET api/users/me
 * @desc    الحصول على بيانات المستخدم الحالي
 * @access  Private
 */
router.get('/me', auth, userController.getCurrentUser);

module.exports = router;