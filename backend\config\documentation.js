/**
 * ملف إعداد وتكوين التوثيق
 * يقوم بإعداد وتكوين نظام التوثيق للتطبيق
 */

const path = require('path');
const fs = require('fs');
const config = require('config');
const logger = require('../utils/logger');
const { recordError } = require('./monitoring');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

// مسارات التوثيق
const DOCS_DIR = path.join(process.cwd(), 'docs');
const API_DOCS_DIR = path.join(DOCS_DIR, 'api');
const USER_DOCS_DIR = path.join(DOCS_DIR, 'user');
const DEV_DOCS_DIR = path.join(DOCS_DIR, 'dev');

/**
 * إنشاء مسارات التوثيق
 */
const createDocDirectories = () => {
  try {
    // إنشاء المسارات إذا لم تكن موجودة
    if (!fs.existsSync(DOCS_DIR)) {
      fs.mkdirSync(DOCS_DIR, { recursive: true });
      logger.info(`تم إنشاء مسار التوثيق: ${DOCS_DIR}`);
    }
    
    if (!fs.existsSync(API_DOCS_DIR)) {
      fs.mkdirSync(API_DOCS_DIR, { recursive: true });
      logger.info(`تم إنشاء مسار توثيق واجهة برمجة التطبيقات: ${API_DOCS_DIR}`);
    }
    
    if (!fs.existsSync(USER_DOCS_DIR)) {
      fs.mkdirSync(USER_DOCS_DIR, { recursive: true });
      logger.info(`تم إنشاء مسار توثيق المستخدم: ${USER_DOCS_DIR}`);
    }
    
    if (!fs.existsSync(DEV_DOCS_DIR)) {
      fs.mkdirSync(DEV_DOCS_DIR, { recursive: true });
      logger.info(`تم إنشاء مسار توثيق المطور: ${DEV_DOCS_DIR}`);
    }
    
    return true;
  } catch (error) {
    logger.error('فشل في إنشاء مسارات التوثيق', error);
    recordError(error);
    return false;
  }
};

/**
 * إنشاء ملف توثيق
 * @param {string} filePath - مسار الملف
 * @param {string} content - محتوى الملف
 * @param {boolean} overwrite - ما إذا كان يجب استبدال الملف إذا كان موجودًا
 * @returns {boolean} نجاح العملية
 */
const createDocFile = (filePath, content, overwrite = false) => {
  try {
    // التحقق مما إذا كان الملف موجودًا
    const fileExists = fs.existsSync(filePath);
    
    if (fileExists && !overwrite) {
      logger.debug(`ملف التوثيق موجود بالفعل ولن يتم استبداله: ${filePath}`);
      return false;
    }
    
    // إنشاء المسار إذا لم يكن موجودًا
    const dirPath = path.dirname(filePath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    // كتابة المحتوى إلى الملف
    fs.writeFileSync(filePath, content, 'utf8');
    
    logger.info(`تم إنشاء ملف التوثيق: ${filePath}`);
    return true;
  } catch (error) {
    logger.error(`فشل في إنشاء ملف التوثيق: ${filePath}`, error);
    recordError(error);
    return false;
  }
};

/**
 * قراءة ملف توثيق
 * @param {string} filePath - مسار الملف
 * @returns {string|null} محتوى الملف أو null في حالة الفشل
 */
const readDocFile = (filePath) => {
  try {
    // التحقق مما إذا كان الملف موجودًا
    if (!fs.existsSync(filePath)) {
      logger.warn(`ملف التوثيق غير موجود: ${filePath}`);
      return null;
    }
    
    // قراءة محتوى الملف
    const content = fs.readFileSync(filePath, 'utf8');
    
    logger.debug(`تم قراءة ملف التوثيق: ${filePath}`);
    return content;
  } catch (error) {
    logger.error(`فشل في قراءة ملف التوثيق: ${filePath}`, error);
    recordError(error);
    return null;
  }
};

/**
 * إنشاء ملفات التوثيق الافتراضية
 * @returns {boolean} نجاح العملية
 */
const createDefaultDocs = () => {
  try {
    // إنشاء مسارات التوثيق
    createDocDirectories();
    
    // إنشاء ملف README.md الرئيسي
    const readmeContent = `# ${appConfig.name || 'نظام المطبعة'}

## نظرة عامة

هذا التوثيق يغطي جميع جوانب نظام المطبعة، بما في ذلك واجهة برمجة التطبيقات (API)، ودليل المستخدم، ودليل المطور.

## محتويات التوثيق

- [توثيق واجهة برمجة التطبيقات (API)](./api/README.md)
- [دليل المستخدم](./user/README.md)
- [دليل المطور](./dev/README.md)

## بدء الاستخدام

يرجى الرجوع إلى الأدلة المناسبة بناءً على دورك:

- إذا كنت مستخدمًا نهائيًا، راجع [دليل المستخدم](./user/README.md).
- إذا كنت مطورًا، راجع [دليل المطور](./dev/README.md).
- إذا كنت تتكامل مع واجهة برمجة التطبيقات الخاصة بنا، راجع [توثيق واجهة برمجة التطبيقات](./api/README.md).

## الدعم

للحصول على المساعدة، يرجى الاتصال بفريق الدعم على [<EMAIL>](mailto:<EMAIL>).
`;
    
    createDocFile(path.join(DOCS_DIR, 'README.md'), readmeContent);
    
    // إنشاء ملف README.md لتوثيق واجهة برمجة التطبيقات
    const apiReadmeContent = `# توثيق واجهة برمجة التطبيقات (API)

## نظرة عامة

توفر واجهة برمجة التطبيقات (API) الخاصة بنا وصولاً برمجيًا إلى وظائف نظام المطبعة. يستخدم API معيار REST ويستجيب بتنسيق JSON.

## الوصول إلى API

يمكن الوصول إلى API من خلال النقطة النهائية التالية:

\`\`\`
${appConfig.baseUrl || 'http://localhost:3000'}/api
\`\`\`

## المصادقة

تتطلب معظم نقاط النهاية مصادقة. نستخدم مصادقة JWT (JSON Web Token). للحصول على رمز مميز، يجب عليك تسجيل الدخول باستخدام بيانات اعتماد صالحة.

### تسجيل الدخول

\`\`\`http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your_password"
}
\`\`\`

### استخدام الرمز المميز

بمجرد حصولك على الرمز المميز، يجب تضمينه في رأس التفويض لجميع الطلبات اللاحقة:

\`\`\`http
GET /api/resource
Authorization: Bearer your_token_here
\`\`\`

## الموارد

- [المستخدمين](./users.md)
- [العملاء](./customers.md)
- [الطلبات](./orders.md)
- [المنتجات](./products.md)
- [الفواتير](./invoices.md)
- [المدفوعات](./payments.md)
- [الموردين](./suppliers.md)
- [التقارير](./reports.md)

## أكواد الحالة

- \`200 OK\`: تم تنفيذ الطلب بنجاح
- \`201 Created\`: تم إنشاء المورد بنجاح
- \`400 Bad Request\`: طلب غير صالح
- \`401 Unauthorized\`: المصادقة مطلوبة
- \`403 Forbidden\`: ليس لديك إذن للوصول إلى هذا المورد
- \`404 Not Found\`: المورد غير موجود
- \`500 Internal Server Error\`: حدث خطأ في الخادم

## التوثيق التفاعلي

يمكنك استكشاف API بشكل تفاعلي باستخدام Swagger UI المتاح على:

\`\`\`
${appConfig.baseUrl || 'http://localhost:3000'}/api-docs
\`\`\`
`;
    
    createDocFile(path.join(API_DOCS_DIR, 'README.md'), apiReadmeContent);
    
    // إنشاء ملف README.md لدليل المستخدم
    const userReadmeContent = `# دليل المستخدم

## نظرة عامة

مرحبًا بك في دليل المستخدم لنظام المطبعة. يوفر هذا الدليل معلومات شاملة حول كيفية استخدام النظام بفعالية.

## المحتويات

- [بدء الاستخدام](./getting-started.md)
- [لوحة التحكم](./dashboard.md)
- [إدارة المستخدمين](./user-management.md)
- [إدارة العملاء](./customer-management.md)
- [إدارة الطلبات](./order-management.md)
- [إدارة المنتجات والمخزون](./product-inventory-management.md)
- [إدارة الفواتير والمدفوعات](./invoice-payment-management.md)
- [إدارة الموردين](./supplier-management.md)
- [التقارير والتحليلات](./reports-analytics.md)
- [إعدادات النظام](./system-settings.md)
- [استكشاف الأخطاء وإصلاحها](./troubleshooting.md)

## بدء الاستخدام

لبدء استخدام النظام، يرجى الاطلاع على [دليل بدء الاستخدام](./getting-started.md).

## الدعم

إذا واجهت أي مشكلات أو كانت لديك أسئلة، يرجى الاتصال بفريق الدعم على [<EMAIL>](mailto:<EMAIL>).
`;
    
    createDocFile(path.join(USER_DOCS_DIR, 'README.md'), userReadmeContent);
    
    // إنشاء ملف README.md لدليل المطور
    const devReadmeContent = `# دليل المطور

## نظرة عامة

مرحبًا بك في دليل المطور لنظام المطبعة. يوفر هذا الدليل معلومات تقنية شاملة للمطورين الذين يعملون على النظام أو يتكاملون معه.

## المحتويات

- [بنية النظام](./architecture.md)
- [إعداد بيئة التطوير](./development-setup.md)
- [قاعدة البيانات](./database.md)
- [واجهة برمجة التطبيقات (API)](./api.md)
- [المصادقة والتفويض](./authentication-authorization.md)
- [معالجة الأخطاء](./error-handling.md)
- [التسجيل والمراقبة](./logging-monitoring.md)
- [الاختبار](./testing.md)
- [النشر](./deployment.md)
- [أفضل الممارسات](./best-practices.md)
- [المساهمة](./contributing.md)

## بدء التطوير

لبدء التطوير، يرجى الاطلاع على [دليل إعداد بيئة التطوير](./development-setup.md).

## المساهمة

نرحب بالمساهمات من المجتمع. يرجى قراءة [دليل المساهمة](./contributing.md) للحصول على معلومات حول كيفية المساهمة في المشروع.

## الدعم

إذا كانت لديك أسئلة تقنية، يرجى الاتصال بفريق التطوير على [<EMAIL>](mailto:<EMAIL>).
`;
    
    createDocFile(path.join(DEV_DOCS_DIR, 'README.md'), devReadmeContent);
    
    // إنشاء ملفات توثيق إضافية
    createDocFile(path.join(USER_DOCS_DIR, 'getting-started.md'), `# بدء الاستخدام

## تسجيل الدخول

للوصول إلى النظام، يجب عليك تسجيل الدخول باستخدام بيانات الاعتماد الخاصة بك. إذا لم يكن لديك حساب، يرجى الاتصال بمسؤول النظام.

1. انتقل إلى صفحة تسجيل الدخول: ${appConfig.frontendUrl || 'http://localhost:3000'}
2. أدخل عنوان البريد الإلكتروني وكلمة المرور الخاصين بك
3. انقر على زر "تسجيل الدخول"

## لوحة التحكم

بعد تسجيل الدخول، ستتم إعادة توجيهك إلى لوحة التحكم. توفر لوحة التحكم نظرة عامة على المعلومات المهمة، مثل:

- الطلبات الحديثة
- حالة المخزون
- الفواتير المستحقة
- إحصائيات المبيعات

## التنقل في النظام

يمكنك التنقل في النظام باستخدام القائمة الجانبية، والتي تحتوي على الأقسام التالية:

- لوحة التحكم
- العملاء
- الطلبات
- المنتجات
- المخزون
- الفواتير
- المدفوعات
- الموردين
- التقارير
- المستخدمين
- الإعدادات

## تغيير كلمة المرور

لتغيير كلمة المرور الخاصة بك:

1. انقر على اسم المستخدم الخاص بك في الزاوية العلوية اليمنى
2. حدد "الملف الشخصي"
3. انقر على "تغيير كلمة المرور"
4. أدخل كلمة المرور الحالية وكلمة المرور الجديدة
5. انقر على "حفظ"

## تسجيل الخروج

لتسجيل الخروج من النظام:

1. انقر على اسم المستخدم الخاص بك في الزاوية العلوية اليمنى
2. حدد "تسجيل الخروج"
`);
    
    createDocFile(path.join(DEV_DOCS_DIR, 'architecture.md'), `# بنية النظام

## نظرة عامة

يستخدم نظام المطبعة بنية تطبيق ويب حديثة مع فصل واضح بين الواجهة الخلفية والواجهة الأمامية.

## المكونات الرئيسية

### الواجهة الخلفية (Backend)

- **لغة البرمجة**: Node.js
- **إطار العمل**: Express.js
- **قاعدة البيانات**: MongoDB
- **المصادقة**: JWT (JSON Web Tokens)

### الواجهة الأمامية (Frontend)

- **إطار العمل**: React.js
- **إدارة الحالة**: Redux
- **التوجيه**: React Router
- **واجهة المستخدم**: Material-UI

## بنية المشروع

\`\`\`
/
├── backend/                # مصدر الواجهة الخلفية
│   ├── config/            # ملفات التكوين
│   ├── controllers/       # وحدات التحكم في API
│   ├── middleware/        # وسطاء Express
│   ├── models/            # نماذج Mongoose
│   ├── routes/            # مسارات API
│   ├── services/          # خدمات منطق الأعمال
│   ├── utils/             # أدوات مساعدة
│   ├── app.js             # تطبيق Express الرئيسي
│   └── server.js          # نقطة الدخول
│
├── frontend/              # مصدر الواجهة الأمامية
│   ├── public/            # الملفات الثابتة
│   ├── src/               # مصدر React
│   │   ├── components/    # مكونات React
│   │   ├── pages/         # صفحات التطبيق
│   │   ├── redux/         # منطق Redux
│   │   ├── services/      # خدمات API
│   │   ├── utils/         # أدوات مساعدة
│   │   ├── App.js         # مكون التطبيق الرئيسي
│   │   └── index.js       # نقطة الدخول
│   └── package.json       # تبعيات الواجهة الأمامية
│
├── docs/                  # التوثيق
├── tests/                 # اختبارات
└── package.json           # تبعيات المشروع الرئيسية
\`\`\`

## تدفق البيانات

1. يرسل المستخدم طلبًا من الواجهة الأمامية
2. يتم توجيه الطلب إلى نقطة نهاية API المناسبة في الواجهة الخلفية
3. تتحقق الوسطاء من المصادقة والتفويض
4. يعالج وحدة التحكم الطلب، ويتفاعل مع الخدمات ونماذج قاعدة البيانات
5. تعيد الواجهة الخلفية استجابة JSON
6. تعالج الواجهة الأمامية البيانات وتحدث واجهة المستخدم

## نمط التصميم

يتبع النظام نمط تصميم MVC (النموذج-العرض-وحدة التحكم) مع بعض العناصر من البنية الموجهة نحو الخدمة:

- **النماذج (Models)**: تمثل هياكل البيانات وتتفاعل مع قاعدة البيانات
- **العروض (Views)**: تُنفذ في الواجهة الأمامية باستخدام React
- **وحدات التحكم (Controllers)**: تعالج طلبات API وتنسق بين النماذج والعروض
- **الخدمات (Services)**: تحتوي على منطق الأعمال المعقد

## مخطط النظام

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  Client        |<---->|  API Server    |<---->|  Database      |
|  (React)       |      |  (Express)     |      |  (MongoDB)     |
|                |      |                |      |                |
+----------------+      +----------------+      +----------------+
                               ^   ^
                               |   |
                 +-------------+   +-------------+
                 |                               |
        +----------------+               +----------------+
        |                |               |                |
        |  File Storage  |               |  External APIs |
        |                |               |                |
        +----------------+               +----------------+
```
`);
    
    logger.info('تم إنشاء ملفات التوثيق الافتراضية');
    return true;
  } catch (error) {
    logger.error('فشل في إنشاء ملفات التوثيق الافتراضية', error);
    recordError(error);
    return false;
  }
};

/**
 * إنشاء توثيق API من مخطط Swagger
 * @param {Object} swaggerSpec - مخطط Swagger
 * @returns {boolean} نجاح العملية
 */
const generateApiDocsFromSwagger = (swaggerSpec) => {
  try {
    if (!swaggerSpec || !swaggerSpec.paths) {
      logger.error('مخطط Swagger غير صالح');
      return false;
    }
    
    // إنشاء مسارات التوثيق
    createDocDirectories();
    
    // إنشاء ملفات توثيق لكل مجموعة من نقاط النهاية
    const endpointGroups = {};
    
    // تجميع نقاط النهاية حسب العلامة
    Object.entries(swaggerSpec.paths).forEach(([path, methods]) => {
      Object.entries(methods).forEach(([method, endpoint]) => {
        if (!endpoint.tags || endpoint.tags.length === 0) {
          return;
        }
        
        const tag = endpoint.tags[0];
        
        if (!endpointGroups[tag]) {
          endpointGroups[tag] = [];
        }
        
        endpointGroups[tag].push({
          path,
          method: method.toUpperCase(),
          summary: endpoint.summary || '',
          description: endpoint.description || '',
          parameters: endpoint.parameters || [],
          requestBody: endpoint.requestBody,
          responses: endpoint.responses || {},
        });
      });
    });
    
    // إنشاء ملف توثيق لكل مجموعة
    Object.entries(endpointGroups).forEach(([tag, endpoints]) => {
      const fileName = `${tag.toLowerCase().replace(/\s+/g, '-')}.md`;
      const filePath = path.join(API_DOCS_DIR, fileName);
      
      let content = `# ${tag}

## نظرة عامة

هذا المستند يوثق نقاط نهاية API المتعلقة بـ ${tag}.

## نقاط النهاية

`;
      
      endpoints.forEach((endpoint) => {
        content += `### ${endpoint.summary}

\`\`\`http
${endpoint.method} ${endpoint.path}
\`\`\`

${endpoint.description}

`;
        
        if (endpoint.parameters && endpoint.parameters.length > 0) {
          content += `#### المعلمات

| الاسم | الموقع | النوع | الوصف | مطلوب |
|------|--------|------|---------|--------|
`;
          
          endpoint.parameters.forEach((param) => {
            content += `| ${param.name} | ${param.in} | ${param.schema?.type || 'object'} | ${param.description || ''} | ${param.required ? 'نعم' : 'لا'} |
`;
          });
          
          content += '\n';
        }
        
        if (endpoint.requestBody) {
          content += `#### هيكل الطلب

\`\`\`json
${JSON.stringify(endpoint.requestBody.content?.['application/json']?.schema?.example || {}, null, 2)}
\`\`\`

`;
        }
        
        content += `#### الاستجابات

`;
        
        Object.entries(endpoint.responses).forEach(([code, response]) => {
          content += `**${code}**: ${response.description || ''}\n\n`;
          
          if (response.content?.['application/json']?.schema?.example) {
            content += `\`\`\`json
${JSON.stringify(response.content['application/json'].schema.example, null, 2)}
\`\`\`\n\n`;
          }
        });
        
        content += '---\n\n';
      });
      
      createDocFile(filePath, content);
    });
    
    logger.info('تم إنشاء توثيق API من مخطط Swagger');
    return true;
  } catch (error) {
    logger.error('فشل في إنشاء توثيق API من مخطط Swagger', error);
    recordError(error);
    return false;
  }
};

/**
 * إعداد نظام التوثيق
 */
const setupDocumentation = () => {
  try {
    // إنشاء مسارات التوثيق
    createDocDirectories();
    
    // إنشاء ملفات التوثيق الافتراضية
    createDefaultDocs();
    
    logger.info('تم إعداد نظام التوثيق بنجاح');
    return true;
  } catch (error) {
    logger.error('فشل في إعداد نظام التوثيق', error);
    recordError(error);
    return false;
  }
};

module.exports = {
  // الثوابت
  DOCS_DIR,
  API_DOCS_DIR,
  USER_DOCS_DIR,
  DEV_DOCS_DIR,
  
  // وظائف إدارة المسارات
  createDocDirectories,
  
  // وظائف إدارة الملفات
  createDocFile,
  readDocFile,
  
  // وظائف إنشاء التوثيق
  createDefaultDocs,
  generateApiDocsFromSwagger,
  
  // وظائف الإعداد
  setupDocumentation,
};