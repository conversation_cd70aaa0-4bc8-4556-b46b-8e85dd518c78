/**
 * ملف الإعدادات للبيئة التطويرية
 * يتم دمج هذه الإعدادات مع الإعدادات الافتراضية عند تشغيل التطبيق في بيئة التطوير
 */

module.exports = {
  // إعدادات التطبيق الأساسية
  app: {
    baseUrl: 'http://localhost:5000',
    frontendUrl: 'http://localhost:3000',
    port: process.env.PORT || 5000,
    env: 'development',
  },

  // إعدادات قاعدة البيانات
  db: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/print-system-dev',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      autoIndex: true, // تمكين إنشاء الفهارس تلقائيًا في التطوير
    },
  },

  // إعدادات المصادقة والأمان
  auth: {
    jwtSecret: 'dev-jwt-secret-key',
    refreshTokenSecret: 'dev-refresh-token-secret-key',
    cookieSecure: false, // السماح بملفات تعريف الارتباط غير الآمنة في التطوير
  },

  // إعدادات البريد الإلكتروني
  email: {
    // استخدام Ethereal للاختبار في بيئة التطوير
    // يمكن إعداده تلقائيًا في ملف التهيئة
    enabled: process.env.EMAIL_ENABLED === 'true',
    host: process.env.EMAIL_HOST || 'smtp.ethereal.email',
    port: process.env.EMAIL_PORT || 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  },

  // إعدادات التسجيل والمراقبة
  logging: {
    level: process.env.LOG_LEVEL || 'debug',
    colorize: true,
  },

  // إعدادات CORS
  cors: {
    origin: '*', // السماح بالوصول من أي مصدر في بيئة التطوير
  },

  // إعدادات الأمان
  security: {
    rateLimiter: {
      enabled: false, // تعطيل محدد معدل الطلبات في التطوير
    },
    helmet: {
      enabled: true,
    },
    xss: {
      enabled: true,
    },
    csrf: {
      enabled: false, // تعطيل حماية CSRF في التطوير
    },
  },

  // إعدادات المستخدمين
  users: {
    emailVerificationRequired: false, // تعطيل التحقق من البريد الإلكتروني في التطوير
  },
};