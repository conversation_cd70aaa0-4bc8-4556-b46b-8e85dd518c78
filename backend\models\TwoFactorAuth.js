const mongoose = require('mongoose');
const crypto = require('crypto');

/**
 * نموذج المصادقة الثنائية
 */
const TwoFactorAuthSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  phone: {
    type: String,
    required: true,
    trim: true
  },
  
  code: {
    type: String,
    required: true
  },
  
  hashedCode: {
    type: String,
    required: true
  },
  
  type: {
    type: String,
    enum: ['login', 'password_reset', 'phone_verification', 'sensitive_action'],
    default: 'login'
  },
  
  attempts: {
    type: Number,
    default: 0,
    max: 5
  },
  
  isUsed: {
    type: Boolean,
    default: false
  },
  
  isExpired: {
    type: Boolean,
    default: false
  },
  
  expiresAt: {
    type: Date,
    required: true,
    default: () => new Date(Date.now() + 10 * 60 * 1000) // 10 دقائق
  },
  
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  usedAt: {
    type: Date
  },
  
  ipAddress: {
    type: String
  },
  
  userAgent: {
    type: String
  },
  
  sessionId: {
    type: String
  },
  
  metadata: {
    type: mongoose.Schema.Types.Mixed
  }
  
}, {
  timestamps: true
});

// فهرسة المستخدم
TwoFactorAuthSchema.index({ userId: 1 });

// فهرسة رقم الهاتف
TwoFactorAuthSchema.index({ phone: 1 });

// فهرسة النوع
TwoFactorAuthSchema.index({ type: 1 });

// فهرسة تاريخ الانتهاء
TwoFactorAuthSchema.index({ expiresAt: 1 });

// فهرسة مركبة للاستعلامات
TwoFactorAuthSchema.index({ userId: 1, type: 1, isUsed: 1, expiresAt: 1 });

// إنشاء رمز التحقق
TwoFactorAuthSchema.statics.generateCode = function() {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// تشفير الرمز
TwoFactorAuthSchema.statics.hashCode = function(code) {
  return crypto.createHash('sha256').update(code).digest('hex');
};

// إنشاء رمز تحقق جديد
TwoFactorAuthSchema.statics.createVerificationCode = async function(userId, phone, type = 'login', metadata = {}) {
  // إلغاء الرموز السابقة غير المستخدمة
  await this.updateMany(
    { userId, type, isUsed: false },
    { isExpired: true }
  );
  
  const code = this.generateCode();
  const hashedCode = this.hashCode(code);
  
  const verification = await this.create({
    userId,
    phone,
    code,
    hashedCode,
    type,
    metadata
  });
  
  return { verification, code };
};

// التحقق من الرمز
TwoFactorAuthSchema.methods.verifyCode = function(inputCode) {
  // التحقق من انتهاء الصلاحية
  if (this.expiresAt < new Date()) {
    this.isExpired = true;
    this.save();
    return { success: false, message: 'انتهت صلاحية رمز التحقق' };
  }
  
  // التحقق من الاستخدام السابق
  if (this.isUsed) {
    return { success: false, message: 'تم استخدام رمز التحقق مسبقاً' };
  }
  
  // التحقق من عدد المحاولات
  if (this.attempts >= 5) {
    this.isExpired = true;
    this.save();
    return { success: false, message: 'تم تجاوز الحد الأقصى للمحاولات' };
  }
  
  // زيادة عدد المحاولات
  this.attempts += 1;
  
  // التحقق من الرمز
  const hashedInput = this.constructor.hashCode(inputCode);
  
  if (hashedInput === this.hashedCode) {
    this.isUsed = true;
    this.usedAt = new Date();
    this.save();
    return { success: true, message: 'تم التحقق بنجاح' };
  } else {
    this.save();
    return { 
      success: false, 
      message: `رمز التحقق غير صحيح. المحاولات المتبقية: ${5 - this.attempts}` 
    };
  }
};

// تنظيف الرموز المنتهية الصلاحية
TwoFactorAuthSchema.statics.cleanupExpired = async function() {
  const result = await this.deleteMany({
    $or: [
      { expiresAt: { $lt: new Date() } },
      { isExpired: true },
      { createdAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } } // أقدم من 24 ساعة
    ]
  });
  
  console.log(`تم حذف ${result.deletedCount} رمز تحقق منتهي الصلاحية`);
  return result;
};

// مهمة تنظيف تلقائية كل ساعة
setInterval(async () => {
  try {
    await mongoose.model('TwoFactorAuth').cleanupExpired();
  } catch (error) {
    console.error('خطأ في تنظيف رموز التحقق:', error);
  }
}, 60 * 60 * 1000); // كل ساعة

module.exports = mongoose.model('TwoFactorAuth', TwoFactorAuthSchema);
