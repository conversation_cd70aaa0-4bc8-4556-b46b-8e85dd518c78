const express = require('express');
const { connectDB } = require('./config/db');
const path = require('path');
const config = require('config');
const cors = require('cors');
const morgan = require('morgan');
const fileUpload = require('express-fileupload');
const fs = require('fs');

// التحقق من تفعيل نظام المهام
const jobsEnabled = process.env.JOBS_ENABLED === 'true';
let jobsSystem = null;

// استدعاء نظام المهام فقط إذا كان مفعلاً
if (jobsEnabled) {
  try {
    jobsSystem = require('./config/jobs');
    console.log('تم تحميل نظام المهام بنجاح');
  } catch (error) {
    console.error('فشل في تحميل نظام المهام:', error.message);
  }
}

// إنشاء تطبيق Express
const app = express();

// اتصال بقاعدة البيانات
connectDB();

// إعداد الوسائط (Middleware)
app.use(express.json({ extended: false }));
app.use(cors());

// تكوين Morgan للتسجيل
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  // إنشاء مجلد السجلات إذا لم يكن موجودًا
  const logDirectory = path.join(__dirname, 'logs');
  if (!fs.existsSync(logDirectory)) {
    fs.mkdirSync(logDirectory);
  }

  // إنشاء تدفق كتابة للسجلات
  const accessLogStream = fs.createWriteStream(
    path.join(logDirectory, 'access.log'),
    { flags: 'a' }
  );

  // استخدام تنسيق مشترك للسجلات
  app.use(morgan('combined', { stream: accessLogStream }));
}

// إعداد تحميل الملفات
app.use(
  fileUpload({
    limits: { fileSize: config.get('maxFileSize') },
    createParentPath: true,
    useTempFiles: true,
    tempFileDir: '/tmp/',
    debug: process.env.NODE_ENV === 'development'
  })
);

// إنشاء مجلد التحميلات إذا لم يكن موجودًا
const uploadDir = config.get('fileUploadPath');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// تعريف المسارات الثابتة
app.use('/uploads', express.static(path.join(__dirname, uploadDir)));

// تعريف مسارات API
app.use('/api/auth', require('./routes/authRoutes'));
app.use('/api/users', require('./routes/userRoutes'));
app.use('/api/customers', require('./routes/customerRoutes'));
app.use('/api/orders', require('./routes/orderRoutes'));
app.use('/api/inventory', require('./routes/inventoryRoutes'));
app.use('/api/invoices', require('./routes/invoiceRoutes'));
app.use('/api/suppliers', require('./routes/supplierRoutes'));
app.use('/api/reports', require('./routes/reportRoutes'));
app.use('/api/financials', require('./routes/financialsRoutes'));

// خدمة الملفات الثابتة في بيئة الإنتاج
if (process.env.NODE_ENV === 'production') {
  // تعيين المجلد الثابت
  app.use(express.static('client/build'));

  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, 'client', 'build', 'index.html'));
  });
} else {
  // المسار الافتراضي في بيئة التطوير
  app.get('/', (req, res) => {
    res.json({ message: 'مرحبًا بك في واجهة API لنظام إدارة المطبعة' });
  });
}

// تحديد المنفذ والاستماع للطلبات
const PORT = process.env.PORT || config.get('port');
app.listen(PORT, () => console.log(`تم تشغيل الخادم على المنفذ ${PORT}`));