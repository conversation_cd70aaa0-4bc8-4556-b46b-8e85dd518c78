require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
const fs = require('fs');

// استيراد الإعدادات والاتصال بقاعدة البيانات
const { connectDB } = require('./config/database');
const config = require('./config/config');

// استيراد المسارات
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const customerRoutes = require('./routes/customers');
const orderRoutes = require('./routes/orders');
const inventoryRoutes = require('./routes/inventory');
const invoiceRoutes = require('./routes/invoices');
const reportRoutes = require('./routes/reports');
const hrRoutes = require('./routes/hr');

// إنشاء تطبيق Express
const app = express();

// اتصال بقاعدة البيانات
connectDB();

// إعداد الأمان
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
    },
  },
}));

// إعداد معدل الطلبات
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100, // حد أقصى 100 طلب لكل IP
  message: {
    error: 'تم تجاوز الحد الأقصى للطلبات، يرجى المحاولة لاحقاً'
  }
});
app.use('/api/', limiter);

// إعداد CORS
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

// إعداد الوسائط (Middleware)
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// تكوين Morgan للتسجيل
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  // إنشاء مجلد السجلات إذا لم يكن موجودًا
  const logDirectory = path.join(__dirname, 'logs');
  if (!fs.existsSync(logDirectory)) {
    fs.mkdirSync(logDirectory, { recursive: true });
  }

  // إنشاء تدفق كتابة للسجلات
  const accessLogStream = fs.createWriteStream(
    path.join(logDirectory, 'access.log'),
    { flags: 'a' }
  );

  // استخدام تنسيق مشترك للسجلات
  app.use(morgan('combined', { stream: accessLogStream }));
}

// إنشاء مجلد التحميلات إذا لم يكن موجودًا
const uploadDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// تعريف المسارات الثابتة
app.use('/uploads', express.static(uploadDir));

// تعريف مسارات API
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/inventory', inventoryRoutes);
app.use('/api/invoices', invoiceRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/hr', hrRoutes);

// مسار الصحة العامة
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'الخادم يعمل بشكل طبيعي',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

// المسار الافتراضي
app.get('/', (req, res) => {
  res.json({
    message: 'مرحبًا بك في نظام إدارة المطبعة المتطور',
    version: '2.0.0',
    documentation: '/api/docs',
    health: '/health'
  });
});

// معالج الأخطاء العام
app.use((err, req, res, next) => {
  console.error('خطأ في الخادم:', err.stack);
  res.status(500).json({
    success: false,
    message: 'خطأ داخلي في الخادم',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// معالج المسارات غير الموجودة
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار المطلوب غير موجود'
  });
});

// تحديد المنفذ والاستماع للطلبات
const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log(`🚀 تم تشغيل الخادم على المنفذ ${PORT}`);
  console.log(`🌍 البيئة: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📊 لوحة التحكم: http://localhost:${PORT}`);
});

// معالجة إغلاق الخادم بشكل صحيح
process.on('SIGTERM', () => {
  console.log('🛑 تم استلام إشارة SIGTERM، جاري إغلاق الخادم...');
  server.close(() => {
    console.log('✅ تم إغلاق الخادم بنجاح');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 تم استلام إشارة SIGINT، جاري إغلاق الخادم...');
  server.close(() => {
    console.log('✅ تم إغلاق الخادم بنجاح');
    process.exit(0);
  });
});