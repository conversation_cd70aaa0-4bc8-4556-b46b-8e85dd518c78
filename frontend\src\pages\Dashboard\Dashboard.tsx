import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  IconButton,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  LinearProgress,
  Button
} from '@mui/material';
import {
  TrendingUp,
  People,
  Assignment,
  Inventory,
  Receipt,
  Notifications,
  Print,
  Schedule,
  AttachMoney,
  Warning,
  CheckCircle,
  AccessTime
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';

// مكون بطاقة الإحصائيات
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, trend }) => (
  <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="h6">
            {title}
          </Typography>
          <Typography variant="h4" component="h2" sx={{ fontWeight: 600, color }}>
            {value}
          </Typography>
          {trend && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <TrendingUp 
                sx={{ 
                  fontSize: 16, 
                  color: trend.isPositive ? 'success.main' : 'error.main',
                  transform: trend.isPositive ? 'none' : 'rotate(180deg)'
                }} 
              />
              <Typography 
                variant="body2" 
                sx={{ 
                  ml: 0.5, 
                  color: trend.isPositive ? 'success.main' : 'error.main' 
                }}
              >
                {trend.value}%
              </Typography>
            </Box>
          )}
        </Box>
        <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
          {icon}
        </Avatar>
      </Box>
    </CardContent>
  </Card>
);

// مكون قائمة الأنشطة الحديثة
interface Activity {
  id: string;
  type: 'order' | 'payment' | 'inventory' | 'employee';
  title: string;
  description: string;
  time: string;
  status: 'success' | 'warning' | 'error' | 'info';
}

const ActivityItem: React.FC<{ activity: Activity }> = ({ activity }) => {
  const getIcon = () => {
    switch (activity.type) {
      case 'order': return <Assignment />;
      case 'payment': return <AttachMoney />;
      case 'inventory': return <Inventory />;
      case 'employee': return <People />;
      default: return <Notifications />;
    }
  };

  const getColor = () => {
    switch (activity.status) {
      case 'success': return 'success.main';
      case 'warning': return 'warning.main';
      case 'error': return 'error.main';
      default: return 'info.main';
    }
  };

  return (
    <ListItem>
      <ListItemAvatar>
        <Avatar sx={{ bgcolor: getColor() }}>
          {getIcon()}
        </Avatar>
      </ListItemAvatar>
      <ListItemText
        primary={activity.title}
        secondary={
          <Box>
            <Typography variant="body2" color="text.secondary">
              {activity.description}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {activity.time}
            </Typography>
          </Box>
        }
      />
    </ListItem>
  );
};

const Dashboard: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const [stats, setStats] = useState({
    totalOrders: 156,
    totalCustomers: 89,
    totalRevenue: 45600,
    pendingOrders: 12,
    completedOrders: 144,
    inventoryItems: 234,
    lowStockItems: 8,
    employees: 15
  });

  const [recentActivities] = useState<Activity[]>([
    {
      id: '1',
      type: 'order',
      title: 'طلب جديد من شركة الأمل',
      description: 'طباعة 1000 بروشور ملون',
      time: 'منذ 5 دقائق',
      status: 'info'
    },
    {
      id: '2',
      type: 'payment',
      title: 'تم استلام دفعة',
      description: 'دفعة بقيمة 2,500 ريال من العميل أحمد محمد',
      time: 'منذ 15 دقيقة',
      status: 'success'
    },
    {
      id: '3',
      type: 'inventory',
      title: 'تنبيه مخزون منخفض',
      description: 'ورق A4 أبيض - الكمية المتبقية: 50 رزمة',
      time: 'منذ 30 دقيقة',
      status: 'warning'
    },
    {
      id: '4',
      type: 'employee',
      title: 'طلب إجازة جديد',
      description: 'سارة أحمد - إجازة سنوية لمدة 5 أيام',
      time: 'منذ ساعة',
      status: 'info'
    }
  ]);

  const [quickStats] = useState([
    { label: 'الطلبات اليوم', value: 8, color: 'primary.main' },
    { label: 'الطلبات المكتملة', value: 6, color: 'success.main' },
    { label: 'الطلبات المعلقة', value: 2, color: 'warning.main' },
    { label: 'الطلبات المتأخرة', value: 1, color: 'error.main' }
  ]);

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* ترحيب */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
          مرحباً، {user?.name} 👋
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          إليك نظرة عامة على أداء المطبعة اليوم
        </Typography>
      </Box>

      {/* بطاقات الإحصائيات الرئيسية */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الطلبات"
            value={stats.totalOrders}
            icon={<Assignment />}
            color="primary.main"
            trend={{ value: 12, isPositive: true }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="العملاء"
            value={stats.totalCustomers}
            icon={<People />}
            color="success.main"
            trend={{ value: 8, isPositive: true }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="الإيرادات (ريال)"
            value={stats.totalRevenue.toLocaleString('ar-SA')}
            icon={<AttachMoney />}
            color="info.main"
            trend={{ value: 15, isPositive: true }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="المخزون"
            value={stats.inventoryItems}
            icon={<Inventory />}
            color="warning.main"
            trend={{ value: 3, isPositive: false }}
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* الإحصائيات السريعة */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <Schedule sx={{ mr: 1 }} />
                إحصائيات اليوم
              </Typography>
              <Box sx={{ mt: 2 }}>
                {quickStats.map((stat, index) => (
                  <Box key={index} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">{stat.label}</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {stat.value}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={(stat.value / 10) * 100}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: stat.color,
                          borderRadius: 3
                        }
                      }}
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* الأنشطة الحديثة */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                  <Notifications sx={{ mr: 1 }} />
                  الأنشطة الحديثة
                </Typography>
                <Button size="small" variant="outlined">
                  عرض الكل
                </Button>
              </Box>
              <List>
                {recentActivities.map((activity) => (
                  <ActivityItem key={activity.id} activity={activity} />
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* التنبيهات المهمة */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <Warning sx={{ mr: 1, color: 'warning.main' }} />
                تنبيهات مهمة
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
                  <Warning sx={{ color: 'warning.main', mr: 2 }} />
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      مخزون منخفض
                    </Typography>
                    <Typography variant="caption">
                      {stats.lowStockItems} عناصر تحتاج إلى إعادة تموين
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
                  <AccessTime sx={{ color: 'error.main', mr: 2 }} />
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      طلبات متأخرة
                    </Typography>
                    <Typography variant="caption">
                      3 طلبات تجاوزت الموعد المحدد
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* الإجراءات السريعة */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                إجراءات سريعة
              </Typography>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<Assignment />}
                    sx={{ py: 1.5 }}
                  >
                    طلب جديد
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<People />}
                    sx={{ py: 1.5 }}
                  >
                    عميل جديد
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<Receipt />}
                    sx={{ py: 1.5 }}
                  >
                    فاتورة جديدة
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<Inventory />}
                    sx={{ py: 1.5 }}
                  >
                    إدارة المخزون
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
