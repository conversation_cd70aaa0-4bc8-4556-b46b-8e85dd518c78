const express = require('express');
const router = express.Router();
const reportController = require('../controllers/reportController');
const auth = require('../middleware/auth');
const checkPermission = require('../middleware/checkPermission');

/**
 * @route   GET api/reports/sales
 * @desc    الحصول على تقرير المبيعات
 * @access  Private
 */
router.get(
  '/sales',
  [auth, checkPermission('reports', 'read')],
  reportController.getSalesReport
);

/**
 * @route   GET api/reports/financial
 * @desc    الحصول على التقرير المالي
 * @access  Private
 */
router.get(
  '/financial',
  [auth, checkPermission('reports', 'read')],
  reportController.getFinancialReport
);

/**
 * @route   GET api/reports/inventory
 * @desc    الحصول على تقرير المخزون
 * @access  Private
 */
router.get(
  '/inventory',
  [auth, checkPermission('reports', 'read')],
  reportController.getInventoryReport
);

/**
 * @route   GET api/reports/production
 * @desc    الحصول على تقرير الإنتاج
 * @access  Private
 */
router.get(
  '/production',
  [auth, checkPermission('reports', 'read')],
  reportController.getProductionReport
);

/**
 * @route   GET api/reports/customers
 * @desc    الحصول على تقرير العملاء
 * @access  Private
 */
router.get(
  '/customers',
  [auth, checkPermission('reports', 'read')],
  reportController.getCustomerReport
);

module.exports = router;