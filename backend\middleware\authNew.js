const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../config/config');

/**
 * Middleware للتحقق من المصادقة
 */
const auth = async (req, res, next) => {
  try {
    // الحصول على الرمز المميز من الهيدر
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'لا يوجد رمز مصادقة، الوصول مرفوض'
      });
    }

    const token = authHeader.substring(7); // إزالة "Bearer "

    // التحقق من صحة الرمز
    const decoded = jwt.verify(token, config.jwt.secret);

    // البحث عن المستخدم
    const user = await User.findById(decoded.id);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // التحقق من حالة المستخدم
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'تم إيقاف هذا الحساب'
      });
    }

    // إضافة المستخدم إلى الطلب
    req.user = {
      id: user._id,
      email: user.email,
      role: user.role,
      permissions: user.permissions
    };

    next();
  } catch (error) {
    console.error('خطأ في التحقق من المصادقة:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'رمز المصادقة غير صحيح'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'انتهت صلاحية رمز المصادقة'
      });
    }

    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
};

/**
 * Middleware للتحقق من الأدوار
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'غير مصرح بالوصول'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية للوصول إلى هذا المورد'
      });
    }

    next();
  };
};

/**
 * Middleware للتحقق من الصلاحيات
 */
const checkPermission = (permission) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'غير مصرح بالوصول'
        });
      }

      // المدير لديه جميع الصلاحيات
      if (req.user.role === 'admin') {
        return next();
      }

      // البحث عن المستخدم للحصول على الصلاحيات المحدثة
      const user = await User.findById(req.user.id);
      
      if (!user || !user.hasPermission(permission)) {
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحية لتنفيذ هذا الإجراء'
        });
      }

      next();
    } catch (error) {
      console.error('خطأ في التحقق من الصلاحيات:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم'
      });
    }
  };
};

module.exports = {
  auth,
  authorize,
  checkPermission
};
