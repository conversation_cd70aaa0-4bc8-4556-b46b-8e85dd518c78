import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Container
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  Sms,
  Security,
  Print
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { login, verifyLogin, resendCode, clearError } from '../../store/slices/authSlice';
import { RootState, AppDispatch } from '../../store/store';

const Login: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { loading, error, isAuthenticated } = useSelector((state: RootState) => state.auth);

  // حالات النموذج
  const [activeStep, setActiveStep] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    verificationCode: ''
  });
  const [verificationData, setVerificationData] = useState<{
    verificationId: string;
    phone: string;
    expiresIn: number;
  } | null>(null);
  const [countdown, setCountdown] = useState(0);

  // خطوات تسجيل الدخول
  const steps = ['بيانات الدخول', 'التحقق من الهوية'];

  // إعادة توجيه إذا كان مسجل الدخول
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // عداد تنازلي لإعادة الإرسال
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // مسح الأخطاء عند تغيير الخطوة
  useEffect(() => {
    dispatch(clearError());
  }, [activeStep, dispatch]);

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email || !formData.password) {
      return;
    }

    try {
      const result = await dispatch(login({
        email: formData.email,
        password: formData.password
      })).unwrap();

      if (result.success) {
        setVerificationData(result.data);
        setActiveStep(1);
        setCountdown(result.data.expiresIn);
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.verificationCode || !verificationData) {
      return;
    }

    try {
      const result = await dispatch(verifyLogin({
        verificationId: verificationData.verificationId,
        code: formData.verificationCode
      })).unwrap();

      if (result.success) {
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('خطأ في التحقق:', error);
    }
  };

  const handleResendCode = async () => {
    if (!verificationData || countdown > 0) return;

    try {
      const result = await dispatch(resendCode({
        verificationId: verificationData.verificationId
      })).unwrap();

      if (result.success) {
        setVerificationData(prev => prev ? {
          ...prev,
          verificationId: result.data.verificationId
        } : null);
        setCountdown(result.data.expiresIn);
      }
    } catch (error) {
      console.error('خطأ في إعادة الإرسال:', error);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2
      }}
    >
      <Container maxWidth="sm">
        <Paper
          elevation={24}
          sx={{
            borderRadius: 4,
            overflow: 'hidden',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)'
          }}
        >
          {/* الهيدر */}
          <Box
            sx={{
              background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
              color: 'white',
              padding: 4,
              textAlign: 'center'
            }}
          >
            <Print sx={{ fontSize: 48, mb: 2 }} />
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
              نظام المطبعة المتطور
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
              نظام إدارة شامل ومتكامل للمطابع
            </Typography>
          </Box>

          <CardContent sx={{ padding: 4 }}>
            {/* مؤشر الخطوات */}
            <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* عرض الأخطاء */}
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {/* الخطوة الأولى: بيانات الدخول */}
            {activeStep === 0 && (
              <Box component="form" onSubmit={handleLogin}>
                <TextField
                  fullWidth
                  label="البريد الإلكتروني"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  required
                  sx={{ mb: 3 }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Email color="primary" />
                      </InputAdornment>
                    ),
                  }}
                />

                <TextField
                  fullWidth
                  label="كلمة المرور"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleInputChange('password')}
                  required
                  sx={{ mb: 4 }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock color="primary" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  sx={{
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #1565c0, #1976d2)',
                    }
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    'متابعة'
                  )}
                </Button>
              </Box>
            )}

            {/* الخطوة الثانية: التحقق من الرمز */}
            {activeStep === 1 && verificationData && (
              <Box component="form" onSubmit={handleVerifyCode}>
                <Box sx={{ textAlign: 'center', mb: 3 }}>
                  <Security sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    التحقق من الهوية
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    تم إرسال رمز التحقق إلى رقم الهاتف
                  </Typography>
                  <Typography variant="h6" color="primary" sx={{ mt: 1 }}>
                    {verificationData.phone}
                  </Typography>
                </Box>

                <TextField
                  fullWidth
                  label="رمز التحقق"
                  value={formData.verificationCode}
                  onChange={handleInputChange('verificationCode')}
                  required
                  inputProps={{
                    maxLength: 6,
                    style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' }
                  }}
                  sx={{ mb: 3 }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Sms color="primary" />
                      </InputAdornment>
                    ),
                  }}
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading || formData.verificationCode.length !== 6}
                  sx={{
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    mb: 2,
                    background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #1565c0, #1976d2)',
                    }
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    'تسجيل الدخول'
                  )}
                </Button>

                <Box sx={{ textAlign: 'center' }}>
                  {countdown > 0 ? (
                    <Typography variant="body2" color="text.secondary">
                      يمكنك إعادة الإرسال خلال {formatTime(countdown)}
                    </Typography>
                  ) : (
                    <Button
                      variant="text"
                      onClick={handleResendCode}
                      disabled={loading}
                    >
                      إعادة إرسال الرمز
                    </Button>
                  )}
                </Box>

                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => {
                    setActiveStep(0);
                    setVerificationData(null);
                    setFormData(prev => ({ ...prev, verificationCode: '' }));
                  }}
                  sx={{ mt: 2 }}
                >
                  العودة
                </Button>
              </Box>
            )}
          </CardContent>
        </Paper>

        {/* معلومات إضافية */}
        <Box sx={{ textAlign: 'center', mt: 3, color: 'white' }}>
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            © 2024 نظام المطبعة المتطور. جميع الحقوق محفوظة.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Login;
