# نظام المطبعة المتطور - الإصدار 2.0

## نظرة عامة

نظام المطبعة المتطور هو تطبيق ويب شامل ومتقدم مصمم لتبسيط وأتمتة العمليات اليومية للمطابع. يوفر النظام إدارة كاملة للعملاء، الطلبات، المخزون، الفواتير، الموارد البشرية، والتقارير، مع نظام مصادقة متقدم يدعم رسائل SMS للتحقق الثنائي.

## الميزات الرئيسية

### إدارة المستخدمين
- تسجيل المستخدمين وإدارة الصلاحيات
- أدوار متعددة: مدير، مبيعات، إنتاج، محاسب، موارد بشرية
- التحكم في الوصول إلى الموارد المختلفة
- نظام مصادقة ثنائية متقدم مع رسائل SMS

### إدارة العملاء
- إضافة وتعديل وحذف بيانات العملاء
- تتبع معلومات الاتصال وتفاصيل العنوان
- تصنيف العملاء وتحديد شروط الدفع

### إدارة الطلبات
- إنشاء طلبات طباعة جديدة
- تتبع حالة الطلب عبر مراحل الإنتاج المختلفة
- إدارة المواعيد النهائية والأولويات
- تحميل وإدارة ملفات التصميم

### إدارة المخزون
- تتبع مخزون الورق والأحبار ومستلزمات الطباعة
- تنبيهات المخزون المنخفض
- تسجيل معاملات المخزون (إضافة/سحب)
- ربط المخزون بالموردين

### إدارة الفواتير
- إنشاء فواتير تلقائية من الطلبات
- تتبع المدفوعات وحالة الفواتير
- حساب الضرائب والخصومات
- إدارة المدفوعات الجزئية

### إدارة الموارد البشرية
- إدارة بيانات الموظفين الشاملة
- نظام الحضور والانصراف الإلكتروني
- إدارة الإجازات والطلبات
- حساب الرواتب والبدلات
- تتبع الأداء والتقييمات

### التقارير والإحصائيات
- تقارير المبيعات والإيرادات
- تقارير المخزون والإنتاج
- تحليل أداء العملاء
- تقارير الموارد البشرية
- لوحة معلومات تفاعلية

## المتطلبات التقنية

### المتطلبات الأساسية
- Node.js (v14 أو أحدث)
- MongoDB (v4 أو أحدث)
- npm أو yarn

### تثبيت واعداد المشروع

1. استنساخ المستودع:
```bash
git clone https://github.com/yourusername/print-management-system.git
cd print-management-system
```

2. تثبيت جميع الاعتمادات بأمر واحد:
```bash
npm run install-all
```

أو تثبيت الاعتمادات لكل جزء على حدة:
```bash
# تثبيت اعتمادات الواجهة الخلفية
cd backend
npm install

# تثبيت اعتمادات الواجهة الأمامية
cd ../frontend
npm install
```

3. إعداد متغيرات البيئة:
   - قم بنسخ ملف `.env.example` إلى `.env` في المجلد الرئيسي
   - قم بتعديل المتغيرات في ملف `.env` حسب إعداداتك

4. تشغيل التطبيق:
```bash
# تشغيل الواجهة الخلفية والأمامية معًا (من المجلد الرئيسي)
npm run dev

# أو تشغيل الواجهة الخلفية فقط
npm run server

# أو تشغيل الواجهة الأمامية فقط
npm run client

# للإنتاج (بعد البناء)
npm run prod
```

5. بناء التطبيق للإنتاج:
```bash
npm run build
```

## هيكل المشروع

```
نظام مطبعة متطور/
├── backend/             # الواجهة الخلفية (Express.js)
│   ├── config/          # ملفات الإعدادات
│   ├── controllers/     # وحدات التحكم
│   ├── middleware/      # الوسائط البرمجية
│   ├── models/          # نماذج البيانات
│   ├── routes/          # مسارات API
│   ├── utils/           # أدوات مساعدة
│   └── server.js        # نقطة الدخول الرئيسية
│
├── frontend/            # الواجهة الأمامية (React.js)
│   ├── public/          # الملفات العامة
│   ├── src/             # كود المصدر
│   │   ├── components/  # المكونات
│   │   ├── pages/       # الصفحات
│   │   ├── context/     # سياقات React
│   │   ├── utils/       # أدوات مساعدة
│   │   └── App.js       # المكون الرئيسي
│   └── package.json     # إعدادات الواجهة الأمامية
│
├── uploads/             # مجلد التحميلات
├── .env                 # متغيرات البيئة
├── .env.example         # نموذج متغيرات البيئة
├── .gitignore           # ملفات مستثناة من Git
├── package.json         # إعدادات المشروع
└── README.md            # توثيق المشروع
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع هذه الخطوات للمساهمة:

1. قم بعمل fork للمستودع
2. قم بإنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. قم بإجراء التغييرات وإضافتها (`git add .`)
4. قم بعمل commit للتغييرات (`git commit -m 'إضافة ميزة رائعة'`)
5. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
6. قم بفتح طلب سحب

## الترخيص

هذا المشروع مرخص بموجب رخصة MIT - انظر ملف LICENSE للحصول على التفاصيل.

## الاتصال

إذا كان لديك أي أسئلة أو استفسارات، يرجى التواصل معنا على [<EMAIL>](mailto:<EMAIL>).

## المتطلبات النظامية
- نظام تشغيل: Windows, macOS, أو Linux
- Node.js (الإصدار 14 أو أحدث)
- npm (الإصدار 6 أو أحدث)
- MongoDB (الإصدار 4 أو أحدث)
- متصفح حديث (Chrome, Firefox, Edge, Safari)

## التكنولوجيا المستخدمة
- **الواجهة الخلفية**: Node.js, Express.js, MongoDB, Mongoose
- **الواجهة الأمامية**: React.js, Redux, Material-UI
- **الأمان**: JWT, bcrypt, helmet
- **التحميل**: express-fileupload
- **المراسلات**: Nodemailer

## الوثائق التقنية
يمكن الوصول إلى وثائق API من خلال الرابط التالي بعد تشغيل الخادم:
```
http://localhost:5000/api-docs
```

## الدعم
للحصول على الدعم الفني، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- رقم الهاتف: +xxx-xxxx-xxxx

## الترخيص
هذا المشروع مرخص بموجب رخصة MIT - انظر ملف LICENSE للحصول على التفاصيل.

حقوق النشر © 2023 نظام مطبعة متطور