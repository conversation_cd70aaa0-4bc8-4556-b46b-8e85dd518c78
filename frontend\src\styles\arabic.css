/* تحسينات خاصة بالتصميم العربي */

/* إعدادات عامة للنصوص العربية */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* تحسينات الخطوط العربية */
.arabic-font {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', 'Segoe UI', sans-serif;
  font-feature-settings: 'liga' 1, 'kern' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* تحسينات للعناوين العربية */
.arabic-heading {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.02em;
}

/* تحسينات للنصوص العادية */
.arabic-body {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: 0.01em;
}

/* تحسينات للأرقام */
.arabic-numbers {
  font-variant-numeric: tabular-nums;
  direction: ltr;
  unicode-bidi: embed;
  font-family: 'SF Pro AR Display', 'Cairo', monospace;
}

/* تحسينات للأزرار العربية */
.arabic-button {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
  font-weight: 500;
  letter-spacing: 0.02em;
}

/* تحسينات للقوائم العربية */
.arabic-menu {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
  font-weight: 400;
}

/* تحسينات للجداول العربية */
.arabic-table {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
  font-weight: 400;
  direction: rtl;
}

.arabic-table th {
  font-weight: 600;
  text-align: right;
}

.arabic-table td {
  text-align: right;
}

/* تحسينات للنماذج العربية */
.arabic-form {
  direction: rtl;
}

.arabic-form .MuiTextField-root {
  direction: rtl;
}

.arabic-form .MuiInputBase-input {
  text-align: right;
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
}

.arabic-form .MuiInputLabel-root {
  right: 14px;
  left: auto;
  transform-origin: top right;
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
}

/* تحسينات للبطاقات العربية */
.arabic-card {
  direction: rtl;
  text-align: right;
}

.arabic-card .MuiCardContent-root {
  text-align: right;
}

/* تحسينات للتنبيهات العربية */
.arabic-alert {
  direction: rtl;
  text-align: right;
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
}

/* تحسينات للحوارات العربية */
.arabic-dialog {
  direction: rtl;
}

.arabic-dialog .MuiDialogTitle-root {
  text-align: right;
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
  font-weight: 600;
}

.arabic-dialog .MuiDialogContent-root {
  text-align: right;
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
}

/* تحسينات للتبويبات العربية */
.arabic-tabs .MuiTab-root {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
  font-weight: 500;
}

/* تحسينات للقوائم المنسدلة العربية */
.arabic-select .MuiSelect-select {
  text-align: right;
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
}

.arabic-select .MuiMenuItem-root {
  text-align: right;
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
}

/* تحسينات للشرائح العربية */
.arabic-stepper .MuiStepLabel-label {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
}

/* تحسينات للتقويم العربي */
.arabic-datepicker {
  direction: rtl;
}

.arabic-datepicker .MuiPickersDay-root {
  font-family: 'SF Pro AR Display', 'Cairo', monospace;
}

/* تحسينات للرسوم البيانية العربية */
.arabic-chart {
  direction: rtl;
}

.arabic-chart text {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', sans-serif;
}

/* تحسينات للطباعة */
@media print {
  .arabic-font,
  .arabic-heading,
  .arabic-body,
  .arabic-button,
  .arabic-menu,
  .arabic-table,
  .arabic-form,
  .arabic-card,
  .arabic-alert,
  .arabic-dialog {
    font-family: 'SF Pro AR Display', 'Cairo', 'Times New Roman', serif !important;
    color: black !important;
  }
  
  .arabic-heading {
    font-size: 14pt !important;
    font-weight: 700 !important;
  }
  
  .arabic-body {
    font-size: 12pt !important;
    line-height: 1.4 !important;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .arabic-heading {
    font-size: 1.2rem;
    line-height: 1.4;
  }
  
  .arabic-body {
    font-size: 0.9rem;
    line-height: 1.5;
  }
  
  .arabic-button {
    font-size: 0.875rem;
    padding: 8px 16px;
  }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
  .arabic-font,
  .arabic-heading,
  .arabic-body {
    font-weight: 600;
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
