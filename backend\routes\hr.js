const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Employee = require('../models/Employee');
const Leave = require('../models/Leave');
const Attendance = require('../models/Attendance');
const { auth, authorize, checkPermission } = require('../middleware/authNew');
const smsService = require('../services/smsService');

const router = express.Router();

/**
 * @route   GET /api/hr/employees
 * @desc    الحصول على قائمة الموظفين
 * @access  Private (HR, Admin, Manager)
 */
router.get('/employees', [
  auth,
  authorize('admin', 'manager', 'hr'),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('department').optional().isString(),
  query('status').optional().isIn(['active', 'inactive', 'terminated', 'suspended']),
  query('search').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // بناء الاستعلام
    let query = {};
    
    if (req.query.department) {
      query.department = req.query.department;
    }
    
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    if (req.query.search) {
      query.$or = [
        { firstName: { $regex: req.query.search, $options: 'i' } },
        { lastName: { $regex: req.query.search, $options: 'i' } },
        { arabicName: { $regex: req.query.search, $options: 'i' } },
        { employeeId: { $regex: req.query.search, $options: 'i' } },
        { email: { $regex: req.query.search, $options: 'i' } }
      ];
    }

    const employees = await Employee.find(query)
      .populate('directManager', 'firstName lastName arabicName')
      .select('-salary -bankInfo -documents')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Employee.countDocuments(query);

    res.json({
      success: true,
      data: {
        employees,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total,
          limit
        }
      }
    });

  } catch (error) {
    console.error('خطأ في جلب الموظفين:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   POST /api/hr/employees
 * @desc    إضافة موظف جديد
 * @access  Private (HR, Admin)
 */
router.post('/employees', [
  auth,
  authorize('admin', 'hr'),
  body('firstName').notEmpty().withMessage('الاسم الأول مطلوب'),
  body('lastName').notEmpty().withMessage('اسم العائلة مطلوب'),
  body('arabicName').notEmpty().withMessage('الاسم بالعربية مطلوب'),
  body('nationalId').isLength({ min: 10, max: 10 }).withMessage('رقم الهوية يجب أن يكون 10 أرقام'),
  body('email').isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('phone').isMobilePhone().withMessage('رقم الهاتف غير صحيح'),
  body('department').notEmpty().withMessage('القسم مطلوب'),
  body('position').notEmpty().withMessage('المنصب مطلوب'),
  body('salary.basic').isNumeric().withMessage('الراتب الأساسي مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    // التحقق من عدم تكرار رقم الهوية أو البريد الإلكتروني
    const existingEmployee = await Employee.findOne({
      $or: [
        { nationalId: req.body.nationalId },
        { email: req.body.email }
      ]
    });

    if (existingEmployee) {
      return res.status(400).json({
        success: false,
        message: 'رقم الهوية أو البريد الإلكتروني مستخدم مسبقاً'
      });
    }

    // إنشاء رقم موظف تلقائي
    const lastEmployee = await Employee.findOne().sort({ employeeId: -1 });
    let employeeId = 'EMP001';
    
    if (lastEmployee && lastEmployee.employeeId) {
      const lastNumber = parseInt(lastEmployee.employeeId.substring(3));
      employeeId = `EMP${String(lastNumber + 1).padStart(3, '0')}`;
    }

    const employeeData = {
      ...req.body,
      employeeId
    };

    const employee = new Employee(employeeData);
    await employee.save();

    // إرسال رسالة ترحيب عبر SMS
    try {
      await smsService.sendNotification(
        employee.phone,
        `مرحباً ${employee.arabicName}، تم إضافتك كموظف جديد في نظام المطبعة. رقم الموظف: ${employee.employeeId}`
      );
    } catch (smsError) {
      console.error('خطأ في إرسال رسالة الترحيب:', smsError);
    }

    res.status(201).json({
      success: true,
      message: 'تم إضافة الموظف بنجاح',
      data: employee
    });

  } catch (error) {
    console.error('خطأ في إضافة الموظف:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   GET /api/hr/employees/:id
 * @desc    الحصول على تفاصيل موظف
 * @access  Private (HR, Admin, Manager)
 */
router.get('/employees/:id', [
  auth,
  authorize('admin', 'manager', 'hr')
], async (req, res) => {
  try {
    const employee = await Employee.findById(req.params.id)
      .populate('directManager', 'firstName lastName arabicName')
      .populate('userId', 'name email role');

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'الموظف غير موجود'
      });
    }

    res.json({
      success: true,
      data: employee
    });

  } catch (error) {
    console.error('خطأ في جلب تفاصيل الموظف:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   PUT /api/hr/employees/:id
 * @desc    تحديث بيانات موظف
 * @access  Private (HR, Admin)
 */
router.put('/employees/:id', [
  auth,
  authorize('admin', 'hr')
], async (req, res) => {
  try {
    const employee = await Employee.findById(req.params.id);
    
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'الموظف غير موجود'
      });
    }

    // تحديث البيانات
    Object.keys(req.body).forEach(key => {
      if (req.body[key] !== undefined) {
        employee[key] = req.body[key];
      }
    });

    await employee.save();

    res.json({
      success: true,
      message: 'تم تحديث بيانات الموظف بنجاح',
      data: employee
    });

  } catch (error) {
    console.error('خطأ في تحديث الموظف:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   POST /api/hr/leaves
 * @desc    طلب إجازة جديدة
 * @access  Private
 */
router.post('/leaves', [
  auth,
  body('type').isIn(['annual', 'sick', 'emergency', 'maternity', 'paternity', 'unpaid', 'study']).withMessage('نوع الإجازة غير صحيح'),
  body('startDate').isISO8601().withMessage('تاريخ البداية غير صحيح'),
  body('endDate').isISO8601().withMessage('تاريخ النهاية غير صحيح'),
  body('reason').notEmpty().withMessage('سبب الإجازة مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    // البحث عن الموظف
    const employee = await Employee.findOne({ userId: req.user.id });
    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'بيانات الموظف غير موجودة'
      });
    }

    const { type, startDate, endDate, reason } = req.body;

    // التحقق من تداخل الإجازات
    const hasOverlap = await Leave.checkOverlap(employee._id, new Date(startDate), new Date(endDate));
    if (hasOverlap) {
      return res.status(400).json({
        success: false,
        message: 'يوجد تداخل مع إجازة أخرى في نفس الفترة'
      });
    }

    // حساب مدة الإجازة
    const duration = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) + 1;

    const leave = new Leave({
      employee: employee._id,
      type,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      duration,
      reason,
      ...req.body
    });

    await leave.save();

    // إرسال إشعار للمدير المباشر
    if (employee.directManager) {
      const manager = await Employee.findById(employee.directManager).populate('userId');
      if (manager && manager.phone) {
        try {
          await smsService.sendNotification(
            manager.phone,
            `طلب إجازة جديد من ${employee.arabicName} من ${startDate} إلى ${endDate}. يرجى المراجعة والموافقة.`
          );
        } catch (smsError) {
          console.error('خطأ في إرسال إشعار للمدير:', smsError);
        }
      }
    }

    res.status(201).json({
      success: true,
      message: 'تم تقديم طلب الإجازة بنجاح',
      data: leave
    });

  } catch (error) {
    console.error('خطأ في طلب الإجازة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   GET /api/hr/leaves
 * @desc    الحصول على قائمة الإجازات
 * @access  Private
 */
router.get('/leaves', [
  auth,
  query('status').optional().isIn(['pending', 'approved', 'rejected', 'cancelled']),
  query('type').optional().isIn(['annual', 'sick', 'emergency', 'maternity', 'paternity', 'unpaid', 'study']),
  query('employee').optional().isMongoId()
], async (req, res) => {
  try {
    let query = {};

    // إذا لم يكن مدير أو HR، يرى إجازاته فقط
    if (!['admin', 'manager', 'hr'].includes(req.user.role)) {
      const employee = await Employee.findOne({ userId: req.user.id });
      if (employee) {
        query.employee = employee._id;
      }
    }

    if (req.query.status) query.status = req.query.status;
    if (req.query.type) query.type = req.query.type;
    if (req.query.employee) query.employee = req.query.employee;

    const leaves = await Leave.find(query)
      .populate('employee', 'firstName lastName arabicName department')
      .populate('approvedBy', 'name')
      .sort({ appliedDate: -1 });

    res.json({
      success: true,
      data: leaves
    });

  } catch (error) {
    console.error('خطأ في جلب الإجازات:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   PUT /api/hr/leaves/:id/approve
 * @desc    الموافقة على إجازة
 * @access  Private (Manager, HR, Admin)
 */
router.put('/leaves/:id/approve', [
  auth,
  authorize('admin', 'manager', 'hr'),
  body('notes').optional().isString()
], async (req, res) => {
  try {
    const leave = await Leave.findById(req.params.id).populate('employee');
    
    if (!leave) {
      return res.status(404).json({
        success: false,
        message: 'طلب الإجازة غير موجود'
      });
    }

    if (leave.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن الموافقة على هذا الطلب'
      });
    }

    await leave.approve(req.user.id, req.body.notes);

    // إرسال إشعار للموظف
    try {
      await smsService.sendNotification(
        leave.employee.phone,
        `تم الموافقة على طلب إجازتك من ${leave.startDate.toLocaleDateString('ar-SA')} إلى ${leave.endDate.toLocaleDateString('ar-SA')}`
      );
    } catch (smsError) {
      console.error('خطأ في إرسال إشعار الموافقة:', smsError);
    }

    res.json({
      success: true,
      message: 'تم الموافقة على الإجازة بنجاح',
      data: leave
    });

  } catch (error) {
    console.error('خطأ في الموافقة على الإجازة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

module.exports = router;
