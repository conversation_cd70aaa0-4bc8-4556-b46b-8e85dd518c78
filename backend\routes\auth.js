const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');

const User = require('../models/User');
const TwoFactorAuth = require('../models/TwoFactorAuth');
const smsService = require('../services/smsService');
const config = require('../config/config');
const auth = require('../middleware/auth');

const router = express.Router();

// معدل محدود لطلبات تسجيل الدخول
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 5, // 5 محاولات كحد أقصى
  message: {
    success: false,
    message: 'تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة لاحقاً'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// معدل محدود لطلبات SMS
const smsLimiter = rateLimit({
  windowMs: 60 * 1000, // دقيقة واحدة
  max: 2, // رسالتان كحد أقصى في الدقيقة
  message: {
    success: false,
    message: 'تم تجاوز الحد الأقصى لإرسال رسائل SMS. يرجى الانتظار'
  }
});

/**
 * @route   POST /api/auth/login
 * @desc    تسجيل الدخول - المرحلة الأولى
 * @access  Public
 */
router.post('/login', [
  loginLimiter,
  body('email').isEmail().normalizeEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('password').isLength({ min: 8 }).withMessage('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
], async (req, res) => {
  try {
    // التحقق من صحة البيانات
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // البحث عن المستخدم
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'بيانات تسجيل الدخول غير صحيحة'
      });
    }

    // التحقق من حالة المستخدم
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'تم إيقاف هذا الحساب. يرجى التواصل مع الإدارة'
      });
    }

    // التحقق من القفل
    if (user.isLocked) {
      return res.status(423).json({
        success: false,
        message: 'تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول متعددة'
      });
    }

    // التحقق من كلمة المرور
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      await user.incLoginAttempts();
      return res.status(401).json({
        success: false,
        message: 'بيانات تسجيل الدخول غير صحيحة'
      });
    }

    // إنشاء رمز التحقق وإرساله
    const { verification, code } = await TwoFactorAuth.createVerificationCode(
      user._id,
      user.phone,
      'login',
      {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    // إرسال رمز التحقق عبر SMS
    await smsService.sendVerificationCode(user.phone, code);

    res.json({
      success: true,
      message: 'تم إرسال رمز التحقق إلى هاتفك',
      data: {
        verificationId: verification._id,
        phone: user.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'), // إخفاء جزء من الرقم
        expiresIn: 10 * 60 // 10 دقائق بالثواني
      }
    });

  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   POST /api/auth/verify-login
 * @desc    تسجيل الدخول - المرحلة الثانية (التحقق من رمز SMS)
 * @access  Public
 */
router.post('/verify-login', [
  body('verificationId').isMongoId().withMessage('معرف التحقق غير صحيح'),
  body('code').isLength({ min: 6, max: 6 }).withMessage('رمز التحقق يجب أن يكون 6 أرقام')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { verificationId, code } = req.body;

    // البحث عن رمز التحقق
    const verification = await TwoFactorAuth.findById(verificationId).populate('userId');
    if (!verification) {
      return res.status(404).json({
        success: false,
        message: 'رمز التحقق غير موجود'
      });
    }

    // التحقق من الرمز
    const verificationResult = verification.verifyCode(code);
    if (!verificationResult.success) {
      return res.status(400).json({
        success: false,
        message: verificationResult.message
      });
    }

    const user = verification.userId;

    // إعادة تعيين محاولات تسجيل الدخول
    await user.resetLoginAttempts();

    // إنشاء JWT
    const token = user.generateAuthToken();

    // إنشاء refresh token
    const refreshToken = jwt.sign(
      { id: user._id, type: 'refresh' },
      config.jwt.secret,
      { expiresIn: config.jwt.refreshExpiresIn }
    );

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      data: {
        token,
        refreshToken,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          avatar: user.avatar,
          permissions: user.permissions
        }
      }
    });

  } catch (error) {
    console.error('خطأ في التحقق من تسجيل الدخول:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   POST /api/auth/resend-code
 * @desc    إعادة إرسال رمز التحقق
 * @access  Public
 */
router.post('/resend-code', [
  smsLimiter,
  body('verificationId').isMongoId().withMessage('معرف التحقق غير صحيح')
], async (req, res) => {
  try {
    const { verificationId } = req.body;

    // البحث عن رمز التحقق الأصلي
    const originalVerification = await TwoFactorAuth.findById(verificationId).populate('userId');
    if (!originalVerification) {
      return res.status(404).json({
        success: false,
        message: 'رمز التحقق غير موجود'
      });
    }

    // إنشاء رمز جديد
    const { verification, code } = await TwoFactorAuth.createVerificationCode(
      originalVerification.userId._id,
      originalVerification.phone,
      originalVerification.type
    );

    // إرسال الرمز الجديد
    await smsService.sendVerificationCode(originalVerification.phone, code);

    res.json({
      success: true,
      message: 'تم إرسال رمز تحقق جديد',
      data: {
        verificationId: verification._id,
        expiresIn: 10 * 60
      }
    });

  } catch (error) {
    console.error('خطأ في إعادة إرسال رمز التحقق:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   POST /api/auth/logout
 * @desc    تسجيل الخروج
 * @access  Private
 */
router.post('/logout', auth, async (req, res) => {
  try {
    // يمكن إضافة منطق إضافي هنا مثل إلغاء الرموز المميزة
    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });
  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   GET /api/auth/me
 * @desc    الحصول على بيانات المستخدم الحالي
 * @access  Private
 */
router.get('/me', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('خطأ في جلب بيانات المستخدم:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ داخلي في الخادم'
    });
  }
});

/**
 * @route   POST /api/auth/refresh-token
 * @desc    تجديد الرمز المميز
 * @access  Public
 */
router.post('/refresh-token', [
  body('refreshToken').notEmpty().withMessage('رمز التجديد مطلوب')
], async (req, res) => {
  try {
    const { refreshToken } = req.body;

    // التحقق من صحة رمز التجديد
    const decoded = jwt.verify(refreshToken, config.jwt.secret);
    
    if (decoded.type !== 'refresh') {
      return res.status(401).json({
        success: false,
        message: 'رمز التجديد غير صحيح'
      });
    }

    // البحث عن المستخدم
    const user = await User.findById(decoded.id);
    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود أو غير نشط'
      });
    }

    // إنشاء رمز جديد
    const newToken = user.generateAuthToken();

    res.json({
      success: true,
      data: {
        token: newToken
      }
    });

  } catch (error) {
    console.error('خطأ في تجديد الرمز:', error);
    res.status(401).json({
      success: false,
      message: 'رمز التجديد غير صحيح أو منتهي الصلاحية'
    });
  }
});

module.exports = router;
