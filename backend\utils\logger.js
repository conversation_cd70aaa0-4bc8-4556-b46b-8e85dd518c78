const winston = require('winston');
const config = require('config');
const path = require('path');
const fs = require('fs');

// إنشاء مجلد السجلات إذا لم يكن موجودًا
const logDir = 'logs';
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// الحصول على مستوى السجل من ملف التكوين أو استخدام القيمة الافتراضية
const logLevel = config.has('logLevel') ? config.get('logLevel') : 'info';

// تنسيق السجلات
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// تنسيق السجلات للطباعة في وحدة التحكم
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}${info.stack ? '\n' + info.stack : ''}`
  )
);

// إنشاء مثيل logger
const logger = winston.createLogger({
  level: logLevel,
  format: logFormat,
  defaultMeta: { service: 'print-management-system' },
  transports: [
    // سجل الأخطاء في ملف
    new winston.transports.File({ 
      filename: path.join(logDir, 'error.log'), 
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // سجل جميع المستويات في ملف
    new winston.transports.File({ 
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
  exceptionHandlers: [
    new winston.transports.File({ 
      filename: path.join(logDir, 'exceptions.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  ],
  rejectionHandlers: [
    new winston.transports.File({ 
      filename: path.join(logDir, 'rejections.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  ],
  exitOnError: false
});

// إضافة نقل وحدة التحكم في بيئة التطوير
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    handleExceptions: true,
    handleRejections: true
  }));
}

// إنشاء وسيط تسجيل HTTP لـ Express
const httpLogger = (req, res, next) => {
  // تجاهل طلبات الصحة والحالة
  if (req.path === '/health' || req.path === '/status') {
    return next();
  }

  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    const message = `${req.method} ${req.originalUrl} ${res.statusCode} ${duration}ms`;
    
    // تسجيل الطلبات بناءً على كود الحالة
    if (res.statusCode >= 500) {
      logger.error(message, { 
        ip: req.ip, 
        user: req.user ? req.user.id : 'anonymous',
        body: req.method === 'POST' || req.method === 'PUT' ? req.body : undefined
      });
    } else if (res.statusCode >= 400) {
      logger.warn(message, { 
        ip: req.ip, 
        user: req.user ? req.user.id : 'anonymous' 
      });
    } else {
      logger.info(message, { 
        ip: req.ip, 
        user: req.user ? req.user.id : 'anonymous' 
      });
    }
  });

  next();
};

module.exports = { logger, httpLogger };