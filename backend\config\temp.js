/**
 * ملف إعداد وتكوين الملفات المؤقتة
 * يقوم بإعداد وتكوين خدمات إدارة الملفات المؤقتة في النظام
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const config = require('config');
const logger = require('../utils/logger');
const { recordError } = require('./monitoring');

// الحصول على إعدادات الملفات المؤقتة من ملف التكوين
let tempConfig;
try {
  tempConfig = config.get('temp');
} catch (error) {
  logger.warn('لم يتم العثور على إعدادات الملفات المؤقتة في ملف التكوين، سيتم استخدام الإعدادات الافتراضية');
  tempConfig = {
    tempDir: process.env.TEMP_DIR || path.join(process.cwd(), 'temp'),
    uploadDir: process.env.UPLOAD_DIR || path.join(process.cwd(), 'temp', 'uploads'),
    exportDir: process.env.EXPORT_DIR || path.join(process.cwd(), 'temp', 'exports'),
    reportDir: process.env.REPORT_DIR || path.join(process.cwd(), 'temp', 'reports'),
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    allowedFileTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv').split(','),
    fileExpiryTime: parseInt(process.env.FILE_EXPIRY_TIME) || 24 * 60 * 60 * 1000, // 24 ساعة بالمللي ثانية
  };
}

/**
 * إنشاء المجلدات المؤقتة إذا لم تكن موجودة
 */
const setupTempDirectories = () => {
  try {
    // إنشاء المجلد الرئيسي للملفات المؤقتة
    if (!fs.existsSync(tempConfig.tempDir)) {
      fs.mkdirSync(tempConfig.tempDir, { recursive: true });
      logger.info(`تم إنشاء مجلد الملفات المؤقتة: ${tempConfig.tempDir}`);
    }

    // إنشاء مجلد التحميلات
    if (!fs.existsSync(tempConfig.uploadDir)) {
      fs.mkdirSync(tempConfig.uploadDir, { recursive: true });
      logger.info(`تم إنشاء مجلد التحميلات: ${tempConfig.uploadDir}`);
    }

    // إنشاء مجلد التصدير
    if (!fs.existsSync(tempConfig.exportDir)) {
      fs.mkdirSync(tempConfig.exportDir, { recursive: true });
      logger.info(`تم إنشاء مجلد التصدير: ${tempConfig.exportDir}`);
    }

    // إنشاء مجلد التقارير
    if (!fs.existsSync(tempConfig.reportDir)) {
      fs.mkdirSync(tempConfig.reportDir, { recursive: true });
      logger.info(`تم إنشاء مجلد التقارير: ${tempConfig.reportDir}`);
    }

    // إنشاء ملف .gitignore في المجلد المؤقت لتجنب تتبع الملفات المؤقتة
    const gitignorePath = path.join(tempConfig.tempDir, '.gitignore');
    if (!fs.existsSync(gitignorePath)) {
      fs.writeFileSync(gitignorePath, '*\n!.gitignore\n');
      logger.info('تم إنشاء ملف .gitignore في مجلد الملفات المؤقتة');
    }
  } catch (error) {
    logger.error('فشل في إنشاء مجلدات الملفات المؤقتة', error);
    recordError(error);
    throw error;
  }
};

/**
 * إنشاء اسم ملف فريد
 * @param {string} originalName - الاسم الأصلي للملف
 * @returns {string} اسم الملف الفريد
 */
const generateUniqueFileName = (originalName) => {
  try {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = path.extname(originalName);
    const fileName = `${timestamp}-${randomString}${extension}`;
    return fileName;
  } catch (error) {
    logger.error('فشل في إنشاء اسم ملف فريد', error);
    recordError(error);
    throw error;
  }
};

/**
 * التحقق من نوع الملف
 * @param {string} fileName - اسم الملف
 * @returns {boolean} نتيجة التحقق
 */
const isAllowedFileType = (fileName) => {
  try {
    const extension = path.extname(fileName).toLowerCase().substring(1);
    return tempConfig.allowedFileTypes.includes(extension);
  } catch (error) {
    logger.error('فشل في التحقق من نوع الملف', error);
    recordError(error);
    return false;
  }
};

/**
 * التحقق من حجم الملف
 * @param {number} fileSize - حجم الملف بالبايت
 * @returns {boolean} نتيجة التحقق
 */
const isAllowedFileSize = (fileSize) => {
  return fileSize <= tempConfig.maxFileSize;
};

/**
 * حفظ ملف مؤقت
 * @param {Object} fileData - بيانات الملف
 * @param {Buffer} fileData.buffer - محتوى الملف
 * @param {string} fileData.originalname - الاسم الأصلي للملف
 * @param {string} fileData.mimetype - نوع MIME للملف
 * @param {number} fileData.size - حجم الملف بالبايت
 * @param {string} [directory='uploads'] - المجلد المراد حفظ الملف فيه (uploads, exports, reports)
 * @returns {Object} معلومات الملف المحفوظ
 */
const saveTempFile = (fileData, directory = 'uploads') => {
  try {
    // التحقق من نوع الملف
    if (!isAllowedFileType(fileData.originalname)) {
      throw new Error(`نوع الملف غير مسموح به: ${path.extname(fileData.originalname)}`);
    }

    // التحقق من حجم الملف
    if (!isAllowedFileSize(fileData.size)) {
      throw new Error(`حجم الملف يتجاوز الحد المسموح به: ${fileData.size} بايت`);
    }

    // تحديد المجلد المناسب
    let targetDir;
    switch (directory) {
      case 'uploads':
        targetDir = tempConfig.uploadDir;
        break;
      case 'exports':
        targetDir = tempConfig.exportDir;
        break;
      case 'reports':
        targetDir = tempConfig.reportDir;
        break;
      default:
        targetDir = tempConfig.uploadDir;
    }

    // إنشاء اسم ملف فريد
    const fileName = generateUniqueFileName(fileData.originalname);
    const filePath = path.join(targetDir, fileName);

    // حفظ الملف
    fs.writeFileSync(filePath, fileData.buffer);

    // إنشاء معلومات الملف
    const fileInfo = {
      fileName,
      originalName: fileData.originalname,
      mimeType: fileData.mimetype,
      size: fileData.size,
      path: filePath,
      url: `/temp/${directory}/${fileName}`,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + tempConfig.fileExpiryTime)
    };

    logger.info(`تم حفظ ملف مؤقت: ${filePath}`);
    return fileInfo;
  } catch (error) {
    logger.error('فشل في حفظ ملف مؤقت', error);
    recordError(error);
    throw error;
  }
};

/**
 * حذف ملف مؤقت
 * @param {string} filePath - مسار الملف
 * @returns {boolean} نتيجة الحذف
 */
const deleteTempFile = (filePath) => {
  try {
    // التحقق من أن المسار ضمن المجلدات المؤقتة المسموح بها
    const normalizedPath = path.normalize(filePath);
    const tempDirNormalized = path.normalize(tempConfig.tempDir);
    
    if (!normalizedPath.startsWith(tempDirNormalized)) {
      logger.warn(`محاولة حذف ملف خارج مجلد الملفات المؤقتة: ${filePath}`);
      return false;
    }

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      logger.warn(`الملف غير موجود: ${filePath}`);
      return false;
    }

    // حذف الملف
    fs.unlinkSync(filePath);
    logger.info(`تم حذف ملف مؤقت: ${filePath}`);
    return true;
  } catch (error) {
    logger.error(`فشل في حذف ملف مؤقت: ${filePath}`, error);
    recordError(error);
    return false;
  }
};

/**
 * تنظيف الملفات المؤقتة منتهية الصلاحية
 * @returns {number} عدد الملفات التي تم حذفها
 */
const cleanupExpiredFiles = () => {
  try {
    let deletedCount = 0;
    const now = Date.now();
    const expiryTime = tempConfig.fileExpiryTime;

    // وظيفة مساعدة لتنظيف مجلد محدد
    const cleanupDirectory = (directory) => {
      if (!fs.existsSync(directory)) {
        return;
      }

      const files = fs.readdirSync(directory);

      for (const file of files) {
        // تجاهل ملف .gitignore
        if (file === '.gitignore') {
          continue;
        }

        const filePath = path.join(directory, file);
        const stats = fs.statSync(filePath);

        // التحقق مما إذا كان ملفًا
        if (stats.isFile()) {
          // التحقق من وقت إنشاء الملف
          const fileAge = now - stats.birthtimeMs;

          // حذف الملف إذا تجاوز وقت انتهاء الصلاحية
          if (fileAge > expiryTime) {
            if (deleteTempFile(filePath)) {
              deletedCount++;
            }
          }
        }
      }
    };

    // تنظيف جميع المجلدات المؤقتة
    cleanupDirectory(tempConfig.uploadDir);
    cleanupDirectory(tempConfig.exportDir);
    cleanupDirectory(tempConfig.reportDir);

    logger.info(`تم تنظيف ${deletedCount} ملف مؤقت منتهي الصلاحية`);
    return deletedCount;
  } catch (error) {
    logger.error('فشل في تنظيف الملفات المؤقتة منتهية الصلاحية', error);
    recordError(error);
    return 0;
  }
};

/**
 * الحصول على معلومات الملف
 * @param {string} filePath - مسار الملف
 * @returns {Object|null} معلومات الملف أو null في حالة الفشل
 */
const getTempFileInfo = (filePath) => {
  try {
    // التحقق من أن المسار ضمن المجلدات المؤقتة المسموح بها
    const normalizedPath = path.normalize(filePath);
    const tempDirNormalized = path.normalize(tempConfig.tempDir);
    
    if (!normalizedPath.startsWith(tempDirNormalized)) {
      logger.warn(`محاولة الوصول إلى ملف خارج مجلد الملفات المؤقتة: ${filePath}`);
      return null;
    }

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      logger.warn(`الملف غير موجود: ${filePath}`);
      return null;
    }

    // الحصول على معلومات الملف
    const stats = fs.statSync(filePath);
    const fileName = path.basename(filePath);
    const originalName = fileName.substring(fileName.indexOf('-') + 9); // استخراج الاسم الأصلي من اسم الملف الفريد
    const extension = path.extname(fileName);
    const mimeType = getMimeType(extension);

    // تحديد نوع المجلد
    let directory = 'uploads';
    if (normalizedPath.includes(path.normalize(tempConfig.exportDir))) {
      directory = 'exports';
    } else if (normalizedPath.includes(path.normalize(tempConfig.reportDir))) {
      directory = 'reports';
    }

    // إنشاء معلومات الملف
    const fileInfo = {
      fileName,
      originalName,
      mimeType,
      size: stats.size,
      path: filePath,
      url: `/temp/${directory}/${fileName}`,
      createdAt: stats.birthtime,
      expiresAt: new Date(stats.birthtimeMs + tempConfig.fileExpiryTime)
    };

    return fileInfo;
  } catch (error) {
    logger.error(`فشل في الحصول على معلومات الملف: ${filePath}`, error);
    recordError(error);
    return null;
  }
};

/**
 * الحصول على نوع MIME بناءً على امتداد الملف
 * @param {string} extension - امتداد الملف
 * @returns {string} نوع MIME
 */
const getMimeType = (extension) => {
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    '.txt': 'text/plain',
    '.csv': 'text/csv'
  };

  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
};

/**
 * إنشاء ملف تقرير
 * @param {string} reportName - اسم التقرير
 * @param {Buffer|string} content - محتوى التقرير
 * @param {string} [extension='.pdf'] - امتداد الملف
 * @returns {Object} معلومات ملف التقرير
 */
const createReportFile = (reportName, content, extension = '.pdf') => {
  try {
    // إنشاء اسم ملف فريد
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(4).toString('hex');
    const fileName = `report-${reportName}-${timestamp}-${randomString}${extension}`;
    const filePath = path.join(tempConfig.reportDir, fileName);

    // حفظ الملف
    fs.writeFileSync(filePath, content);

    // إنشاء معلومات الملف
    const fileInfo = {
      fileName,
      originalName: `${reportName}${extension}`,
      mimeType: getMimeType(extension),
      size: fs.statSync(filePath).size,
      path: filePath,
      url: `/temp/reports/${fileName}`,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + tempConfig.fileExpiryTime)
    };

    logger.info(`تم إنشاء ملف تقرير: ${filePath}`);
    return fileInfo;
  } catch (error) {
    logger.error('فشل في إنشاء ملف تقرير', error);
    recordError(error);
    throw error;
  }
};

/**
 * إنشاء ملف تصدير
 * @param {string} exportName - اسم ملف التصدير
 * @param {Buffer|string} content - محتوى ملف التصدير
 * @param {string} [extension='.xlsx'] - امتداد الملف
 * @returns {Object} معلومات ملف التصدير
 */
const createExportFile = (exportName, content, extension = '.xlsx') => {
  try {
    // إنشاء اسم ملف فريد
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(4).toString('hex');
    const fileName = `export-${exportName}-${timestamp}-${randomString}${extension}`;
    const filePath = path.join(tempConfig.exportDir, fileName);

    // حفظ الملف
    fs.writeFileSync(filePath, content);

    // إنشاء معلومات الملف
    const fileInfo = {
      fileName,
      originalName: `${exportName}${extension}`,
      mimeType: getMimeType(extension),
      size: fs.statSync(filePath).size,
      path: filePath,
      url: `/temp/exports/${fileName}`,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + tempConfig.fileExpiryTime)
    };

    logger.info(`تم إنشاء ملف تصدير: ${filePath}`);
    return fileInfo;
  } catch (error) {
    logger.error('فشل في إنشاء ملف تصدير', error);
    recordError(error);
    throw error;
  }
};

/**
 * إنشاء ملف مؤقت من نص
 * @param {string} content - محتوى النص
 * @param {string} [fileName='temp.txt'] - اسم الملف
 * @param {string} [directory='uploads'] - المجلد المراد حفظ الملف فيه
 * @returns {Object} معلومات الملف
 */
const createTempFileFromText = (content, fileName = 'temp.txt', directory = 'uploads') => {
  try {
    // تحديد المجلد المناسب
    let targetDir;
    switch (directory) {
      case 'uploads':
        targetDir = tempConfig.uploadDir;
        break;
      case 'exports':
        targetDir = tempConfig.exportDir;
        break;
      case 'reports':
        targetDir = tempConfig.reportDir;
        break;
      default:
        targetDir = tempConfig.uploadDir;
    }

    // إنشاء اسم ملف فريد
    const uniqueFileName = generateUniqueFileName(fileName);
    const filePath = path.join(targetDir, uniqueFileName);

    // حفظ الملف
    fs.writeFileSync(filePath, content);

    // إنشاء معلومات الملف
    const fileInfo = {
      fileName: uniqueFileName,
      originalName: fileName,
      mimeType: getMimeType(path.extname(fileName)),
      size: fs.statSync(filePath).size,
      path: filePath,
      url: `/temp/${directory}/${uniqueFileName}`,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + tempConfig.fileExpiryTime)
    };

    logger.info(`تم إنشاء ملف مؤقت من نص: ${filePath}`);
    return fileInfo;
  } catch (error) {
    logger.error('فشل في إنشاء ملف مؤقت من نص', error);
    recordError(error);
    throw error;
  }
};

/**
 * إنشاء ملف مؤقت من كائن JSON
 * @param {Object} data - كائن البيانات
 * @param {string} [fileName='data.json'] - اسم الملف
 * @param {string} [directory='exports'] - المجلد المراد حفظ الملف فيه
 * @returns {Object} معلومات الملف
 */
const createTempFileFromJson = (data, fileName = 'data.json', directory = 'exports') => {
  try {
    const jsonContent = JSON.stringify(data, null, 2);
    return createTempFileFromText(jsonContent, fileName, directory);
  } catch (error) {
    logger.error('فشل في إنشاء ملف مؤقت من كائن JSON', error);
    recordError(error);
    throw error;
  }
};

// إعداد المجلدات المؤقتة عند تحميل الملف
setupTempDirectories();

module.exports = {
  // الإعدادات
  tempConfig,
  setupTempDirectories,
  
  // وظائف إدارة الملفات المؤقتة
  generateUniqueFileName,
  isAllowedFileType,
  isAllowedFileSize,
  saveTempFile,
  deleteTempFile,
  cleanupExpiredFiles,
  getTempFileInfo,
  
  // وظائف إنشاء الملفات
  createReportFile,
  createExportFile,
  createTempFileFromText,
  createTempFileFromJson
};