import React from 'react';
import { Box, Typo<PERSON>, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import { Add } from '@mui/icons-material';

const Orders: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          إدارة الطلبات
        </Typography>
        <Button variant="contained" startIcon={<Add />}>
          طلب جديد
        </Button>
      </Box>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            قائمة الطلبات
          </Typography>
          <Typography color="text.secondary">
            سيتم تطوير هذه الصفحة قريباً مع جميع وظائف إدارة الطلبات.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Orders;
