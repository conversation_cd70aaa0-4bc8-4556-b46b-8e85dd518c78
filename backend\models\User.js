const mongoose = require('mongoose');

/**
 * نموذج المستخدم
 * يستخدم لتخزين بيانات المستخدمين في النظام مع تحديد الصلاحيات
 */
const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['admin', 'sales', 'production', 'accountant'],
    default: 'sales'
  },
  permissions: {
    customers: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    orders: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    inventory: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    production: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    invoices: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    reports: {
      view: { type: Boolean, default: false }
    },
    users: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    }
  },
  avatar: {
    type: String
  },
  phone: {
    type: String
  },
  date: {
    type: Date,
    default: Date.now
  },
  active: {
    type: Boolean,
    default: true
  }
});

// تعيين الصلاحيات حسب الدور
UserSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('role')) {
    switch (this.role) {
      case 'admin':
        // المدير لديه جميع الصلاحيات
        Object.keys(this.permissions).forEach(module => {
          Object.keys(this.permissions[module]).forEach(action => {
            this.permissions[module][action] = true;
          });
        });
        break;
      case 'sales':
        // موظف المبيعات لديه صلاحيات للعملاء والطلبات والفواتير
        this.permissions.customers = { view: true, create: true, edit: true, delete: false };
        this.permissions.orders = { view: true, create: true, edit: true, delete: false };
        this.permissions.invoices = { view: true, create: true, edit: false, delete: false };
        this.permissions.reports.view = true;
        break;
      case 'production':
        // موظف الإنتاج لديه صلاحيات للطلبات والإنتاج والمخزون
        this.permissions.orders = { view: true, create: false, edit: true, delete: false };
        this.permissions.production = { view: true, create: true, edit: true, delete: false };
        this.permissions.inventory = { view: true, create: false, edit: true, delete: false };
        break;
      case 'accountant':
        // المحاسب لديه صلاحيات للفواتير والتقارير
        this.permissions.customers = { view: true, create: false, edit: false, delete: false };
        this.permissions.orders = { view: true, create: false, edit: false, delete: false };
        this.permissions.invoices = { view: true, create: true, edit: true, delete: false };
        this.permissions.reports.view = true;
        break;
    }
  }
  next();
});

module.exports = mongoose.model('user', UserSchema);