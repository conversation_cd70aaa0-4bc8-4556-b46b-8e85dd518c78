const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../config/config');

/**
 * نموذج المستخدم المحدث
 * يستخدم لتخزين بيانات المستخدمين في النظام مع تحديد الصلاحيات
 */
const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم المستخدم مطلوب'],
    trim: true,
    maxlength: [100, 'اسم المستخدم لا يجب أن يتجاوز 100 حرف']
  },

  email: {
    type: String,
    required: [true, 'البريد الإلكتروني مطلوب'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'يرجى إدخال بريد إلكتروني صحيح'
    ]
  },

  password: {
    type: String,
    required: [true, 'كلمة المرور مطلوبة'],
    minlength: [8, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'],
    select: false
  },

  role: {
    type: String,
    enum: {
      values: ['admin', 'manager', 'accountant', 'sales', 'production', 'inventory', 'customer_service', 'viewer'],
      message: 'دور المستخدم غير صحيح'
    },
    default: 'viewer'
  },

  avatar: {
    type: String,
    default: '/assets/images/default-avatar.png'
  },

  phone: {
    type: String,
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'رقم الهاتف غير صحيح']
  },

  department: {
    type: String,
    trim: true
  },
  permissions: {
    customers: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    orders: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    inventory: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    production: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    invoices: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    reports: {
      view: { type: Boolean, default: false }
    },
    users: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    }
  },
  avatar: {
    type: String
  },
  phone: {
    type: String
  },
  date: {
    type: Date,
    default: Date.now
  },
  active: {
    type: Boolean,
    default: true
  }
});

// تعيين الصلاحيات حسب الدور
UserSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('role')) {
    switch (this.role) {
      case 'admin':
        // المدير لديه جميع الصلاحيات
        Object.keys(this.permissions).forEach(module => {
          Object.keys(this.permissions[module]).forEach(action => {
            this.permissions[module][action] = true;
          });
        });
        break;
      case 'sales':
        // موظف المبيعات لديه صلاحيات للعملاء والطلبات والفواتير
        this.permissions.customers = { view: true, create: true, edit: true, delete: false };
        this.permissions.orders = { view: true, create: true, edit: true, delete: false };
        this.permissions.invoices = { view: true, create: true, edit: false, delete: false };
        this.permissions.reports.view = true;
        break;
      case 'production':
        // موظف الإنتاج لديه صلاحيات للطلبات والإنتاج والمخزون
        this.permissions.orders = { view: true, create: false, edit: true, delete: false };
        this.permissions.production = { view: true, create: true, edit: true, delete: false };
        this.permissions.inventory = { view: true, create: false, edit: true, delete: false };
        break;
      case 'accountant':
        // المحاسب لديه صلاحيات للفواتير والتقارير
        this.permissions.customers = { view: true, create: false, edit: false, delete: false };
        this.permissions.orders = { view: true, create: false, edit: false, delete: false };
        this.permissions.invoices = { view: true, create: true, edit: true, delete: false };
        this.permissions.reports.view = true;
        break;
    }
  }
  next();
});

module.exports = mongoose.model('user', UserSchema);