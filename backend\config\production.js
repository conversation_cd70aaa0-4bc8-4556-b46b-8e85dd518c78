/**
 * ملف الإعدادات للبيئة الإنتاجية
 * يتم دمج هذه الإعدادات مع الإعدادات الافتراضية عند تشغيل التطبيق في بيئة الإنتاج
 */

module.exports = {
  // إعدادات التطبيق الأساسية
  app: {
    baseUrl: process.env.BASE_URL,
    frontendUrl: process.env.FRONTEND_URL,
    port: process.env.PORT || 5000,
    env: 'production',
  },

  // إعدادات قاعدة البيانات
  db: {
    uri: process.env.MONGODB_URI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      autoIndex: false, // تعطيل إنشاء الفهارس تلقائيًا في الإنتاج لتحسين الأداء
    },
  },

  // إعدادات المصادقة والأمان
  auth: {
    jwtSecret: process.env.JWT_SECRET,
    refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET,
    cookieSecure: true,
  },

  // إعدادات التسجيل والمراقبة
  logging: {
    level: process.env.LOG_LEVEL || 'error',
    colorize: false,
  },

  // إعدادات CORS
  cors: {
    origin: process.env.CORS_ORIGIN || process.env.FRONTEND_URL,
  },

  // إعدادات الأمان
  security: {
    rateLimiter: {
      enabled: true,
      windowMs: 15 * 60 * 1000, // 15 دقيقة
      max: 100, // الحد الأقصى للطلبات لكل IP
    },
    helmet: {
      enabled: true,
    },
    xss: {
      enabled: true,
    },
    csrf: {
      enabled: process.env.CSRF_PROTECTION_ENABLED === 'true',
    },
  },
};