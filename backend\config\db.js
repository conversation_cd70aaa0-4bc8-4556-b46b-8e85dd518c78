/**
 * ملف تكوين قاعدة البيانات
 * يقوم بإعداد الاتصال بقاعدة بيانات MongoDB وإدارة الاتصال
 */

const mongoose = require('mongoose');
const config = require('config');
const logger = require('../utils/logger');

// الحصول على رابط الاتصال بقاعدة البيانات من ملف التكوين
const dbURI = config.get('db.uri') || config.get('mongoURI'); // للتوافق مع الإعدادات القديمة
const dbOptions = config.get('db.options') || {
  useNewUrlParser: true,
  useUnifiedTopology: true,
};

/**
 * إعداد الاتصال بقاعدة البيانات
 * @returns {Promise} وعد يتم حله عند نجاح الاتصال أو رفضه عند الفشل
 */
const connectDB = async () => {
  try {
    // تعيين خيارات mongoose العامة
    mongoose.set('strictQuery', true);
    
    // محاولة الاتصال بقاعدة البيانات
    const conn = await mongoose.connect(dbURI, dbOptions);
    
    logger.info ? logger.info(`تم الاتصال بقاعدة البيانات MongoDB: ${conn.connection.host}`) : 
      console.log(`تم الاتصال بقاعدة البيانات MongoDB: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    const errorMsg = `فشل الاتصال بقاعدة البيانات: ${error.message}`;
    logger.error ? logger.error(errorMsg) : console.error(errorMsg);
    // إعادة المحاولة أو إنهاء التطبيق حسب الحاجة
    process.exit(1);
  }
};

/**
 * إغلاق الاتصال بقاعدة البيانات
 * @returns {Promise} وعد يتم حله عند إغلاق الاتصال بنجاح
 */
const closeDB = async () => {
  try {
    await mongoose.connection.close();
    logger.info ? logger.info('تم إغلاق الاتصال بقاعدة البيانات') : 
      console.log('تم إغلاق الاتصال بقاعدة البيانات');
  } catch (error) {
    const errorMsg = `فشل إغلاق الاتصال بقاعدة البيانات: ${error.message}`;
    logger.error ? logger.error(errorMsg) : console.error(errorMsg);
    throw error;
  }
};

/**
 * إعادة تعيين قاعدة البيانات (للاختبارات فقط)
 * @returns {Promise} وعد يتم حله عند إعادة تعيين قاعدة البيانات بنجاح
 */
const resetDB = async () => {
  if (process.env.NODE_ENV !== 'test') {
    throw new Error('لا يمكن إعادة تعيين قاعدة البيانات إلا في بيئة الاختبار');
  }
  
  try {
    const collections = await mongoose.connection.db.collections();
    
    for (const collection of collections) {
      await collection.deleteMany({});
    }
    
    logger.info ? logger.info('تم إعادة تعيين قاعدة البيانات') : 
      console.log('تم إعادة تعيين قاعدة البيانات');
  } catch (error) {
    const errorMsg = `فشل إعادة تعيين قاعدة البيانات: ${error.message}`;
    logger.error ? logger.error(errorMsg) : console.error(errorMsg);
    throw error;
  }
};

/**
 * مراقبة أحداث اتصال قاعدة البيانات
 */
mongoose.connection.on('connected', () => {
  logger.info ? logger.info('Mongoose متصل بقاعدة البيانات') : 
    console.log('Mongoose متصل بقاعدة البيانات');
});

mongoose.connection.on('error', (err) => {
  const errorMsg = `خطأ في اتصال Mongoose: ${err.message}`;
  logger.error ? logger.error(errorMsg) : console.error(errorMsg);
});

mongoose.connection.on('disconnected', () => {
  logger.info ? logger.info('Mongoose غير متصل بقاعدة البيانات') : 
    console.log('Mongoose غير متصل بقاعدة البيانات');
});

// التعامل مع إنهاء العملية بشكل صحيح
process.on('SIGINT', async () => {
  await mongoose.connection.close();
  logger.info ? logger.info('تم إغلاق اتصال Mongoose بسبب إنهاء التطبيق') : 
    console.log('تم إغلاق اتصال Mongoose بسبب إنهاء التطبيق');
  process.exit(0);
});

module.exports = {
  connectDB,
  closeDB,
  resetDB,
};

// للتوافق مع الإصدارات السابقة
module.exports.default = connectDB;