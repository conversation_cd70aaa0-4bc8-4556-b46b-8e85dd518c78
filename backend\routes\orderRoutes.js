const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const orderController = require('../controllers/orderController');
const auth = require('../middleware/auth');
const checkPermission = require('../middleware/checkPermission');

/**
 * @route   POST api/orders
 * @desc    إنشاء طلب جديد
 * @access  Private
 */
router.post(
  '/',
  [
    auth,
    checkPermission('orders', 'create'),
    [
      check('customer', 'معرف العميل مطلوب').not().isEmpty(),
      check('items', 'يجب إضافة عنصر واحد على الأقل').isArray({ min: 1 }),
      check('items.*.productType', 'نوع المنتج مطلوب').not().isEmpty(),
      check('items.*.quantity', 'الكمية مطلوبة').isNumeric(),
      check('items.*.unitPrice', 'سعر الوحدة مطلوب').isNumeric(),
      check('items.*.totalPrice', 'السعر الإجمالي مطلوب').isNumeric()
    ]
  ],
  orderController.createOrder
);

/**
 * @route   GET api/orders
 * @desc    الحصول على قائمة الطلبات
 * @access  Private
 */
router.get(
  '/',
  [auth, checkPermission('orders', 'read')],
  orderController.getOrders
);

/**
 * @route   GET api/orders/:id
 * @desc    الحصول على طلب محدد
 * @access  Private
 */
router.get(
  '/:id',
  [auth, checkPermission('orders', 'read')],
  orderController.getOrderById
);

/**
 * @route   PUT api/orders/:id
 * @desc    تحديث طلب
 * @access  Private
 */
router.put(
  '/:id',
  [
    auth,
    checkPermission('orders', 'update'),
    [
      check('customer', 'معرف العميل مطلوب').optional().not().isEmpty(),
      check('items', 'يجب إضافة عنصر واحد على الأقل').optional().isArray({ min: 1 }),
      check('items.*.productType', 'نوع المنتج مطلوب').optional().not().isEmpty(),
      check('items.*.quantity', 'الكمية مطلوبة').optional().isNumeric(),
      check('items.*.unitPrice', 'سعر الوحدة مطلوب').optional().isNumeric(),
      check('items.*.totalPrice', 'السعر الإجمالي مطلوب').optional().isNumeric()
    ]
  ],
  orderController.updateOrder
);

/**
 * @route   PUT api/orders/status/:id
 * @desc    تحديث حالة الطلب
 * @access  Private
 */
router.put(
  '/status/:id',
  [
    auth,
    checkPermission('orders', 'update'),
    [
      check('status', 'حالة الطلب مطلوبة').not().isEmpty()
    ]
  ],
  orderController.updateOrderStatus
);

/**
 * @route   PUT api/orders/production/:id
 * @desc    تحديث مراحل الإنتاج للطلب
 * @access  Private
 */
router.put(
  '/production/:id',
  [
    auth,
    checkPermission('production', 'update'),
    [
      check('productionStages', 'مراحل الإنتاج مطلوبة').isArray()
    ]
  ],
  orderController.updateProductionStages
);

/**
 * @route   DELETE api/orders/:id
 * @desc    حذف طلب
 * @access  Private
 */
router.delete(
  '/:id',
  [auth, checkPermission('orders', 'delete')],
  orderController.deleteOrder
);

/**
 * @route   GET api/orders/dashboard/stats
 * @desc    الحصول على إحصائيات الطلبات للوحة المعلومات
 * @access  Private
 */
router.get(
  '/dashboard/stats',
  [auth, checkPermission('orders', 'read')],
  orderController.getOrderStats
);

module.exports = router;