/**
 * اختبارات نظام المهام المجدولة
 */

const { createQueue, addJob, getQueue } = require('../config/jobs');
const Bull = require('bull');

// استبدال وظائف Bull بوظائف وهمية للاختبار
jest.mock('bull');

describe('نظام المهام المجدولة', () => {
  beforeEach(() => {
    // إعادة تعيين المحاكاة قبل كل اختبار
    jest.clearAllMocks();
    
    // تهيئة محاكاة Bull
    Bull.mockImplementation(() => ({
      on: jest.fn(),
      add: jest.fn().mockResolvedValue({ id: 'mock-job-id' }),
      process: jest.fn(),
      close: jest.fn().mockResolvedValue(undefined)
    }));
  });
  
  test('إنشاء طابور جديد', () => {
    const queue = createQueue('test-queue');
    
    expect(Bull).toHaveBeenCalledTimes(1);
    expect(Bull).toHaveBeenCalledWith('test-queue', expect.any(Object));
    expect(queue.on).toHaveBeenCalledWith('error', expect.any(Function));
    expect(queue.on).toHaveBeenCalledWith('failed', expect.any(Function));
    expect(queue.on).toHaveBeenCalledWith('completed', expect.any(Function));
  });
  
  test('الحصول على طابور موجود', () => {
    const queue1 = createQueue('existing-queue');
    const queue2 = getQueue('existing-queue');
    
    expect(Bull).toHaveBeenCalledTimes(1);
    expect(queue1).toBe(queue2);
  });
  
  test('إضافة مهمة إلى طابور', async () => {
    const queue = createQueue('job-queue');
    const jobData = { key: 'value' };
    const jobOptions = { priority: 1 };
    
    const job = await addJob('job-queue', 'test-job', jobData, jobOptions);
    
    expect(queue.add).toHaveBeenCalledWith('test-job', jobData, expect.objectContaining({
      priority: 1
    }));
    expect(job.id).toBe('mock-job-id');
  });
  
  test('إضافة مهمة إلى طابور غير موجود', async () => {
    const jobData = { key: 'value' };
    
    const job = await addJob('new-queue', 'test-job', jobData);
    
    expect(Bull).toHaveBeenCalledWith('new-queue', expect.any(Object));
    expect(job.id).toBe('mock-job-id');
  });
});