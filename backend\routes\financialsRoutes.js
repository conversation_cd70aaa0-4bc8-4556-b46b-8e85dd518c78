const express = require('express');
const router = express.Router();
const {
  getFinancials,
  createFinancialRecord,
  getFinancialRecord,
  updateFinancialRecord,
  deleteFinancialRecord,
} = require('../controllers/financialsController');

// Assuming you have authentication middleware
// const { protect, authorize } = require('../middleware/auth');

// router.use(protect);
// router.use(authorize('admin')); // Or appropriate roles

router
    .route('/')
    .get(getFinancials)
    .post(createFinancialRecord);

router
    .route('/:id')
    .get(getFinancialRecord)
    .put(updateFinancialRecord)
    .delete(deleteFinancialRecord);

module.exports = router;
