const fileUpload = require('express-fileupload');
const path = require('path');
const fs = require('fs');
const config = require('config');
const { logger } = require('../utils/logger');

// الحصول على إعدادات تحميل الملفات من ملف التكوين
const uploadPath = config.get('fileUploadPath');
const maxFileSize = config.get('maxFileSize');
const allowedFileTypes = config.get('allowedFileTypes');

// إنشاء مجلد التحميل إذا لم يكن موجودًا
if (!fs.existsSync(uploadPath)) {
  fs.mkdirSync(uploadPath, { recursive: true });
  logger.info(`تم إنشاء مجلد التحميل: ${uploadPath}`);
}

/**
 * وسيط لتكوين تحميل الملفات
 */
const fileUploadMiddleware = fileUpload({
  createParentPath: true,
  limits: { fileSize: maxFileSize },
  abortOnLimit: true,
  responseOnLimit: 'حجم الملف يتجاوز الحد المسموح به',
  limitHandler: (req, res, next) => {
    const error = new Error('حجم الملف يتجاوز الحد المسموح به');
    error.statusCode = 400;
    next(error);
  },
  useTempFiles: true,
  tempFileDir: path.join(uploadPath, 'temp'),
  parseNested: true
});

/**
 * وسيط للتحقق من نوع الملف
 */
const fileTypeValidator = (req, res, next) => {
  // التحقق من وجود ملفات للتحميل
  if (!req.files) {
    return next();
  }

  // التحقق من أنواع الملفات
  const files = req.files;
  const fileKeys = Object.keys(files);

  for (const key of fileKeys) {
    const file = files[key];
    const fileArray = Array.isArray(file) ? file : [file];

    for (const singleFile of fileArray) {
      const fileExtension = path.extname(singleFile.name).toLowerCase().substring(1);
      
      if (!allowedFileTypes.includes(fileExtension)) {
        // حذف الملف المؤقت
        fs.unlinkSync(singleFile.tempFilePath);
        
        const error = new Error(`نوع الملف غير مسموح به. الأنواع المسموح بها: ${allowedFileTypes.join(', ')}`);
        error.statusCode = 400;
        return next(error);
      }
    }
  }

  next();
};

/**
 * وظيفة مساعدة لتحميل ملف
 * @param {Object} file - كائن الملف
 * @param {string} customFileName - اسم الملف المخصص (اختياري)
 * @param {string} subFolder - المجلد الفرعي (اختياري)
 * @returns {Object} معلومات الملف المحمل
 */
const uploadFile = async (file, customFileName = null, subFolder = '') => {
  try {
    // إنشاء المجلد الفرعي إذا تم تحديده
    const targetPath = subFolder ? path.join(uploadPath, subFolder) : uploadPath;
    if (subFolder && !fs.existsSync(targetPath)) {
      fs.mkdirSync(targetPath, { recursive: true });
    }

    // إنشاء اسم الملف
    const fileExtension = path.extname(file.name);
    const fileName = customFileName 
      ? `${customFileName}${fileExtension}` 
      : `${Date.now()}-${Math.round(Math.random() * 1E9)}${fileExtension}`;
    
    // مسار الملف الكامل
    const filePath = path.join(targetPath, fileName);
    
    // نقل الملف من المجلد المؤقت إلى المجلد النهائي
    await file.mv(filePath);
    
    // إنشاء مسار URL للملف
    const fileUrl = subFolder 
      ? `/${path.join('uploads', subFolder, fileName).replace(/\\/g, '/')}` 
      : `/${path.join('uploads', fileName).replace(/\\/g, '/')}`;
    
    return {
      fileName,
      originalName: file.name,
      fileType: file.mimetype,
      fileSize: file.size,
      filePath: filePath.replace(/\\/g, '/'),
      fileUrl
    };
  } catch (error) {
    logger.error('خطأ في تحميل الملف:', error);
    throw new Error('فشل في تحميل الملف');
  }
};

/**
 * وظيفة مساعدة لحذف ملف
 * @param {string} filePath - مسار الملف المراد حذفه
 * @returns {boolean} نجاح العملية
 */
const deleteFile = async (filePath) => {
  try {
    // التحقق من وجود الملف
    if (!filePath) {
      return false;
    }
    
    // إذا كان المسار يبدأ بـ '/'، قم بإزالته
    const normalizedPath = filePath.startsWith('/') ? filePath.substring(1) : filePath;
    
    // إذا كان المسار يبدأ بـ 'uploads/'، قم بإزالته واستخدم مسار التحميل الأساسي
    const fullPath = normalizedPath.startsWith('uploads/') 
      ? path.join(process.cwd(), normalizedPath) 
      : path.join(uploadPath, path.basename(normalizedPath));
    
    // التحقق من وجود الملف قبل الحذف
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
      logger.info(`تم حذف الملف: ${fullPath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    logger.error('خطأ في حذف الملف:', error);
    return false;
  }
};

module.exports = {
  fileUploadMiddleware,
  fileTypeValidator,
  uploadFile,
  deleteFile,
  uploadPath
};