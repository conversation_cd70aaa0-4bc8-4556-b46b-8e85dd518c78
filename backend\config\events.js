/**
 * ملف إعداد وتكوين الأحداث
 * يقوم بإعداد وتكوين نظام الأحداث للتطبيق
 */

const EventEmitter = require('events');
const config = require('config');
const logger = require('../utils/logger');
const { recordError } = require('./monitoring');
const { NOTIFICATION_TYPES, sendNotification } = require('./notifications');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

// إنشاء مُصدر الأحداث
const eventEmitter = new EventEmitter();

// زيادة الحد الأقصى للمستمعين لتجنب تحذيرات الذاكرة
eventEmitter.setMaxListeners(50);

// أنواع الأحداث
const EVENT_TYPES = {
  // أحداث المستخدمين
  USER_REGISTERED: 'user.registered',
  USER_UPDATED: 'user.updated',
  USER_DELETED: 'user.deleted',
  USER_PASSWORD_CHANGED: 'user.password.changed',
  USER_PASSWORD_RESET: 'user.password.reset',
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  USER_ROLE_CHANGED: 'user.role.changed',
  
  // أحداث العملاء
  CUSTOMER_CREATED: 'customer.created',
  CUSTOMER_UPDATED: 'customer.updated',
  CUSTOMER_DELETED: 'customer.deleted',
  
  // أحداث الطلبات
  ORDER_CREATED: 'order.created',
  ORDER_UPDATED: 'order.updated',
  ORDER_STATUS_CHANGED: 'order.status.changed',
  ORDER_COMPLETED: 'order.completed',
  ORDER_CANCELLED: 'order.cancelled',
  ORDER_PAYMENT_RECEIVED: 'order.payment.received',
  
  // أحداث المنتجات والمخزون
  PRODUCT_CREATED: 'product.created',
  PRODUCT_UPDATED: 'product.updated',
  PRODUCT_DELETED: 'product.deleted',
  INVENTORY_UPDATED: 'inventory.updated',
  INVENTORY_LOW: 'inventory.low',
  INVENTORY_OUT_OF_STOCK: 'inventory.out_of_stock',
  
  // أحداث الفواتير والمدفوعات
  INVOICE_CREATED: 'invoice.created',
  INVOICE_UPDATED: 'invoice.updated',
  INVOICE_PAID: 'invoice.paid',
  INVOICE_OVERDUE: 'invoice.overdue',
  PAYMENT_CREATED: 'payment.created',
  PAYMENT_UPDATED: 'payment.updated',
  
  // أحداث الموردين
  SUPPLIER_CREATED: 'supplier.created',
  SUPPLIER_UPDATED: 'supplier.updated',
  SUPPLIER_DELETED: 'supplier.deleted',
  
  // أحداث النظام
  SYSTEM_ERROR: 'system.error',
  SYSTEM_WARNING: 'system.warning',
  SYSTEM_INFO: 'system.info',
  SYSTEM_BACKUP_COMPLETED: 'system.backup.completed',
  SYSTEM_BACKUP_FAILED: 'system.backup.failed',
  SYSTEM_MAINTENANCE_STARTED: 'system.maintenance.started',
  SYSTEM_MAINTENANCE_COMPLETED: 'system.maintenance.completed',
};

/**
 * إرسال حدث
 * @param {string} eventType - نوع الحدث
 * @param {Object} data - بيانات الحدث
 * @param {Object} options - خيارات إضافية
 * @returns {boolean} نجاح العملية
 */
const emitEvent = (eventType, data = {}, options = {}) => {
  try {
    // التحقق من صحة نوع الحدث
    if (!Object.values(EVENT_TYPES).includes(eventType)) {
      logger.warn(`نوع حدث غير معروف: ${eventType}`);
    }
    
    // إعداد بيانات الحدث
    const eventData = {
      type: eventType,
      data,
      timestamp: options.timestamp || new Date(),
      source: options.source || 'system',
      metadata: options.metadata || {},
    };
    
    // تسجيل الحدث
    logger.debug(`إرسال حدث: ${eventType}`, {
      eventType,
      dataKeys: Object.keys(data),
      source: eventData.source,
    });
    
    // إرسال الحدث
    eventEmitter.emit(eventType, eventData);
    
    // إرسال الحدث العام
    eventEmitter.emit('*', eventData);
    
    return true;
  } catch (error) {
    logger.error(`فشل في إرسال الحدث: ${eventType}`, error);
    recordError(error);
    return false;
  }
};

/**
 * إضافة مستمع للأحداث
 * @param {string} eventType - نوع الحدث
 * @param {Function} listenerFunction - دالة المستمع
 * @param {Object} options - خيارات إضافية
 * @returns {Function} دالة لإزالة المستمع
 */
const addListener = (eventType, listenerFunction, options = {}) => {
  try {
    if (typeof listenerFunction !== 'function') {
      throw new Error(`دالة المستمع غير صالحة للحدث: ${eventType}`);
    }
    
    // إنشاء دالة المستمع المغلفة
    const wrappedListener = async (eventData) => {
      const startTime = Date.now();
      
      try {
        await listenerFunction(eventData);
        const duration = Date.now() - startTime;
        
        if (duration > 1000) {
          logger.warn(`مستمع الحدث ${eventType} استغرق وقتًا طويلاً: ${duration}ms`);
        }
      } catch (error) {
        logger.error(`فشل في تنفيذ مستمع الحدث: ${eventType}`, error);
        recordError(error);
      }
    };
    
    // تخزين المستمع الأصلي للإشارة إليه عند الإزالة
    wrappedListener.originalListener = listenerFunction;
    
    // إضافة المستمع
    if (options.once) {
      eventEmitter.once(eventType, wrappedListener);
    } else {
      eventEmitter.on(eventType, wrappedListener);
    }
    
    logger.debug(`تم إضافة مستمع للحدث: ${eventType}`);
    
    // إرجاع دالة لإزالة المستمع
    return () => {
      eventEmitter.removeListener(eventType, wrappedListener);
      logger.debug(`تم إزالة مستمع الحدث: ${eventType}`);
    };
  } catch (error) {
    logger.error(`فشل في إضافة مستمع للحدث: ${eventType}`, error);
    recordError(error);
    throw error;
  }
};

/**
 * إضافة مستمع لمرة واحدة
 * @param {string} eventType - نوع الحدث
 * @param {Function} listenerFunction - دالة المستمع
 * @param {Object} options - خيارات إضافية
 * @returns {Function} دالة لإزالة المستمع
 */
const addOnceListener = (eventType, listenerFunction, options = {}) => {
  return addListener(eventType, listenerFunction, { ...options, once: true });
};

/**
 * إزالة جميع المستمعين لنوع حدث معين
 * @param {string} eventType - نوع الحدث
 * @returns {boolean} نجاح العملية
 */
const removeAllListeners = (eventType) => {
  try {
    eventEmitter.removeAllListeners(eventType);
    logger.debug(`تم إزالة جميع مستمعي الحدث: ${eventType}`);
    return true;
  } catch (error) {
    logger.error(`فشل في إزالة جميع مستمعي الحدث: ${eventType}`, error);
    recordError(error);
    return false;
  }
};

/**
 * الحصول على عدد المستمعين لنوع حدث معين
 * @param {string} eventType - نوع الحدث
 * @returns {number} عدد المستمعين
 */
const getListenerCount = (eventType) => {
  try {
    return eventEmitter.listenerCount(eventType);
  } catch (error) {
    logger.error(`فشل في الحصول على عدد مستمعي الحدث: ${eventType}`, error);
    recordError(error);
    return 0;
  }
};

/**
 * إعداد المستمعين الافتراضيين
 */
const setupDefaultListeners = () => {
  try {
    // ربط أحداث المستخدمين بالإشعارات
    addListener(EVENT_TYPES.USER_REGISTERED, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.USER_REGISTERED, eventData.data);
    });
    
    addListener(EVENT_TYPES.USER_PASSWORD_RESET, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.USER_PASSWORD_RESET, eventData.data);
    });
    
    // ربط أحداث العملاء بالإشعارات
    addListener(EVENT_TYPES.CUSTOMER_CREATED, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.CUSTOMER_CREATED, eventData.data);
    });
    
    // ربط أحداث الطلبات بالإشعارات
    addListener(EVENT_TYPES.ORDER_CREATED, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.ORDER_CREATED, eventData.data);
    });
    
    addListener(EVENT_TYPES.ORDER_STATUS_CHANGED, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.ORDER_STATUS_CHANGED, eventData.data);
    });
    
    addListener(EVENT_TYPES.ORDER_COMPLETED, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.ORDER_COMPLETED, eventData.data);
    });
    
    // ربط أحداث المخزون بالإشعارات
    addListener(EVENT_TYPES.INVENTORY_LOW, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.INVENTORY_LOW, eventData.data);
    });
    
    addListener(EVENT_TYPES.INVENTORY_OUT_OF_STOCK, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.INVENTORY_OUT_OF_STOCK, eventData.data);
    });
    
    // ربط أحداث الفواتير بالإشعارات
    addListener(EVENT_TYPES.INVOICE_CREATED, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.INVOICE_CREATED, eventData.data);
    });
    
    addListener(EVENT_TYPES.INVOICE_OVERDUE, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.INVOICE_OVERDUE, eventData.data);
    });
    
    // ربط أحداث النظام بالإشعارات
    addListener(EVENT_TYPES.SYSTEM_ERROR, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.SYSTEM_ERROR, eventData.data);
    });
    
    addListener(EVENT_TYPES.SYSTEM_BACKUP_COMPLETED, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.SYSTEM_BACKUP_COMPLETED, eventData.data);
    });
    
    addListener(EVENT_TYPES.SYSTEM_BACKUP_FAILED, async (eventData) => {
      await sendNotification(NOTIFICATION_TYPES.SYSTEM_BACKUP_FAILED, eventData.data);
    });
    
    // مستمع عام لتسجيل جميع الأحداث
    addListener('*', (eventData) => {
      logger.debug('حدث عام', {
        type: eventData.type,
        timestamp: eventData.timestamp,
        source: eventData.source,
      });
    });
    
    logger.info('تم إعداد المستمعين الافتراضيين للأحداث');
  } catch (error) {
    logger.error('فشل في إعداد المستمعين الافتراضيين للأحداث', error);
    recordError(error);
  }
};

/**
 * إعداد نظام الأحداث
 */
const setupEvents = () => {
  try {
    // إعداد المستمعين الافتراضيين
    setupDefaultListeners();
    
    logger.info('تم إعداد نظام الأحداث بنجاح');
    return true;
  } catch (error) {
    logger.error('فشل في إعداد نظام الأحداث', error);
    recordError(error);
    return false;
  }
};

module.exports = {
  // الثوابت
  EVENT_TYPES,
  
  // وظائف الأحداث
  emitEvent,
  addListener,
  addOnceListener,
  removeAllListeners,
  getListenerCount,
  
  // وظائف الإعداد
  setupEvents,
  setupDefaultListeners,
  
  // كائن مُصدر الأحداث
  eventEmitter,
};