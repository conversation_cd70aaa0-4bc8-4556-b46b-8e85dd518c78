# دليل المساهمة في نظام مطبعة متطور

شكراً لاهتمامك بالمساهمة في تطوير نظام مطبعة متطور! هذا الدليل يوضح كيفية المساهمة في المشروع بشكل فعال.

## كيفية المساهمة

### الإبلاغ عن الأخطاء

1. تأكد من أن الخطأ لم يتم الإبلاغ عنه مسبقاً في قسم المشكلات (Issues).
2. استخدم نموذج الإبلاغ عن الأخطاء لتقديم تقرير مفصل.
3. قدم خطوات واضحة لإعادة إنتاج المشكلة.
4. أرفق لقطات شاشة إذا كان ذلك ممكناً.

### اقتراح تحسينات

1. افتح مشكلة جديدة (Issue) باستخدام نموذج اقتراح الميزات.
2. صف الميزة المقترحة بالتفصيل.
3. وضح كيف ستفيد هذه الميزة المستخدمين.

### تقديم تغييرات برمجية

1. قم بعمل نسخة (Fork) من المستودع.
2. قم بإنشاء فرع (Branch) جديد لميزتك:
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. قم بإجراء التغييرات اللازمة.
4. تأكد من اتباع معايير الترميز وكتابة اختبارات مناسبة.
5. قم بعمل Commit للتغييرات:
   ```bash
   git commit -m 'إضافة ميزة رائعة'
   ```
6. قم بدفع التغييرات إلى الفرع الخاص بك:
   ```bash
   git push origin feature/amazing-feature
   ```
7. قم بفتح طلب سحب (Pull Request) إلى الفرع الرئيسي للمشروع.

## معايير الترميز

### عام
- استخدم مسافتين للإزاحة (Indentation).
- استخدم نهاية السطر LF (Unix-style).
- تجنب ترك مسافات فارغة في نهاية الأسطر.

### JavaScript / Node.js
- اتبع معايير ESLint المحددة في المشروع.
- استخدم ES6+ حيثما أمكن.
- اكتب تعليقات JSDoc للدوال والفئات.

### React
- استخدم مكونات وظيفية (Functional Components) مع Hooks.
- اتبع نمط تنظيم المكونات المحدد في المشروع.
- استخدم PropTypes أو TypeScript للتحقق من أنواع البيانات.

### CSS / SCSS
- استخدم نهج CSS Modules أو Styled Components.
- اتبع تسمية BEM إذا كنت تستخدم CSS التقليدي.

## عملية المراجعة

1. سيتم مراجعة طلبات السحب من قبل مشرفي المشروع.
2. قد يُطلب منك إجراء تغييرات قبل دمج التعديلات.
3. تأكد من أن جميع الاختبارات تمر بنجاح.
4. يجب أن تكون التغييرات متوافقة مع أهداف المشروع.

## الاختبارات

- اكتب اختبارات لجميع الميزات الجديدة.
- تأكد من أن الاختبارات الحالية تمر بنجاح.
- استخدم أدوات الاختبار المحددة في المشروع (Jest, React Testing Library, إلخ).

## الإصدارات
\ننتهج نهج الإصدار الدلالي (Semantic Versioning):
- **Major.Minor.Patch** (مثال: 1.2.3)
- **Major**: تغييرات غير متوافقة مع الإصدارات السابقة
- **Minor**: إضافة ميزات جديدة بطريقة متوافقة
- **Patch**: إصلاحات للأخطاء بطريقة متوافقة

## الاتصال

إذا كان لديك أي أسئلة حول كيفية المساهمة، يرجى التواصل معنا عبر:
- البريد الإلكتروني: <EMAIL>

## مدونة قواعد السلوك
\ننتظر من جميع المساهمين الالتزام بمدونة قواعد السلوك الخاصة بنا. يرجى قراءتها قبل المساهمة.

---

شكراً لمساهمتك في تحسين نظام مطبعة متطور!