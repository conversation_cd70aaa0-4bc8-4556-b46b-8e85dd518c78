/**
 * ملف إعداد وتكوين التخزين المؤقت (Caching)
 * يقوم بإعداد وتكوين نظام التخزين المؤقت للتطبيق
 */

const NodeCache = require('node-cache');
const Redis = require('ioredis');
const config = require('config');
const { logger } = require('./logging');

// الحصول على إعدادات التخزين المؤقت من ملف التكوين
let cacheConfig;
try {
  cacheConfig = config.get('cache');
} catch (error) {
  // استخدام الإعدادات الافتراضية إذا لم يتم العثور على إعدادات في ملف التكوين
  cacheConfig = {
    driver: process.env.CACHE_DRIVER || 'memory',
    prefix: process.env.CACHE_PREFIX || 'print_system:',
    ttl: parseInt(process.env.CACHE_TTL, 10) || 3600, // الوقت الافتراضي للانتهاء بالثواني (ساعة واحدة)
    memory: {
      checkperiod: 600, // فترة التحقق من انتهاء صلاحية العناصر بالثواني (10 دقائق)
      maxKeys: -1 // بلا حدود
    },
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT, 10) || 6379,
      password: process.env.REDIS_PASSWORD || '',
      db: parseInt(process.env.REDIS_DB, 10) || 0,
      keyPrefix: process.env.CACHE_PREFIX || 'print_system:'
    }
  };
}

// إنشاء كائن التخزين المؤقت
let cacheClient;

/**
 * تهيئة نظام التخزين المؤقت
 * @returns {Object} كائن التخزين المؤقت
 */
const initCache = () => {
  if (cacheClient) {
    return cacheClient;
  }
  
  try {
    if (cacheConfig.driver === 'redis') {
      // استخدام Redis كمحرك للتخزين المؤقت
      const redisClient = new Redis({
        host: cacheConfig.redis.host,
        port: cacheConfig.redis.port,
        password: cacheConfig.redis.password || undefined,
        db: cacheConfig.redis.db,
        keyPrefix: cacheConfig.redis.keyPrefix,
        retryStrategy: (times) => {
          const delay = Math.min(times * 50, 2000);
          return delay;
        }
      });
      
      redisClient.on('connect', () => {
        logger.info('تم الاتصال بخادم Redis بنجاح');
      });
      
      redisClient.on('error', (err) => {
        logger.error(`خطأ في الاتصال بخادم Redis: ${err.message}`, { error: err });
      });
      
      cacheClient = {
        type: 'redis',
        client: redisClient,
        async get(key) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          const value = await redisClient.get(fullKey);
          if (value) {
            try {
              return JSON.parse(value);
            } catch (e) {
              return value;
            }
          }
          return null;
        },
        async set(key, value, ttl = cacheConfig.ttl) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          const stringValue = typeof value === 'object' ? JSON.stringify(value) : value;
          if (ttl) {
            await redisClient.set(fullKey, stringValue, 'EX', ttl);
          } else {
            await redisClient.set(fullKey, stringValue);
          }
          return true;
        },
        async del(key) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          await redisClient.del(fullKey);
          return true;
        },
        async has(key) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          return (await redisClient.exists(fullKey)) === 1;
        },
        async clear() {
          const keys = await redisClient.keys(`${cacheConfig.prefix}*`);
          if (keys.length > 0) {
            await redisClient.del(keys);
          }
          return true;
        },
        async keys() {
          const keys = await redisClient.keys(`${cacheConfig.prefix}*`);
          return keys.map(key => key.replace(cacheConfig.prefix, ''));
        },
        async ttl(key) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          return await redisClient.ttl(fullKey);
        },
        async close() {
          await redisClient.quit();
          logger.info('تم إغلاق اتصال Redis بنجاح');
        }
      };
    } else {
      // استخدام الذاكرة كمحرك للتخزين المؤقت (الافتراضي)
      const nodeCache = new NodeCache({
        stdTTL: cacheConfig.ttl,
        checkperiod: cacheConfig.memory.checkperiod,
        maxKeys: cacheConfig.memory.maxKeys
      });
      
      cacheClient = {
        type: 'memory',
        client: nodeCache,
        async get(key) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          return nodeCache.get(fullKey);
        },
        async set(key, value, ttl = cacheConfig.ttl) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          return nodeCache.set(fullKey, value, ttl);
        },
        async del(key) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          return nodeCache.del(fullKey);
        },
        async has(key) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          return nodeCache.has(fullKey);
        },
        async clear() {
          return nodeCache.flushAll();
        },
        async keys() {
          const keys = nodeCache.keys();
          return keys.map(key => key.replace(cacheConfig.prefix, ''));
        },
        async ttl(key) {
          const fullKey = `${cacheConfig.prefix}${key}`;
          const ttl = nodeCache.getTtl(fullKey);
          if (!ttl) return -1;
          const now = Date.now();
          return Math.round((ttl - now) / 1000);
        },
        async close() {
          nodeCache.close();
          logger.info('تم إغلاق التخزين المؤقت في الذاكرة بنجاح');
        }
      };
    }
    
    logger.info(`تم تهيئة نظام التخزين المؤقت بنجاح (${cacheClient.type})`);
    return cacheClient;
  } catch (error) {
    logger.error(`خطأ في تهيئة نظام التخزين المؤقت: ${error.message}`, { error });
    throw error;
  }
};

/**
 * الحصول على كائن التخزين المؤقت
 * @returns {Object} كائن التخزين المؤقت
 */
const getCache = () => {
  if (!cacheClient) {
    return initCache();
  }
  return cacheClient;
};

/**
 * إغلاق اتصال التخزين المؤقت
 */
const closeCache = async () => {
  if (cacheClient) {
    await cacheClient.close();
    cacheClient = null;
  }
};

/**
 * دالة مساعدة لتخزين نتائج الدوال في التخزين المؤقت
 * @param {Function} fn - الدالة المراد تخزين نتائجها
 * @param {string} key - مفتاح التخزين المؤقت
 * @param {number} ttl - وقت انتهاء الصلاحية بالثواني
 * @returns {any} نتيجة الدالة
 */
const cacheResult = async (fn, key, ttl = cacheConfig.ttl) => {
  const cache = getCache();
  const cachedResult = await cache.get(key);
  
  if (cachedResult !== null && cachedResult !== undefined) {
    return cachedResult;
  }
  
  const result = await fn();
  await cache.set(key, result, ttl);
  return result;
};

/**
 * وسيط Express لتخزين استجابات API في التخزين المؤقت
 * @param {number} ttl - وقت انتهاء الصلاحية بالثواني
 * @returns {Function} وسيط Express
 */
const cacheMiddleware = (ttl = cacheConfig.ttl) => {
  return async (req, res, next) => {
    // تجاهل التخزين المؤقت لطلبات غير GET
    if (req.method !== 'GET') {
      return next();
    }
    
    // إنشاء مفتاح فريد للطلب
    const key = `api:${req.originalUrl || req.url}`;
    const cache = getCache();
    
    try {
      // التحقق من وجود الاستجابة في التخزين المؤقت
      const cachedResponse = await cache.get(key);
      
      if (cachedResponse) {
        // إرسال الاستجابة المخزنة مؤقتًا
        res.setHeader('X-Cache', 'HIT');
        return res.status(cachedResponse.status).json(cachedResponse.body);
      }
      
      // تخزين الاستجابة الأصلية
      const originalSend = res.json;
      
      res.json = function(body) {
        // تخزين الاستجابة في التخزين المؤقت
        const response = {
          status: res.statusCode,
          body: body
        };
        
        // لا تخزن الاستجابات التي تحتوي على أخطاء
        if (res.statusCode < 400) {
          cache.set(key, response, ttl);
        }
        
        res.setHeader('X-Cache', 'MISS');
        return originalSend.call(this, body);
      };
      
      next();
    } catch (error) {
      logger.error(`خطأ في وسيط التخزين المؤقت: ${error.message}`, { error });
      next();
    }
  };
};

/**
 * إنشاء مفتاح تخزين مؤقت من المعلمات
 * @param {string} prefix - بادئة المفتاح
 * @param {Object} params - معلمات المفتاح
 * @returns {string} مفتاح التخزين المؤقت
 */
const createCacheKey = (prefix, params) => {
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {});
  
  return `${prefix}:${JSON.stringify(sortedParams)}`;
};

/**
 * إزالة مجموعة من المفاتيح من التخزين المؤقت باستخدام نمط
 * @param {string} pattern - نمط المفاتيح المراد إزالتها
 * @returns {Promise<number>} عدد المفاتيح التي تمت إزالتها
 */
const invalidatePattern = async (pattern) => {
  const cache = getCache();
  
  if (cache.type === 'redis') {
    const keys = await cache.client.keys(`${cacheConfig.prefix}${pattern}*`);
    if (keys.length > 0) {
      await cache.client.del(keys);
    }
    return keys.length;
  } else {
    const allKeys = cache.client.keys();
    const matchingKeys = allKeys.filter(key => {
      const keyWithoutPrefix = key.replace(cacheConfig.prefix, '');
      return keyWithoutPrefix.startsWith(pattern);
    });
    
    if (matchingKeys.length > 0) {
      cache.client.del(matchingKeys);
    }
    return matchingKeys.length;
  }
};

/**
 * الحصول على إحصائيات التخزين المؤقت
 * @returns {Promise<Object>} إحصائيات التخزين المؤقت
 */
const getCacheStats = async () => {
  const cache = getCache();
  
  if (cache.type === 'redis') {
    const info = await cache.client.info();
    const dbInfo = await cache.client.info('keyspace');
    const keys = await cache.client.keys(`${cacheConfig.prefix}*`);
    
    return {
      type: 'redis',
      keys: keys.length,
      info: info,
      dbInfo: dbInfo
    };
  } else {
    const stats = cache.client.getStats();
    const keys = cache.client.keys();
    
    return {
      type: 'memory',
      keys: keys.length,
      hits: stats.hits,
      misses: stats.misses,
      ksize: stats.ksize,
      vsize: stats.vsize
    };
  }
};

// تصدير الدوال والمتغيرات
module.exports = {
  initCache,
  getCache,
  closeCache,
  cacheResult,
  cacheMiddleware,
  createCacheKey,
  invalidatePattern,
  getCacheStats
};