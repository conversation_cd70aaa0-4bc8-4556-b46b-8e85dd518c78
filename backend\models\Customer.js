const mongoose = require('mongoose');

/**
 * نموذج العميل
 * يستخدم لتخزين بيانات العملاء في النظام
 */
const CustomerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  contactPerson: {
    type: String
  },
  email: {
    type: String,
    required: true
  },
  phone: {
    type: String,
    required: true
  },
  address: {
    street: { type: String },
    city: { type: String },
    state: { type: String },
    postalCode: { type: String },
    country: { type: String, default: 'المملكة العربية السعودية' }
  },
  taxNumber: {
    type: String
  },
  customerType: {
    type: String,
    enum: ['individual', 'company', 'government'],
    default: 'individual'
  },
  notes: {
    type: String
  },
  paymentTerms: {
    type: String
  },
  creditLimit: {
    type: Number,
    default: 0
  },
  active: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// تحديث تاريخ التعديل عند تحديث البيانات
CustomerSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// إضافة طريقة للحصول على ملخص العميل
CustomerSchema.methods.getSummary = function() {
  return {
    id: this._id,
    name: this.name,
    email: this.email,
    phone: this.phone,
    type: this.customerType
  };
};

// إضافة طريقة للحصول على عنوان العميل كاملاً
CustomerSchema.methods.getFullAddress = function() {
  const { street, city, state, postalCode, country } = this.address;
  const addressParts = [street, city, state, postalCode, country].filter(Boolean);
  return addressParts.join('، ');
};

module.exports = mongoose.model('customer', CustomerSchema);