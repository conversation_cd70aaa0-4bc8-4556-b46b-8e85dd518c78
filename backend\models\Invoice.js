const mongoose = require('mongoose');

/**
 * نموذج الفاتورة
 * يستخدم لتخزين بيانات الفواتير في النظام
 */
const InvoiceSchema = new mongoose.Schema({
  invoiceNumber: {
    type: String,
    required: true,
    unique: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'customer',
    required: true
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'order',
    required: true
  },
  issueDate: {
    type: Date,
    default: Date.now,
    required: true
  },
  dueDate: {
    type: Date,
    required: true
  },
  items: [{
    description: {
      type: String,
      required: true
    },
    quantity: {
      type: Number,
      required: true
    },
    unitPrice: {
      type: Number,
      required: true
    },
    totalPrice: {
      type: Number,
      required: true
    }
  }],
  subtotal: {
    type: Number,
    required: true
  },
  taxRate: {
    type: Number,
    default: 15 // ضريبة القيمة المضافة 15%
  },
  taxAmount: {
    type: Number,
    required: true
  },
  discount: {
    type: Number,
    default: 0
  },
  totalAmount: {
    type: Number,
    required: true
  },
  notes: {
    type: String
  },
  paymentTerms: {
    type: String
  },
  status: {
    type: String,
    enum: ['draft', 'issued', 'paid', 'partially_paid', 'overdue', 'cancelled'],
    default: 'draft'
  },
  payments: [{
    amount: {
      type: Number,
      required: true
    },
    paymentDate: {
      type: Date,
      default: Date.now
    },
    paymentMethod: {
      type: String,
      enum: ['cash', 'bank_transfer', 'credit_card', 'check', 'other'],
      required: true
    },
    reference: {
      type: String
    },
    notes: {
      type: String
    },
    receivedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'user'
    }
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// تحديث تاريخ التعديل عند تحديث البيانات
InvoiceSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// إنشاء رقم الفاتورة تلقائياً
InvoiceSchema.pre('save', async function(next) {
  if (this.isNew && !this.invoiceNumber) {
    try {
      // الحصول على آخر فاتورة
      const lastInvoice = await this.constructor.findOne({}, {}, { sort: { 'createdAt': -1 } });
      
      // إنشاء رقم الفاتورة الجديد
      const year = new Date().getFullYear().toString();
      const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
      
      if (lastInvoice && lastInvoice.invoiceNumber) {
        // استخراج الرقم التسلسلي من آخر فاتورة
        const lastNumber = parseInt(lastInvoice.invoiceNumber.split('-')[2]);
        this.invoiceNumber = `INV-${year}${month}-${(lastNumber + 1).toString().padStart(4, '0')}`;
      } else {
        // إذا لم يكن هناك فواتير سابقة
        this.invoiceNumber = `INV-${year}${month}-0001`;
      }
    } catch (err) {
      return next(err);
    }
  }
  next();
});

// حساب المبالغ تلقائياً
InvoiceSchema.pre('save', function(next) {
  if (this.isModified('items') || this.isNew) {
    // حساب المجموع الفرعي
    this.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
    
    // حساب مبلغ الضريبة
    this.taxAmount = (this.subtotal - this.discount) * (this.taxRate / 100);
    
    // حساب المبلغ الإجمالي
    this.totalAmount = this.subtotal - this.discount + this.taxAmount;
  }
  
  // تحديث حالة الفاتورة بناءً على المدفوعات
  if (this.isModified('payments') || this.isNew) {
    const totalPaid = this.payments.reduce((sum, payment) => sum + payment.amount, 0);
    
    if (totalPaid === 0 && this.status !== 'draft' && this.status !== 'cancelled') {
      this.status = 'issued';
    } else if (totalPaid > 0 && totalPaid < this.totalAmount) {
      this.status = 'partially_paid';
    } else if (totalPaid >= this.totalAmount) {
      this.status = 'paid';
    }
    
    // التحقق من تجاوز تاريخ الاستحقاق
    if (this.status !== 'paid' && this.status !== 'cancelled' && this.dueDate < new Date()) {
      this.status = 'overdue';
    }
  }
  
  next();
});

// إضافة طريقة لإضافة دفعة جديدة
InvoiceSchema.methods.addPayment = function(amount, paymentMethod, reference, notes, userId) {
  // التحقق من أن المبلغ المدفوع لا يتجاوز المبلغ المتبقي
  const totalPaid = this.payments.reduce((sum, payment) => sum + payment.amount, 0);
  const remainingAmount = this.totalAmount - totalPaid;
  
  if (amount > remainingAmount) {
    throw new Error('المبلغ المدفوع يتجاوز المبلغ المتبقي');
  }
  
  // إضافة الدفعة
  this.payments.push({
    amount,
    paymentDate: new Date(),
    paymentMethod,
    reference,
    notes,
    receivedBy: userId
  });
  
  return this.save();
};

// إضافة طريقة للحصول على المبلغ المدفوع
InvoiceSchema.methods.getPaidAmount = function() {
  return this.payments.reduce((sum, payment) => sum + payment.amount, 0);
};

// إضافة طريقة للحصول على المبلغ المتبقي
InvoiceSchema.methods.getRemainingAmount = function() {
  const paidAmount = this.getPaidAmount();
  return this.totalAmount - paidAmount;
};

module.exports = mongoose.model('invoice', InvoiceSchema);