import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert
} from '@mui/material';
import {
  People,
  PersonAdd,
  Schedule,
  EventNote,
  AttachMoney,
  Visibility,
  Edit,
  Delete,
  CheckCircle,
  Cancel,
  AccessTime,
  Work
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`hr-tabpanel-${index}`}
      aria-labelledby={`hr-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const HRDashboard: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<'employee' | 'leave' | 'attendance'>('employee');

  // بيانات وهمية للموظفين
  const [employees] = useState([
    {
      id: '1',
      employeeId: 'EMP001',
      name: 'أحمد محمد',
      department: 'الإنتاج',
      position: 'مشغل آلة طباعة',
      status: 'active',
      hireDate: '2023-01-15',
      salary: 4500,
      avatar: '/avatars/ahmed.jpg'
    },
    {
      id: '2',
      employeeId: 'EMP002',
      name: 'فاطمة علي',
      department: 'المبيعات',
      position: 'مندوبة مبيعات',
      status: 'active',
      hireDate: '2023-03-20',
      salary: 4000,
      avatar: '/avatars/fatima.jpg'
    },
    {
      id: '3',
      employeeId: 'EMP003',
      name: 'محمد سعد',
      department: 'المحاسبة',
      position: 'محاسب',
      status: 'active',
      hireDate: '2022-11-10',
      salary: 5000,
      avatar: '/avatars/mohammed.jpg'
    }
  ]);

  // بيانات وهمية للإجازات
  const [leaves] = useState([
    {
      id: '1',
      employeeName: 'أحمد محمد',
      type: 'annual',
      startDate: '2024-01-15',
      endDate: '2024-01-20',
      duration: 5,
      status: 'pending',
      reason: 'إجازة سنوية'
    },
    {
      id: '2',
      employeeName: 'فاطمة علي',
      type: 'sick',
      startDate: '2024-01-10',
      endDate: '2024-01-12',
      duration: 2,
      status: 'approved',
      reason: 'إجازة مرضية'
    }
  ]);

  // بيانات وهمية للحضور
  const [attendance] = useState([
    {
      id: '1',
      employeeName: 'أحمد محمد',
      date: '2024-01-08',
      checkIn: '08:00',
      checkOut: '17:00',
      workingHours: 8,
      status: 'present'
    },
    {
      id: '2',
      employeeName: 'فاطمة علي',
      date: '2024-01-08',
      checkIn: '08:15',
      checkOut: '17:00',
      workingHours: 7.75,
      status: 'late'
    }
  ]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'pending': return 'warning';
      case 'approved': return 'success';
      case 'rejected': return 'error';
      case 'present': return 'success';
      case 'late': return 'warning';
      case 'absent': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'pending': return 'معلق';
      case 'approved': return 'موافق عليه';
      case 'rejected': return 'مرفوض';
      case 'present': return 'حاضر';
      case 'late': return 'متأخر';
      case 'absent': return 'غائب';
      default: return status;
    }
  };

  const getLeaveTypeText = (type: string) => {
    switch (type) {
      case 'annual': return 'سنوية';
      case 'sick': return 'مرضية';
      case 'emergency': return 'طارئة';
      case 'maternity': return 'أمومة';
      case 'paternity': return 'أبوة';
      default: return type;
    }
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* العنوان */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          الموارد البشرية
        </Typography>
        <Button
          variant="contained"
          startIcon={<PersonAdd />}
          onClick={() => {
            setDialogType('employee');
            setOpenDialog(true);
          }}
        >
          إضافة موظف جديد
        </Button>
      </Box>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    إجمالي الموظفين
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                    {employees.length}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <People />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    الحضور اليوم
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'success.main' }}>
                    {attendance.filter(a => a.status === 'present').length}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <Schedule />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    الإجازات المعلقة
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'warning.main' }}>
                    {leaves.filter(l => l.status === 'pending').length}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <EventNote />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    إجمالي الرواتب
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'info.main' }}>
                    {employees.reduce((sum, emp) => sum + emp.salary, 0).toLocaleString('ar-SA')}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* التبويبات */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="الموظفين" icon={<People />} />
            <Tab label="الإجازات" icon={<EventNote />} />
            <Tab label="الحضور والانصراف" icon={<Schedule />} />
            <Tab label="الرواتب" icon={<AttachMoney />} />
          </Tabs>
        </Box>

        {/* تبويب الموظفين */}
        <TabPanel value={tabValue} index={0}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>الموظف</TableCell>
                  <TableCell>رقم الموظف</TableCell>
                  <TableCell>القسم</TableCell>
                  <TableCell>المنصب</TableCell>
                  <TableCell>تاريخ التوظيف</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {employees.map((employee) => (
                  <TableRow key={employee.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ mr: 2 }}>{employee.name.charAt(0)}</Avatar>
                        <Typography>{employee.name}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{employee.employeeId}</TableCell>
                    <TableCell>{employee.department}</TableCell>
                    <TableCell>{employee.position}</TableCell>
                    <TableCell>{new Date(employee.hireDate).toLocaleDateString('ar-SA')}</TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusText(employee.status)}
                        color={getStatusColor(employee.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" color="primary">
                        <Visibility />
                      </IconButton>
                      <IconButton size="small" color="secondary">
                        <Edit />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* تبويب الإجازات */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={<EventNote />}
              onClick={() => {
                setDialogType('leave');
                setOpenDialog(true);
              }}
            >
              طلب إجازة جديد
            </Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>الموظف</TableCell>
                  <TableCell>نوع الإجازة</TableCell>
                  <TableCell>تاريخ البداية</TableCell>
                  <TableCell>تاريخ النهاية</TableCell>
                  <TableCell>المدة (أيام)</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {leaves.map((leave) => (
                  <TableRow key={leave.id}>
                    <TableCell>{leave.employeeName}</TableCell>
                    <TableCell>{getLeaveTypeText(leave.type)}</TableCell>
                    <TableCell>{new Date(leave.startDate).toLocaleDateString('ar-SA')}</TableCell>
                    <TableCell>{new Date(leave.endDate).toLocaleDateString('ar-SA')}</TableCell>
                    <TableCell>{leave.duration}</TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusText(leave.status)}
                        color={getStatusColor(leave.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {leave.status === 'pending' && (
                        <>
                          <IconButton size="small" color="success">
                            <CheckCircle />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <Cancel />
                          </IconButton>
                        </>
                      )}
                      <IconButton size="small" color="primary">
                        <Visibility />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* تبويب الحضور والانصراف */}
        <TabPanel value={tabValue} index={2}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>الموظف</TableCell>
                  <TableCell>التاريخ</TableCell>
                  <TableCell>وقت الحضور</TableCell>
                  <TableCell>وقت الانصراف</TableCell>
                  <TableCell>ساعات العمل</TableCell>
                  <TableCell>الحالة</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {attendance.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>{record.employeeName}</TableCell>
                    <TableCell>{new Date(record.date).toLocaleDateString('ar-SA')}</TableCell>
                    <TableCell>{record.checkIn}</TableCell>
                    <TableCell>{record.checkOut}</TableCell>
                    <TableCell>{record.workingHours}</TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusText(record.status)}
                        color={getStatusColor(record.status) as any}
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* تبويب الرواتب */}
        <TabPanel value={tabValue} index={3}>
          <Alert severity="info" sx={{ mb: 2 }}>
            سيتم تطوير نظام الرواتب قريباً مع إمكانيات متقدمة لحساب الرواتب والبدلات والخصومات.
          </Alert>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>الموظف</TableCell>
                  <TableCell>الراتب الأساسي</TableCell>
                  <TableCell>البدلات</TableCell>
                  <TableCell>الخصومات</TableCell>
                  <TableCell>صافي الراتب</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {employees.map((employee) => (
                  <TableRow key={employee.id}>
                    <TableCell>{employee.name}</TableCell>
                    <TableCell>{employee.salary.toLocaleString('ar-SA')} ريال</TableCell>
                    <TableCell>500 ريال</TableCell>
                    <TableCell>200 ريال</TableCell>
                    <TableCell>{(employee.salary + 500 - 200).toLocaleString('ar-SA')} ريال</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default HRDashboard;
