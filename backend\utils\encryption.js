const bcrypt = require('bcryptjs');
const crypto = require('crypto');

/**
 * تشفير كلمة المرور باستخدام bcrypt
 * @param {string} password - كلمة المرور النصية
 * @returns {Promise<string>} كلمة المرور المشفرة
 */
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
};

/**
 * مقارنة كلمة المرور النصية مع كلمة المرور المشفرة
 * @param {string} password - كلمة المرور النصية
 * @param {string} hashedPassword - كلمة المرور المشفرة
 * @returns {Promise<boolean>} صحيح إذا كانت كلمة المرور متطابقة
 */
const comparePassword = async (password, hashedPassword) => {
  return bcrypt.compare(password, hashedPassword);
};

/**
 * إنشاء سلسلة عشوائية
 * @param {number} length - طول السلسلة (اختياري، الافتراضي 32)
 * @returns {string} سلسلة عشوائية
 */
const generateRandomString = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * إنشاء رمز تحقق عشوائي (مثل رمز التحقق من البريد الإلكتروني)
 * @param {number} length - طول الرمز (اختياري، الافتراضي 6)
 * @returns {string} رمز تحقق عشوائي
 */
const generateVerificationCode = (length = 6) => {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return Math.floor(min + Math.random() * (max - min + 1)).toString();
};

/**
 * تشفير نص باستخدام AES
 * @param {string} text - النص المراد تشفيره
 * @param {string} secretKey - المفتاح السري للتشفير
 * @returns {string} النص المشفر
 */
const encryptText = (text, secretKey) => {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(secretKey), iv);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return `${iv.toString('hex')}:${encrypted}`;
};

/**
 * فك تشفير نص مشفر باستخدام AES
 * @param {string} encryptedText - النص المشفر
 * @param {string} secretKey - المفتاح السري للتشفير
 * @returns {string} النص الأصلي
 */
const decryptText = (encryptedText, secretKey) => {
  const [ivHex, encrypted] = encryptedText.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(secretKey), iv);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

/**
 * إنشاء توقيع HMAC لنص
 * @param {string} text - النص المراد توقيعه
 * @param {string} secretKey - المفتاح السري للتوقيع
 * @returns {string} التوقيع
 */
const generateHmacSignature = (text, secretKey) => {
  return crypto.createHmac('sha256', secretKey).update(text).digest('hex');
};

/**
 * التحقق من توقيع HMAC
 * @param {string} text - النص الأصلي
 * @param {string} signature - التوقيع
 * @param {string} secretKey - المفتاح السري للتوقيع
 * @returns {boolean} صحيح إذا كان التوقيع صحيحًا
 */
const verifyHmacSignature = (text, signature, secretKey) => {
  const computedSignature = generateHmacSignature(text, secretKey);
  return crypto.timingSafeEqual(
    Buffer.from(computedSignature, 'hex'),
    Buffer.from(signature, 'hex')
  );
};

module.exports = {
  hashPassword,
  comparePassword,
  generateRandomString,
  generateVerificationCode,
  encryptText,
  decryptText,
  generateHmacSignature,
  verifyHmacSignature
};