/**
 * ملف إعداد وتكوين التحقق من صحة البيانات
 * يقوم بإعداد وتكوين قواعد التحقق من صحة البيانات للتطبيق
 */

const Joi = require('joi');
const config = require('config');
const logger = require('../utils/logger');
const { recordError } = require('./monitoring');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

// تخصيص رسائل الخطأ باللغة العربية
const customMessages = {
  'string.base': 'يجب أن يكون الحقل {#label} نصًا',
  'string.empty': 'لا يمكن أن يكون الحقل {#label} فارغًا',
  'string.min': 'يجب أن يحتوي الحقل {#label} على الأقل {#limit} حرفًا',
  'string.max': 'يجب أن لا يتجاوز الحقل {#label} {#limit} حرفًا',
  'string.email': 'يجب أن يكون الحقل {#label} بريدًا إلكترونيًا صالحًا',
  'string.pattern.base': 'الحقل {#label} يجب أن يتطابق مع النمط المطلوب',
  'string.alphanum': 'يجب أن يحتوي الحقل {#label} على أحرف وأرقام فقط',
  'string.uri': 'يجب أن يكون الحقل {#label} رابطًا صالحًا',
  'string.length': 'يجب أن يكون طول الحقل {#label} {#limit} حرفًا',
  
  'number.base': 'يجب أن يكون الحقل {#label} رقمًا',
  'number.min': 'يجب أن يكون الحقل {#label} أكبر من أو يساوي {#limit}',
  'number.max': 'يجب أن يكون الحقل {#label} أقل من أو يساوي {#limit}',
  'number.integer': 'يجب أن يكون الحقل {#label} رقمًا صحيحًا',
  'number.positive': 'يجب أن يكون الحقل {#label} رقمًا موجبًا',
  
  'boolean.base': 'يجب أن يكون الحقل {#label} قيمة منطقية',
  
  'object.base': 'يجب أن يكون الحقل {#label} كائنًا',
  'object.unknown': 'الحقل {#label} غير مسموح به',
  
  'array.base': 'يجب أن يكون الحقل {#label} مصفوفة',
  'array.min': 'يجب أن يحتوي الحقل {#label} على الأقل {#limit} عنصر',
  'array.max': 'يجب أن لا يتجاوز الحقل {#label} {#limit} عنصر',
  
  'date.base': 'يجب أن يكون الحقل {#label} تاريخًا صالحًا',
  'date.min': 'يجب أن يكون الحقل {#label} بعد {#limit}',
  'date.max': 'يجب أن يكون الحقل {#label} قبل {#limit}',
  
  'any.required': 'الحقل {#label} مطلوب',
  'any.only': 'الحقل {#label} يجب أن يكون {#valids}',
  'any.invalid': 'الحقل {#label} يحتوي على قيمة غير صالحة',
  'any.unknown': 'الحقل {#label} غير مسموح به',
};

// إعداد Joi مع الرسائل المخصصة
const customJoi = Joi.defaults((schema) => {
  return schema.messages(customMessages);
});

/**
 * التحقق من صحة البيانات
 * @param {Object} data - البيانات المراد التحقق منها
 * @param {Object} schema - مخطط التحقق
 * @param {Object} options - خيارات إضافية
 * @returns {Object} نتيجة التحقق
 */
const validate = (data, schema, options = {}) => {
  try {
    const validationOptions = {
      abortEarly: false,
      stripUnknown: options.stripUnknown !== false,
      ...options,
    };
    
    const result = schema.validate(data, validationOptions);
    
    if (result.error) {
      // تنسيق رسائل الخطأ
      const errors = {};
      
      result.error.details.forEach((error) => {
        const path = error.path.join('.');
        if (!errors[path]) {
          errors[path] = [];
        }
        errors[path].push(error.message);
      });
      
      return {
        isValid: false,
        value: result.value,
        error: result.error,
        errors,
      };
    }
    
    return {
      isValid: true,
      value: result.value,
      error: null,
      errors: {},
    };
  } catch (error) {
    logger.error('فشل في التحقق من صحة البيانات', error);
    recordError(error);
    
    return {
      isValid: false,
      value: data,
      error,
      errors: { _general: ['حدث خطأ أثناء التحقق من صحة البيانات'] },
    };
  }
};

/**
 * إنشاء وسيط للتحقق من صحة البيانات
 * @param {Object} schema - مخطط التحقق
 * @param {string} source - مصدر البيانات (body, params, query)
 * @param {Object} options - خيارات إضافية
 * @returns {Function} وسيط Express
 */
const validateMiddleware = (schema, source = 'body', options = {}) => {
  return (req, res, next) => {
    try {
      const data = req[source];
      const result = validate(data, schema, options);
      
      if (!result.isValid) {
        return res.status(400).json({
          success: false,
          message: 'خطأ في التحقق من صحة البيانات',
          errors: result.errors,
        });
      }
      
      // تحديث البيانات بالقيم التي تم التحقق منها
      req[source] = result.value;
      next();
    } catch (error) {
      logger.error('فشل في تنفيذ وسيط التحقق من صحة البيانات', error);
      recordError(error);
      
      return res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء التحقق من صحة البيانات',
      });
    }
  };
};

// مخططات التحقق الشائعة
const schemas = {
  // مخططات المستخدمين
  user: {
    create: customJoi.object({
      username: customJoi.string().min(3).max(50).required(),
      email: customJoi.string().email().required(),
      password: customJoi.string().min(8).max(100).required(),
      firstName: customJoi.string().min(2).max(50).required(),
      lastName: customJoi.string().min(2).max(50).required(),
      phone: customJoi.string().pattern(/^[0-9+\-\s]+$/).min(10).max(20),
      roles: customJoi.array().items(customJoi.string()),
      isActive: customJoi.boolean().default(true),
    }),
    
    update: customJoi.object({
      username: customJoi.string().min(3).max(50),
      email: customJoi.string().email(),
      firstName: customJoi.string().min(2).max(50),
      lastName: customJoi.string().min(2).max(50),
      phone: customJoi.string().pattern(/^[0-9+\-\s]+$/).min(10).max(20),
      roles: customJoi.array().items(customJoi.string()),
      isActive: customJoi.boolean(),
    }),
    
    changePassword: customJoi.object({
      currentPassword: customJoi.string().required(),
      newPassword: customJoi.string().min(8).max(100).required(),
      confirmPassword: customJoi.string().valid(customJoi.ref('newPassword')).required()
        .messages({ 'any.only': 'يجب أن تتطابق كلمة المرور الجديدة مع تأكيد كلمة المرور' }),
    }),
    
    resetPassword: customJoi.object({
      token: customJoi.string().required(),
      newPassword: customJoi.string().min(8).max(100).required(),
      confirmPassword: customJoi.string().valid(customJoi.ref('newPassword')).required()
        .messages({ 'any.only': 'يجب أن تتطابق كلمة المرور الجديدة مع تأكيد كلمة المرور' }),
    }),
    
    login: customJoi.object({
      email: customJoi.string().email().required(),
      password: customJoi.string().required(),
      rememberMe: customJoi.boolean().default(false),
    }),
  },
  
  // مخططات العملاء
  customer: {
    create: customJoi.object({
      name: customJoi.string().min(2).max(100).required(),
      email: customJoi.string().email().required(),
      phone: customJoi.string().pattern(/^[0-9+\-\s]+$/).min(10).max(20).required(),
      address: customJoi.string().min(5).max(200),
      city: customJoi.string().min(2).max(50),
      state: customJoi.string().min(2).max(50),
      postalCode: customJoi.string().max(20),
      country: customJoi.string().min(2).max(50),
      contactPerson: customJoi.string().min(2).max(100),
      taxId: customJoi.string().max(50),
      notes: customJoi.string().max(500),
      isActive: customJoi.boolean().default(true),
    }),
    
    update: customJoi.object({
      name: customJoi.string().min(2).max(100),
      email: customJoi.string().email(),
      phone: customJoi.string().pattern(/^[0-9+\-\s]+$/).min(10).max(20),
      address: customJoi.string().min(5).max(200),
      city: customJoi.string().min(2).max(50),
      state: customJoi.string().min(2).max(50),
      postalCode: customJoi.string().max(20),
      country: customJoi.string().min(2).max(50),
      contactPerson: customJoi.string().min(2).max(100),
      taxId: customJoi.string().max(50),
      notes: customJoi.string().max(500),
      isActive: customJoi.boolean(),
    }),
  },
  
  // مخططات المنتجات
  product: {
    create: customJoi.object({
      name: customJoi.string().min(2).max(100).required(),
      description: customJoi.string().max(500),
      sku: customJoi.string().max(50),
      barcode: customJoi.string().max(50),
      category: customJoi.string().required(),
      price: customJoi.number().positive().required(),
      costPrice: customJoi.number().positive(),
      taxRate: customJoi.number().min(0).max(100).default(0),
      quantity: customJoi.number().integer().min(0).default(0),
      unit: customJoi.string().max(20).default('piece'),
      minQuantity: customJoi.number().integer().min(0).default(0),
      maxQuantity: customJoi.number().integer().min(0),
      isActive: customJoi.boolean().default(true),
      attributes: customJoi.object().pattern(
        customJoi.string(),
        customJoi.alternatives().try(
          customJoi.string(),
          customJoi.number(),
          customJoi.boolean()
        )
      ),
    }),
    
    update: customJoi.object({
      name: customJoi.string().min(2).max(100),
      description: customJoi.string().max(500),
      sku: customJoi.string().max(50),
      barcode: customJoi.string().max(50),
      category: customJoi.string(),
      price: customJoi.number().positive(),
      costPrice: customJoi.number().positive(),
      taxRate: customJoi.number().min(0).max(100),
      quantity: customJoi.number().integer().min(0),
      unit: customJoi.string().max(20),
      minQuantity: customJoi.number().integer().min(0),
      maxQuantity: customJoi.number().integer().min(0),
      isActive: customJoi.boolean(),
      attributes: customJoi.object().pattern(
        customJoi.string(),
        customJoi.alternatives().try(
          customJoi.string(),
          customJoi.number(),
          customJoi.boolean()
        )
      ),
    }),
  },
  
  // مخططات الطلبات
  order: {
    create: customJoi.object({
      customer: customJoi.string().required(),
      orderDate: customJoi.date().default(Date.now),
      dueDate: customJoi.date(),
      status: customJoi.string().valid(
        'draft', 'pending', 'processing', 'completed', 'cancelled', 'on-hold'
      ).default('draft'),
      items: customJoi.array().items(
        customJoi.object({
          product: customJoi.string().required(),
          quantity: customJoi.number().integer().min(1).required(),
          price: customJoi.number().positive().required(),
          discount: customJoi.number().min(0).max(100).default(0),
          notes: customJoi.string().max(200),
        })
      ).min(1).required(),
      shippingAddress: customJoi.string().max(200),
      billingAddress: customJoi.string().max(200),
      shippingMethod: customJoi.string().max(50),
      paymentMethod: customJoi.string().max(50),
      notes: customJoi.string().max(500),
      taxRate: customJoi.number().min(0).max(100).default(0),
      discountRate: customJoi.number().min(0).max(100).default(0),
      shippingCost: customJoi.number().min(0).default(0),
    }),
    
    update: customJoi.object({
      customer: customJoi.string(),
      orderDate: customJoi.date(),
      dueDate: customJoi.date(),
      status: customJoi.string().valid(
        'draft', 'pending', 'processing', 'completed', 'cancelled', 'on-hold'
      ),
      items: customJoi.array().items(
        customJoi.object({
          product: customJoi.string().required(),
          quantity: customJoi.number().integer().min(1).required(),
          price: customJoi.number().positive().required(),
          discount: customJoi.number().min(0).max(100).default(0),
          notes: customJoi.string().max(200),
        })
      ).min(1),
      shippingAddress: customJoi.string().max(200),
      billingAddress: customJoi.string().max(200),
      shippingMethod: customJoi.string().max(50),
      paymentMethod: customJoi.string().max(50),
      notes: customJoi.string().max(500),
      taxRate: customJoi.number().min(0).max(100),
      discountRate: customJoi.number().min(0).max(100),
      shippingCost: customJoi.number().min(0),
    }),
    
    updateStatus: customJoi.object({
      status: customJoi.string().valid(
        'draft', 'pending', 'processing', 'completed', 'cancelled', 'on-hold'
      ).required(),
      notes: customJoi.string().max(500),
    }),
  },
  
  // مخططات الفواتير
  invoice: {
    create: customJoi.object({
      customer: customJoi.string().required(),
      order: customJoi.string(),
      invoiceDate: customJoi.date().default(Date.now),
      dueDate: customJoi.date().required(),
      status: customJoi.string().valid(
        'draft', 'sent', 'paid', 'partially_paid', 'overdue', 'cancelled'
      ).default('draft'),
      items: customJoi.array().items(
        customJoi.object({
          description: customJoi.string().required(),
          quantity: customJoi.number().integer().min(1).required(),
          price: customJoi.number().positive().required(),
          discount: customJoi.number().min(0).max(100).default(0),
          taxRate: customJoi.number().min(0).max(100).default(0),
        })
      ).min(1).required(),
      notes: customJoi.string().max(500),
      terms: customJoi.string().max(1000),
      taxRate: customJoi.number().min(0).max(100).default(0),
      discountRate: customJoi.number().min(0).max(100).default(0),
      shippingCost: customJoi.number().min(0).default(0),
    }),
    
    update: customJoi.object({
      customer: customJoi.string(),
      order: customJoi.string(),
      invoiceDate: customJoi.date(),
      dueDate: customJoi.date(),
      status: customJoi.string().valid(
        'draft', 'sent', 'paid', 'partially_paid', 'overdue', 'cancelled'
      ),
      items: customJoi.array().items(
        customJoi.object({
          description: customJoi.string().required(),
          quantity: customJoi.number().integer().min(1).required(),
          price: customJoi.number().positive().required(),
          discount: customJoi.number().min(0).max(100).default(0),
          taxRate: customJoi.number().min(0).max(100).default(0),
        })
      ).min(1),
      notes: customJoi.string().max(500),
      terms: customJoi.string().max(1000),
      taxRate: customJoi.number().min(0).max(100),
      discountRate: customJoi.number().min(0).max(100),
      shippingCost: customJoi.number().min(0),
    }),
    
    updateStatus: customJoi.object({
      status: customJoi.string().valid(
        'draft', 'sent', 'paid', 'partially_paid', 'overdue', 'cancelled'
      ).required(),
      notes: customJoi.string().max(500),
    }),
  },
  
  // مخططات المدفوعات
  payment: {
    create: customJoi.object({
      invoice: customJoi.string().required(),
      amount: customJoi.number().positive().required(),
      paymentDate: customJoi.date().default(Date.now),
      paymentMethod: customJoi.string().required(),
      reference: customJoi.string().max(100),
      notes: customJoi.string().max(500),
    }),
    
    update: customJoi.object({
      amount: customJoi.number().positive(),
      paymentDate: customJoi.date(),
      paymentMethod: customJoi.string(),
      reference: customJoi.string().max(100),
      notes: customJoi.string().max(500),
    }),
  },
  
  // مخططات الموردين
  supplier: {
    create: customJoi.object({
      name: customJoi.string().min(2).max(100).required(),
      email: customJoi.string().email().required(),
      phone: customJoi.string().pattern(/^[0-9+\-\s]+$/).min(10).max(20).required(),
      address: customJoi.string().min(5).max(200),
      city: customJoi.string().min(2).max(50),
      state: customJoi.string().min(2).max(50),
      postalCode: customJoi.string().max(20),
      country: customJoi.string().min(2).max(50),
      contactPerson: customJoi.string().min(2).max(100),
      taxId: customJoi.string().max(50),
      website: customJoi.string().uri().max(100),
      notes: customJoi.string().max(500),
      isActive: customJoi.boolean().default(true),
    }),
    
    update: customJoi.object({
      name: customJoi.string().min(2).max(100),
      email: customJoi.string().email(),
      phone: customJoi.string().pattern(/^[0-9+\-\s]+$/).min(10).max(20),
      address: customJoi.string().min(5).max(200),
      city: customJoi.string().min(2).max(50),
      state: customJoi.string().min(2).max(50),
      postalCode: customJoi.string().max(20),
      country: customJoi.string().min(2).max(50),
      contactPerson: customJoi.string().min(2).max(100),
      taxId: customJoi.string().max(50),
      website: customJoi.string().uri().max(100),
      notes: customJoi.string().max(500),
      isActive: customJoi.boolean(),
    }),
  },
  
  // مخططات البحث والتصفية
  common: {
    pagination: customJoi.object({
      page: customJoi.number().integer().min(1).default(1),
      limit: customJoi.number().integer().min(1).max(100).default(10),
      sortBy: customJoi.string(),
      sortOrder: customJoi.string().valid('asc', 'desc').default('asc'),
    }),
    
    dateRange: customJoi.object({
      startDate: customJoi.date(),
      endDate: customJoi.date().min(customJoi.ref('startDate')),
    }),
    
    id: customJoi.object({
      id: customJoi.string().required(),
    }),
    
    ids: customJoi.object({
      ids: customJoi.array().items(customJoi.string()).min(1).required(),
    }),
  },
};

module.exports = {
  // الثوابت
  customMessages,
  customJoi,
  schemas,
  
  // وظائف التحقق
  validate,
  validateMiddleware,
};