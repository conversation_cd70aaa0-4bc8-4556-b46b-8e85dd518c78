{"openapi": "3.0.0", "info": {"title": "نظام مطبعة متطور API", "description": "توثيق واجهة برمجة التطبيقات لنظام إدارة المطبعة المتطور", "version": "1.0.0", "contact": {"email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:5000/api", "description": "<PERSON>ادم التطوير المحلي"}, {"url": "https://api.print-system.com/api", "description": "<PERSON><PERSON><PERSON> الإنتاج"}], "tags": [{"name": "auth", "description": "عمليات المصادقة وإدارة الجلسات"}, {"name": "users", "description": "عمليات إدارة المستخدمين"}, {"name": "customers", "description": "عمليات إدارة العملاء"}, {"name": "orders", "description": "عمليات إدارة الطلبات"}, {"name": "inventory", "description": "عمليات إدارة المخزون"}, {"name": "invoices", "description": "عمليات إدارة الفواتير"}, {"name": "suppliers", "description": "عمليات إدارة الموردين"}, {"name": "reports", "description": "عمليات إنشاء التقارير"}], "paths": {"/auth/login": {"post": {"tags": ["auth"], "summary": "تسجيل الدخول للمستخدم", "description": "تسجيل دخول المستخدم والحصول على رمز JWT", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "password123"}}}}}}, "responses": {"200": {"description": "تم تسجيل الدخول بنجاح", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}}}}, "400": {"description": "بيانات غير صالحة", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "بيانات اعتماد غير صحيحة", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users": {"get": {"tags": ["users"], "summary": "الحصول على قائمة المستخدمين", "description": "استرجاع قائمة بجميع المستخدمين (للمسؤولين فقط)", "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "رقم الصفحة للتصفح"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "عد<PERSON> العناصر في الصفحة"}], "responses": {"200": {"description": "قائمة المستخدمين", "content": {"application/json": {"schema": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"description": "غير مصرح", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "ممنوع", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["users"], "summary": "إنشاء مستخدم جديد", "description": "إنشاء مستخدم جديد (للمسؤولين فقط)", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInput"}}}}, "responses": {"201": {"description": "تم إنشاء المستخدم بنجاح", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "بيانات غير صالحة", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "غير مصرح", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "ممنوع", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"_id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "role": {"type": "string", "enum": ["admin", "manager", "sales", "production", "accountant", "user"]}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "UserInput": {"type": "object", "required": ["name", "email", "password", "role"], "properties": {"name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> محمد"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "Password123!"}, "role": {"type": "string", "enum": ["admin", "manager", "sales", "production", "accountant", "user"], "example": "sales"}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["orders:read", "orders:create"]}, "isActive": {"type": "boolean", "default": true}}}, "Pagination": {"type": "object", "properties": {"totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}, "Error": {"type": "object", "properties": {"message": {"type": "string"}, "errors": {"type": "array", "items": {"type": "object", "properties": {"msg": {"type": "string"}, "param": {"type": "string"}, "location": {"type": "string"}}}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}