const mongoose = require('mongoose');

/**
 * نموذج سجل رسائل SMS
 */
const SMSLogSchema = new mongoose.Schema({
  phone: {
    type: String,
    required: true,
    trim: true
  },
  
  message: {
    type: String,
    required: true
  },
  
  type: {
    type: String,
    enum: ['verification', 'notification', 'alert', 'reminder', 'marketing'],
    default: 'notification'
  },
  
  provider: {
    type: String,
    enum: ['taqnyat', 'msegat', 'oursms'],
    default: 'taqnyat'
  },
  
  messageId: {
    type: String,
    trim: true
  },
  
  status: {
    type: String,
    enum: ['pending', 'sent', 'delivered', 'failed', 'expired'],
    default: 'pending'
  },
  
  cost: {
    type: Number,
    default: 0
  },
  
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  orderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order'
  },
  
  customerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer'
  },
  
  sentAt: {
    type: Date,
    default: Date.now
  },
  
  deliveredAt: {
    type: Date
  },
  
  failureReason: {
    type: String
  },
  
  retryCount: {
    type: Number,
    default: 0
  },
  
  metadata: {
    type: mongoose.Schema.Types.Mixed
  }
  
}, {
  timestamps: true
});

// فهرسة رقم الهاتف
SMSLogSchema.index({ phone: 1 });

// فهرسة النوع
SMSLogSchema.index({ type: 1 });

// فهرسة الحالة
SMSLogSchema.index({ status: 1 });

// فهرسة تاريخ الإرسال
SMSLogSchema.index({ sentAt: -1 });

// فهرسة مركبة للاستعلامات المتقدمة
SMSLogSchema.index({ phone: 1, type: 1, sentAt: -1 });

module.exports = mongoose.model('SMSLog', SMSLogSchema);
