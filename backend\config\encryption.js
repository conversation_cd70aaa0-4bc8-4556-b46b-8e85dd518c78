/**
 * ملف إعداد وتكوين التشفير والأمان
 * يقوم بإعداد وتكوين خدمات التشفير وتأمين البيانات في النظام
 */

const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('config');
const logger = require('../utils/logger');
const { recordError } = require('./monitoring');

// الحصول على إعدادات التشفير من ملف التكوين
let encryptionConfig;
try {
  encryptionConfig = config.get('encryption');
} catch (error) {
  logger.warn('لم يتم العثور على إعدادات التشفير في ملف التكوين، سيتم استخدام الإعدادات الافتراضية');
  encryptionConfig = {
    jwt: {
      secret: process.env.JWT_SECRET || 'default_jwt_secret_key_change_in_production',
      expiresIn: process.env.JWT_EXPIRES_IN || '1d',
      refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
    },
    bcrypt: {
      saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS) || 10
    },
    aes: {
      key: process.env.AES_KEY || 'default_aes_key_32_chars_change_me!',
      iv: process.env.AES_IV || 'default_aes_iv_16',
      algorithm: process.env.AES_ALGORITHM || 'aes-256-cbc'
    }
  };
}

/**
 * تشفير كلمة المرور باستخدام bcrypt
 * @param {string} password - كلمة المرور المراد تشفيرها
 * @returns {Promise<string>} كلمة المرور المشفرة
 */
const hashPassword = async (password) => {
  try {
    const salt = await bcrypt.genSalt(encryptionConfig.bcrypt.saltRounds);
    const hashedPassword = await bcrypt.hash(password, salt);
    return hashedPassword;
  } catch (error) {
    logger.error('فشل في تشفير كلمة المرور', error);
    recordError(error);
    throw error;
  }
};

/**
 * مقارنة كلمة المرور بالنسخة المشفرة
 * @param {string} password - كلمة المرور المراد التحقق منها
 * @param {string} hashedPassword - كلمة المرور المشفرة المخزنة
 * @returns {Promise<boolean>} نتيجة المقارنة
 */
const comparePassword = async (password, hashedPassword) => {
  try {
    return await bcrypt.compare(password, hashedPassword);
  } catch (error) {
    logger.error('فشل في مقارنة كلمة المرور', error);
    recordError(error);
    throw error;
  }
};

/**
 * إنشاء رمز JWT
 * @param {Object} payload - البيانات المراد تضمينها في الرمز
 * @param {string} [expiresIn] - مدة صلاحية الرمز (اختياري)
 * @returns {string} رمز JWT
 */
const generateToken = (payload, expiresIn = encryptionConfig.jwt.expiresIn) => {
  try {
    return jwt.sign(payload, encryptionConfig.jwt.secret, { expiresIn });
  } catch (error) {
    logger.error('فشل في إنشاء رمز JWT', error);
    recordError(error);
    throw error;
  }
};

/**
 * إنشاء رمز تحديث JWT
 * @param {Object} payload - البيانات المراد تضمينها في الرمز
 * @returns {string} رمز تحديث JWT
 */
const generateRefreshToken = (payload) => {
  try {
    return jwt.sign(payload, encryptionConfig.jwt.secret, { expiresIn: encryptionConfig.jwt.refreshExpiresIn });
  } catch (error) {
    logger.error('فشل في إنشاء رمز تحديث JWT', error);
    recordError(error);
    throw error;
  }
};

/**
 * التحقق من صحة رمز JWT
 * @param {string} token - رمز JWT المراد التحقق منه
 * @returns {Object|null} البيانات المضمنة في الرمز أو null في حالة الفشل
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, encryptionConfig.jwt.secret);
  } catch (error) {
    logger.error('فشل في التحقق من رمز JWT', error);
    recordError(error);
    return null;
  }
};

/**
 * تشفير نص باستخدام AES
 * @param {string} text - النص المراد تشفيره
 * @returns {string} النص المشفر بتنسيق Base64
 */
const encryptText = (text) => {
  try {
    const key = Buffer.from(encryptionConfig.aes.key);
    const iv = Buffer.from(encryptionConfig.aes.iv);
    const cipher = crypto.createCipheriv(encryptionConfig.aes.algorithm, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
  } catch (error) {
    logger.error('فشل في تشفير النص', error);
    recordError(error);
    throw error;
  }
};

/**
 * فك تشفير نص مشفر باستخدام AES
 * @param {string} encryptedText - النص المشفر بتنسيق Base64
 * @returns {string} النص الأصلي
 */
const decryptText = (encryptedText) => {
  try {
    const key = Buffer.from(encryptionConfig.aes.key);
    const iv = Buffer.from(encryptionConfig.aes.iv);
    const decipher = crypto.createDecipheriv(encryptionConfig.aes.algorithm, key, iv);
    let decrypted = decipher.update(encryptedText, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    logger.error('فشل في فك تشفير النص', error);
    recordError(error);
    throw error;
  }
};

/**
 * إنشاء رمز عشوائي
 * @param {number} [length=32] - طول الرمز
 * @returns {string} رمز عشوائي
 */
const generateRandomToken = (length = 32) => {
  try {
    return crypto.randomBytes(length).toString('hex');
  } catch (error) {
    logger.error('فشل في إنشاء رمز عشوائي', error);
    recordError(error);
    throw error;
  }
};

/**
 * حساب قيمة التجزئة (hash) لنص
 * @param {string} text - النص المراد حساب قيمة التجزئة له
 * @param {string} [algorithm='sha256'] - خوارزمية التجزئة
 * @returns {string} قيمة التجزئة بتنسيق hex
 */
const hashText = (text, algorithm = 'sha256') => {
  try {
    return crypto.createHash(algorithm).update(text).digest('hex');
  } catch (error) {
    logger.error(`فشل في حساب قيمة التجزئة باستخدام ${algorithm}`, error);
    recordError(error);
    throw error;
  }
};

/**
 * إنشاء توقيع HMAC لنص
 * @param {string} text - النص المراد توقيعه
 * @param {string} [secret] - المفتاح السري (اختياري، يستخدم مفتاح JWT الافتراضي إذا لم يتم تحديده)
 * @param {string} [algorithm='sha256'] - خوارزمية التجزئة
 * @returns {string} التوقيع بتنسيق hex
 */
const createHmacSignature = (text, secret = encryptionConfig.jwt.secret, algorithm = 'sha256') => {
  try {
    return crypto.createHmac(algorithm, secret).update(text).digest('hex');
  } catch (error) {
    logger.error(`فشل في إنشاء توقيع HMAC باستخدام ${algorithm}`, error);
    recordError(error);
    throw error;
  }
};

/**
 * التحقق من توقيع HMAC
 * @param {string} text - النص الأصلي
 * @param {string} signature - التوقيع المراد التحقق منه
 * @param {string} [secret] - المفتاح السري (اختياري، يستخدم مفتاح JWT الافتراضي إذا لم يتم تحديده)
 * @param {string} [algorithm='sha256'] - خوارزمية التجزئة
 * @returns {boolean} نتيجة التحقق
 */
const verifyHmacSignature = (text, signature, secret = encryptionConfig.jwt.secret, algorithm = 'sha256') => {
  try {
    const computedSignature = createHmacSignature(text, secret, algorithm);
    return crypto.timingSafeEqual(Buffer.from(computedSignature, 'hex'), Buffer.from(signature, 'hex'));
  } catch (error) {
    logger.error(`فشل في التحقق من توقيع HMAC باستخدام ${algorithm}`, error);
    recordError(error);
    return false;
  }
};

/**
 * تشفير كائن JSON
 * @param {Object} data - الكائن المراد تشفيره
 * @returns {string} النص المشفر بتنسيق Base64
 */
const encryptObject = (data) => {
  try {
    const jsonString = JSON.stringify(data);
    return encryptText(jsonString);
  } catch (error) {
    logger.error('فشل في تشفير كائن JSON', error);
    recordError(error);
    throw error;
  }
};

/**
 * فك تشفير كائن JSON
 * @param {string} encryptedData - النص المشفر بتنسيق Base64
 * @returns {Object|null} الكائن الأصلي أو null في حالة الفشل
 */
const decryptObject = (encryptedData) => {
  try {
    const jsonString = decryptText(encryptedData);
    return JSON.parse(jsonString);
  } catch (error) {
    logger.error('فشل في فك تشفير كائن JSON', error);
    recordError(error);
    return null;
  }
};

/**
 * إنشاء رمز تأكيد البريد الإلكتروني
 * @param {string} email - البريد الإلكتروني
 * @returns {string} رمز التأكيد
 */
const generateEmailVerificationToken = (email) => {
  try {
    const payload = {
      email,
      type: 'email_verification',
      timestamp: Date.now()
    };
    return encryptObject(payload);
  } catch (error) {
    logger.error('فشل في إنشاء رمز تأكيد البريد الإلكتروني', error);
    recordError(error);
    throw error;
  }
};

/**
 * التحقق من رمز تأكيد البريد الإلكتروني
 * @param {string} token - رمز التأكيد
 * @param {number} [expiryTime=24] - مدة صلاحية الرمز بالساعات
 * @returns {Object|null} بيانات الرمز أو null في حالة الفشل
 */
const verifyEmailVerificationToken = (token, expiryTime = 24) => {
  try {
    const payload = decryptObject(token);
    
    if (!payload || payload.type !== 'email_verification') {
      return null;
    }
    
    // التحقق من صلاحية الرمز
    const expiryTimeMs = expiryTime * 60 * 60 * 1000; // تحويل الساعات إلى مللي ثانية
    if (Date.now() - payload.timestamp > expiryTimeMs) {
      return null; // الرمز منتهي الصلاحية
    }
    
    return payload;
  } catch (error) {
    logger.error('فشل في التحقق من رمز تأكيد البريد الإلكتروني', error);
    recordError(error);
    return null;
  }
};

/**
 * إنشاء رمز إعادة تعيين كلمة المرور
 * @param {string} userId - معرف المستخدم
 * @returns {string} رمز إعادة تعيين كلمة المرور
 */
const generatePasswordResetToken = (userId) => {
  try {
    const payload = {
      userId,
      type: 'password_reset',
      timestamp: Date.now()
    };
    return encryptObject(payload);
  } catch (error) {
    logger.error('فشل في إنشاء رمز إعادة تعيين كلمة المرور', error);
    recordError(error);
    throw error;
  }
};

/**
 * التحقق من رمز إعادة تعيين كلمة المرور
 * @param {string} token - رمز إعادة تعيين كلمة المرور
 * @param {number} [expiryTime=24] - مدة صلاحية الرمز بالساعات
 * @returns {Object|null} بيانات الرمز أو null في حالة الفشل
 */
const verifyPasswordResetToken = (token, expiryTime = 24) => {
  try {
    const payload = decryptObject(token);
    
    if (!payload || payload.type !== 'password_reset') {
      return null;
    }
    
    // التحقق من صلاحية الرمز
    const expiryTimeMs = expiryTime * 60 * 60 * 1000; // تحويل الساعات إلى مللي ثانية
    if (Date.now() - payload.timestamp > expiryTimeMs) {
      return null; // الرمز منتهي الصلاحية
    }
    
    return payload;
  } catch (error) {
    logger.error('فشل في التحقق من رمز إعادة تعيين كلمة المرور', error);
    recordError(error);
    return null;
  }
};

/**
 * إنشاء رمز API
 * @param {string} userId - معرف المستخدم
 * @param {string} [scope='*'] - نطاق الوصول
 * @returns {string} رمز API
 */
const generateApiKey = (userId, scope = '*') => {
  try {
    // إنشاء جزء عشوائي للرمز
    const randomPart = crypto.randomBytes(16).toString('hex');
    
    // إنشاء توقيع للرمز
    const signature = createHmacSignature(`${userId}:${scope}:${randomPart}`);
    
    // دمج الأجزاء لإنشاء الرمز النهائي
    const apiKey = `${userId}.${scope}.${randomPart}.${signature.substring(0, 16)}`;
    
    return apiKey;
  } catch (error) {
    logger.error('فشل في إنشاء رمز API', error);
    recordError(error);
    throw error;
  }
};

/**
 * التحقق من رمز API
 * @param {string} apiKey - رمز API
 * @returns {Object|null} بيانات الرمز أو null في حالة الفشل
 */
const verifyApiKey = (apiKey) => {
  try {
    // تقسيم الرمز إلى أجزائه
    const parts = apiKey.split('.');
    
    if (parts.length !== 4) {
      return null;
    }
    
    const [userId, scope, randomPart, providedSignature] = parts;
    
    // إعادة إنشاء التوقيع للتحقق
    const computedSignature = createHmacSignature(`${userId}:${scope}:${randomPart}`).substring(0, 16);
    
    // التحقق من التوقيع
    if (providedSignature !== computedSignature) {
      return null;
    }
    
    return {
      userId,
      scope
    };
  } catch (error) {
    logger.error('فشل في التحقق من رمز API', error);
    recordError(error);
    return null;
  }
};

module.exports = {
  // وظائف تشفير كلمة المرور
  hashPassword,
  comparePassword,
  
  // وظائف JWT
  generateToken,
  generateRefreshToken,
  verifyToken,
  
  // وظائف التشفير
  encryptText,
  decryptText,
  encryptObject,
  decryptObject,
  
  // وظائف التجزئة والتوقيع
  hashText,
  createHmacSignature,
  verifyHmacSignature,
  
  // وظائف إنشاء الرموز
  generateRandomToken,
  generateEmailVerificationToken,
  verifyEmailVerificationToken,
  generatePasswordResetToken,
  verifyPasswordResetToken,
  generateApiKey,
  verifyApiKey
};