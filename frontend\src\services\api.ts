import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// إعداد Axios
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // إضافة interceptor للطلبات
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // إضافة interceptor للاستجابات
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // طرق HTTP الأساسية
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.get(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.post(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.put(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.delete(url, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.patch(url, data, config);
  }
}

// إنشاء instance واحد
const apiService = new ApiService();

// خدمات المصادقة
export const authAPI = {
  login: (credentials: { email: string; password: string }) =>
    apiService.post('/auth/login', credentials),
  
  verifyLogin: (data: { verificationId: string; code: string }) =>
    apiService.post('/auth/verify-login', data),
  
  resendCode: (data: { verificationId: string }) =>
    apiService.post('/auth/resend-code', data),
  
  logout: () =>
    apiService.post('/auth/logout'),
  
  getCurrentUser: () =>
    apiService.get('/auth/me'),
  
  refreshToken: (refreshToken: string) =>
    apiService.post('/auth/refresh-token', { refreshToken }),
};

// خدمات العملاء
export const customersAPI = {
  getAll: (params?: any) =>
    apiService.get('/customers', { params }),
  
  getById: (id: string) =>
    apiService.get(`/customers/${id}`),
  
  create: (data: any) =>
    apiService.post('/customers', data),
  
  update: (id: string, data: any) =>
    apiService.put(`/customers/${id}`, data),
  
  delete: (id: string) =>
    apiService.delete(`/customers/${id}`),
};

// خدمات الطلبات
export const ordersAPI = {
  getAll: (params?: any) =>
    apiService.get('/orders', { params }),
  
  getById: (id: string) =>
    apiService.get(`/orders/${id}`),
  
  create: (data: any) =>
    apiService.post('/orders', data),
  
  update: (id: string, data: any) =>
    apiService.put(`/orders/${id}`, data),
  
  delete: (id: string) =>
    apiService.delete(`/orders/${id}`),
  
  updateStatus: (id: string, status: string) =>
    apiService.patch(`/orders/${id}/status`, { status }),
};

// خدمات المخزون
export const inventoryAPI = {
  getAll: (params?: any) =>
    apiService.get('/inventory', { params }),
  
  getById: (id: string) =>
    apiService.get(`/inventory/${id}`),
  
  create: (data: any) =>
    apiService.post('/inventory', data),
  
  update: (id: string, data: any) =>
    apiService.put(`/inventory/${id}`, data),
  
  delete: (id: string) =>
    apiService.delete(`/inventory/${id}`),
  
  updateStock: (id: string, quantity: number, type: 'add' | 'subtract') =>
    apiService.patch(`/inventory/${id}/stock`, { quantity, type }),
};

// خدمات الفواتير
export const invoicesAPI = {
  getAll: (params?: any) =>
    apiService.get('/invoices', { params }),
  
  getById: (id: string) =>
    apiService.get(`/invoices/${id}`),
  
  create: (data: any) =>
    apiService.post('/invoices', data),
  
  update: (id: string, data: any) =>
    apiService.put(`/invoices/${id}`, data),
  
  delete: (id: string) =>
    apiService.delete(`/invoices/${id}`),
  
  markAsPaid: (id: string, paymentData: any) =>
    apiService.patch(`/invoices/${id}/pay`, paymentData),
};

// خدمات الموارد البشرية
export const hrAPI = {
  // الموظفين
  getEmployees: (params?: any) =>
    apiService.get('/hr/employees', { params }),
  
  getEmployee: (id: string) =>
    apiService.get(`/hr/employees/${id}`),
  
  createEmployee: (data: any) =>
    apiService.post('/hr/employees', data),
  
  updateEmployee: (id: string, data: any) =>
    apiService.put(`/hr/employees/${id}`, data),
  
  deleteEmployee: (id: string) =>
    apiService.delete(`/hr/employees/${id}`),
  
  // الإجازات
  getLeaves: (params?: any) =>
    apiService.get('/hr/leaves', { params }),
  
  createLeave: (data: any) =>
    apiService.post('/hr/leaves', data),
  
  approveLeave: (id: string, notes?: string) =>
    apiService.put(`/hr/leaves/${id}/approve`, { notes }),
  
  rejectLeave: (id: string, reason: string) =>
    apiService.put(`/hr/leaves/${id}/reject`, { reason }),
  
  // الحضور
  getAttendance: (params?: any) =>
    apiService.get('/hr/attendance', { params }),
  
  checkIn: (data: any) =>
    apiService.post('/hr/attendance/checkin', data),
  
  checkOut: (data: any) =>
    apiService.post('/hr/attendance/checkout', data),
};

// خدمات المستخدمين
export const usersAPI = {
  getAll: (params?: any) =>
    apiService.get('/users', { params }),
  
  getById: (id: string) =>
    apiService.get(`/users/${id}`),
  
  create: (data: any) =>
    apiService.post('/users', data),
  
  update: (id: string, data: any) =>
    apiService.put(`/users/${id}`, data),
  
  delete: (id: string) =>
    apiService.delete(`/users/${id}`),
  
  updatePassword: (id: string, passwordData: any) =>
    apiService.patch(`/users/${id}/password`, passwordData),
};

// خدمات التقارير
export const reportsAPI = {
  getSalesReport: (params?: any) =>
    apiService.get('/reports/sales', { params }),
  
  getInventoryReport: (params?: any) =>
    apiService.get('/reports/inventory', { params }),
  
  getCustomersReport: (params?: any) =>
    apiService.get('/reports/customers', { params }),
  
  getFinancialReport: (params?: any) =>
    apiService.get('/reports/financial', { params }),
  
  exportReport: (type: string, format: string, params?: any) =>
    apiService.get(`/reports/${type}/export/${format}`, { 
      params,
      responseType: 'blob'
    }),
};

// تصدير الخدمة الرئيسية
export default apiService;
