/**
 * ملف إعداد وتكوين المهام المجدولة (Jobs)
 * يقوم بإعداد وتكوين نظام المهام المجدولة والمهام الخلفية للتطبيق
 */

const Bull = require('bull');
const path = require('path');
const fs = require('fs');
const config = require('config');
const { logger } = require('./logging');
const { emitEvent } = require('./events');

// التحقق من تفعيل نظام المهام
const jobsEnabled = process.env.JOBS_ENABLED === 'true';

// الحصول على إعدادات المهام المجدولة من ملف التكوين
let jobsConfig;
try {
  jobsConfig = config.get('jobs');
} catch (error) {
  // استخدام الإعدادات الافتراضية إذا لم يتم العثور على إعدادات في ملف التكوين
  jobsConfig = {
    enabled: process.env.JOBS_ENABLED === 'true',
    concurrency: parseInt(process.env.JOBS_CONCURRENCY, 10) || 1,
    attempts: parseInt(process.env.JOBS_ATTEMPTS, 10) || 3,
    backoff: {
      type: process.env.JOBS_BACKOFF_TYPE || 'exponential',
      delay: parseInt(process.env.JOBS_BACKOFF_DELAY, 10) || 1000
    },
    removeOnComplete: process.env.JOBS_REMOVE_ON_COMPLETE === 'true',
    removeOnFail: process.env.JOBS_REMOVE_ON_FAIL !== 'true',
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT, 10) || 6379,
      password: process.env.REDIS_PASSWORD || '',
      db: parseInt(process.env.REDIS_DB, 10) || 0,
      keyPrefix: process.env.JOBS_PREFIX || 'print_system:jobs:'
    },
    defaultJobOptions: {
      attempts: parseInt(process.env.JOBS_ATTEMPTS, 10) || 3,
      timeout: parseInt(process.env.JOBS_TIMEOUT, 10) || 30000, // 30 ثانية
      removeOnComplete: process.env.JOBS_REMOVE_ON_COMPLETE === 'true',
      removeOnFail: process.env.JOBS_REMOVE_ON_FAIL !== 'true'
    },
    jobsDirectory: 'jobs'
  };
}

// قائمة لتخزين جميع الطوابير
const queues = new Map();

/**
 * إنشاء طابور جديد
 * @param {string} name - اسم الطابور
 * @param {Object} options - خيارات الطابور
 * @returns {Bull.Queue} كائن الطابور
 */
const createQueue = (name, options = {}) => {
  if (queues.has(name)) {
    return queues.get(name);
  }
  
  // التحقق من تفعيل نظام المهام
  if (!jobsConfig.enabled) {
    logger.warn(`محاولة إنشاء طابور ${name} ولكن نظام المهام معطل`);
    return null;
  }
  
  const queueOptions = {
    redis: jobsConfig.redis,
    prefix: jobsConfig.redis.keyPrefix,
    defaultJobOptions: {
      ...jobsConfig.defaultJobOptions,
      ...options.defaultJobOptions
    }
  };
  
  const queue = new Bull(name, queueOptions);
  
  // إعداد معالجات الأحداث
  queue.on('error', (error) => {
    logger.error(`خطأ في طابور ${name}: ${error.message}`, { error, queue: name });
  });
  
  queue.on('failed', (job, error) => {
    logger.error(`فشل المهمة ${job.id} في طابور ${name}: ${error.message}`, {
      error,
      queue: name,
      jobId: job.id,
      jobName: job.name,
      attempts: job.attemptsMade
    });
    
    emitEvent('jobs.failed', {
      queue: name,
      jobId: job.id,
      jobName: job.name,
      attempts: job.attemptsMade,
      error: error.message
    });
  });
  
  queue.on('completed', (job, result) => {
    logger.debug(`اكتملت المهمة ${job.id} في طابور ${name}`, {
      queue: name,
      jobId: job.id,
      jobName: job.name,
      result: typeof result === 'object' ? 'Object' : result
    });
    
    emitEvent('jobs.completed', {
      queue: name,
      jobId: job.id,
      jobName: job.name,
      result: result
    });
  });
  
  queues.set(name, queue);
  logger.info(`تم إنشاء طابور ${name}`);
  
  return queue;
};

/**
 * الحصول على طابور موجود
 * @param {string} name - اسم الطابور
 * @returns {Bull.Queue|null} كائن الطابور أو null إذا لم يكن موجودًا
 */
const getQueue = (name) => {
  return queues.get(name) || null;
};

/**
 * إضافة مهمة إلى الطابور
 * @param {string} queueName - اسم الطابور
 * @param {string} jobName - اسم المهمة
 * @param {Object} data - بيانات المهمة
 * @param {Object} options - خيارات المهمة
 * @returns {Promise<Bull.Job>} كائن المهمة
 */
const addJob = async (queueName, jobName, data = {}, options = {}) => {
  if (!jobsConfig.enabled) {
    logger.warn(`محاولة إضافة مهمة ${jobName} إلى طابور ${queueName} ولكن نظام المهام معطل`);
    return null;
  }
  
  try {
    let queue = getQueue(queueName);
    
    if (!queue) {
      queue = createQueue(queueName);
    }
    
    const jobOptions = {
      ...jobsConfig.defaultJobOptions,
      ...options
    };
    
    const job = await queue.add(jobName, data, jobOptions);
    
    logger.debug(`تمت إضافة المهمة ${job.id} (${jobName}) إلى طابور ${queueName}`, {
      queue: queueName,
      jobId: job.id,
      jobName,
      options: jobOptions
    });
    
    return job;
  } catch (error) {
    logger.error(`خطأ في إضافة المهمة ${jobName} إلى طابور ${queueName}: ${error.message}`, {
      error,
      queue: queueName,
      jobName,
      data,
      options
    });
    
    throw error;
  }
};

/**
 * تحميل معالجات المهام من الدليل
 * @param {string} queueName - اسم الطابور
 * @param {string} processorsDir - مسار دليل المعالجات
 */
const loadProcessors = (queueName, processorsDir) => {
  // التحقق من تفعيل نظام المهام
  if (!jobsConfig.enabled) {
    logger.info(`تجاهل تحميل معالجات المهام لطابور ${queueName} لأن نظام المهام معطل`);
    return;
  }
  
  try {
    const queue = getQueue(queueName) || createQueue(queueName);
    
    // إذا فشل إنشاء الطابور، نتوقف
    if (!queue) {
      logger.warn(`فشل في إنشاء طابور ${queueName}، تجاهل تحميل المعالجات`);
      return;
    }
    
    const processorFiles = fs.readdirSync(processorsDir);
    
    processorFiles.forEach(file => {
      if (file.endsWith('.js')) {
        const processorPath = path.join(processorsDir, file);
        const processor = require(processorPath);
        
        if (typeof processor === 'function') {
          const jobName = path.basename(file, '.js');
          
          queue.process(jobName, jobsConfig.concurrency, async (job) => {
            try {
              logger.debug(`بدء معالجة المهمة ${job.id} (${jobName}) في طابور ${queueName}`, {
                queue: queueName,
                jobId: job.id,
                jobName,
                data: job.data
              });
              
              const startTime = Date.now();
              const result = await processor(job.data, job);
              const duration = Date.now() - startTime;
              
              logger.debug(`اكتملت معالجة المهمة ${job.id} (${jobName}) في طابور ${queueName} خلال ${duration}ms`, {
                queue: queueName,
                jobId: job.id,
                jobName,
                duration
              });
              
              return result;
            } catch (error) {
              logger.error(`خطأ في معالجة المهمة ${job.id} (${jobName}) في طابور ${queueName}: ${error.message}`, {
                error,
                queue: queueName,
                jobId: job.id,
                jobName,
                data: job.data
              });
              
              throw error;
            }
          });
          
          logger.info(`تم تسجيل معالج المهمة ${jobName} في طابور ${queueName}`);
        } else {
          logger.warn(`ملف المعالج ${file} لا يصدر دالة`);
        }
      }
    });
  } catch (error) {
    logger.error(`خطأ في تحميل معالجات المهام لطابور ${queueName}: ${error.message}`, { error });
  }
};

/**
 * إعداد المهام المجدولة
 */
const setupScheduledJobs = () => {
  if (!jobsConfig.enabled) {
    logger.info('نظام المهام المجدولة معطل');
    return;
  }
  
  try {
    // إنشاء طابور للمهام المجدولة
    const schedulerQueue = createQueue('scheduler');
    
    // إضافة مهام مجدولة
    
    // مهمة يومية لإرسال تذكيرات الفواتير المستحقة
    schedulerQueue.add('invoiceReminders', {}, {
      repeat: {
        cron: '0 9 * * *' // كل يوم الساعة 9 صباحًا
      }
    });
    
    // مهمة أسبوعية لإنشاء نسخة احتياطية من قاعدة البيانات
    schedulerQueue.add('databaseBackup', {}, {
      repeat: {
        cron: '0 0 * * 0' // كل يوم أحد الساعة 12 منتصف الليل
      }
    });
    
    // مهمة كل ساعة للتحقق من مستويات المخزون
    schedulerQueue.add('inventoryCheck', {}, {
      repeat: {
        cron: '0 * * * *' // كل ساعة
      }
    });
    
    // مهمة كل 15 دقيقة لتحديث حالة الطلبات
    schedulerQueue.add('orderStatusUpdate', {}, {
      repeat: {
        cron: '*/15 * * * *' // كل 15 دقيقة
      }
    });
    
    // مهمة يومية لإنشاء تقارير المبيعات
    schedulerQueue.add('generateSalesReport', {}, {
      repeat: {
        cron: '0 1 * * *' // كل يوم الساعة 1 صباحًا
      }
    });
    
    // مهمة كل 5 دقائق لمعالجة الإشعارات المعلقة
    schedulerQueue.add('processNotifications', {}, {
      repeat: {
        cron: '*/5 * * * *' // كل 5 دقائق
      }
    });
    
    logger.info('تم إعداد المهام المجدولة بنجاح');
  } catch (error) {
    logger.error(`خطأ في إعداد المهام المجدولة: ${error.message}`, { error });
  }
};

/**
 * تحميل جميع معالجات المهام
 */
const loadAllProcessors = () => {
  if (!jobsConfig.enabled) {
    return;
  }
  
  try {
    const jobsDir = path.join(process.cwd(), jobsConfig.jobsDirectory);
    
    if (!fs.existsSync(jobsDir)) {
      fs.mkdirSync(jobsDir, { recursive: true });
      logger.info(`تم إنشاء دليل المهام: ${jobsDir}`);
      return;
    }
    
    const queueDirs = fs.readdirSync(jobsDir);
    
    queueDirs.forEach(queueDir => {
      const queuePath = path.join(jobsDir, queueDir);
      const stats = fs.statSync(queuePath);
      
      if (stats.isDirectory()) {
        loadProcessors(queueDir, queuePath);
      }
    });
    
    logger.info('تم تحميل جميع معالجات المهام بنجاح');
  } catch (error) {
    logger.error(`خطأ في تحميل معالجات المهام: ${error.message}`, { error });
  }
};

/**
 * إيقاف جميع الطوابير
 */
const stopAllQueues = async () => {
  try {
    const closePromises = [];
    
    for (const [name, queue] of queues.entries()) {
      logger.info(`إيقاف طابور ${name}...`);
      closePromises.push(queue.close());
    }
    
    await Promise.all(closePromises);
    queues.clear();
    
    logger.info('تم إيقاف جميع طوابير المهام بنجاح');
  } catch (error) {
    logger.error(`خطأ في إيقاف طوابير المهام: ${error.message}`, { error });
  }
};

/**
 * إعداد نظام المهام بالكامل
 */
const setupJobs = () => {
  if (!jobsConfig.enabled) {
    logger.info('نظام المهام معطل');
    return;
  }
  
  try {
    loadAllProcessors();
    setupScheduledJobs();
    
    // إعداد معالج لإيقاف الطوابير عند إيقاف التطبيق
    process.on('SIGTERM', async () => {
      logger.info('تم استلام إشارة SIGTERM، إيقاف طوابير المهام...');
      await stopAllQueues();
    });
    
    process.on('SIGINT', async () => {
      logger.info('تم استلام إشارة SIGINT، إيقاف طوابير المهام...');
      await stopAllQueues();
    });
    
    logger.info('تم إعداد نظام المهام بنجاح');
  } catch (error) {
    logger.error(`خطأ في إعداد نظام المهام: ${error.message}`, { error });
  }
};

/**
 * إنشاء مهام افتراضية
 */
const createDefaultJobs = () => {
  // إنشاء دليل المهام إذا لم يكن موجودًا
  const jobsDir = path.join(process.cwd(), jobsConfig.jobsDirectory);
  
  if (!fs.existsSync(jobsDir)) {
    fs.mkdirSync(jobsDir, { recursive: true });
  }
  
  // إنشاء دليل لطابور المهام المجدولة
  const schedulerDir = path.join(jobsDir, 'scheduler');
  if (!fs.existsSync(schedulerDir)) {
    fs.mkdirSync(schedulerDir, { recursive: true });
    
    // إنشاء ملفات معالجات المهام المجدولة
    const invoiceRemindersProcessor = `/**
 * معالج مهمة إرسال تذكيرات الفواتير المستحقة
 * @param {Object} data - بيانات المهمة
 * @param {Bull.Job} job - كائن المهمة
 */
const { sendInvoiceReminders } = require('../../services/invoice');
const { logger } = require('../../config/logging');

module.exports = async (data, job) => {
  try {
    logger.info('بدء إرسال تذكيرات الفواتير المستحقة');
    
    const result = await sendInvoiceReminders();
    
    logger.info(`تم إرسال ${result.count} تذكير للفواتير المستحقة`);
    
    return result;
  } catch (error) {
    logger.error(`خطأ في إرسال تذكيرات الفواتير المستحقة: ${error.message}`, { error });
    throw error;
  }
};
`;
    
    const databaseBackupProcessor = `/**
 * معالج مهمة إنشاء نسخة احتياطية من قاعدة البيانات
 * @param {Object} data - بيانات المهمة
 * @param {Bull.Job} job - كائن المهمة
 */
const path = require('path');
const { backupDatabase } = require('../../config/database');
const { logger } = require('../../config/logging');

module.exports = async (data, job) => {
  try {
    logger.info('بدء إنشاء نسخة احتياطية من قاعدة البيانات');
    
    const backupPath = path.join(process.cwd(), 'backups');
    const backupFilePath = await backupDatabase(backupPath);
    
    logger.info(`تم إنشاء نسخة احتياطية من قاعدة البيانات بنجاح: ${backupFilePath}`);
    
    return { success: true, backupFilePath };
  } catch (error) {
    logger.error(`خطأ في إنشاء نسخة احتياطية من قاعدة البيانات: ${error.message}`, { error });
    throw error;
  }
};
`;
    
    const inventoryCheckProcessor = `/**
 * معالج مهمة التحقق من مستويات المخزون
 * @param {Object} data - بيانات المهمة
 * @param {Bull.Job} job - كائن المهمة
 */
const { checkLowInventory } = require('../../services/inventory');
const { logger } = require('../../config/logging');

module.exports = async (data, job) => {
  try {
    logger.info('بدء التحقق من مستويات المخزون');
    
    const result = await checkLowInventory();
    
    if (result.lowItems.length > 0) {
      logger.info(`تم العثور على ${result.lowItems.length} عنصر بمستوى مخزون منخفض`);
    } else {
      logger.info('جميع عناصر المخزون في المستويات المقبولة');
    }
    
    return result;
  } catch (error) {
    logger.error(`خطأ في التحقق من مستويات المخزون: ${error.message}`, { error });
    throw error;
  }
};
`;
    
    // كتابة ملفات المعالجات
    fs.writeFileSync(path.join(schedulerDir, 'invoiceReminders.js'), invoiceRemindersProcessor, 'utf8');
    fs.writeFileSync(path.join(schedulerDir, 'databaseBackup.js'), databaseBackupProcessor, 'utf8');
    fs.writeFileSync(path.join(schedulerDir, 'inventoryCheck.js'), inventoryCheckProcessor, 'utf8');
    
    logger.info('تم إنشاء ملفات معالجات المهام الافتراضية');
  }
};

// تصدير الدوال والمتغيرات
module.exports = {
  createQueue,
  getQueue,
  addJob,
  loadProcessors,
  setupScheduledJobs,
  loadAllProcessors,
  stopAllQueues,
  setupJobs,
  createDefaultJobs
};