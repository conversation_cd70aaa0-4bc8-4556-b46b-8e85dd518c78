const mongoose = require('mongoose');

/**
 * نموذج الإجازات
 */
const LeaveSchema = new mongoose.Schema({
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee',
    required: [true, 'الموظف مطلوب']
  },
  
  type: {
    type: String,
    enum: ['annual', 'sick', 'emergency', 'maternity', 'paternity', 'unpaid', 'study'],
    required: [true, 'نوع الإجازة مطلوب']
  },
  
  startDate: {
    type: Date,
    required: [true, 'تاريخ بداية الإجازة مطلوب']
  },
  
  endDate: {
    type: Date,
    required: [true, 'تاريخ نهاية الإجازة مطلوب']
  },
  
  duration: {
    type: Number,
    required: [true, 'مدة الإجازة مطلوبة'],
    min: [0.5, 'مدة الإجازة يجب أن تكون نصف يوم على الأقل']
  },
  
  reason: {
    type: String,
    required: [true, 'سبب الإجازة مطلوب'],
    trim: true,
    maxlength: [500, 'سبب الإجازة لا يجب أن يتجاوز 500 حرف']
  },
  
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'cancelled'],
    default: 'pending'
  },
  
  appliedDate: {
    type: Date,
    default: Date.now
  },
  
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  approvedDate: {
    type: Date
  },
  
  rejectionReason: {
    type: String,
    trim: true,
    maxlength: [300, 'سبب الرفض لا يجب أن يتجاوز 300 حرف']
  },
  
  // للإجازات المرضية
  medicalCertificate: {
    filename: { type: String },
    path: { type: String },
    uploadDate: { type: Date }
  },
  
  // للإجازات الطارئة
  emergencyDetails: {
    type: String,
    trim: true
  },
  
  // معلومات الاستبدال
  replacement: {
    employee: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Employee'
    },
    confirmed: {
      type: Boolean,
      default: false
    }
  },
  
  // ملاحظات إضافية
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'الملاحظات لا يجب أن تتجاوز 1000 حرف']
  },
  
  // معلومات الاتصال أثناء الإجازة
  contactDuringLeave: {
    available: { type: Boolean, default: false },
    phone: { type: String, trim: true },
    email: { type: String, trim: true }
  },
  
  // تاريخ العودة الفعلي
  actualReturnDate: {
    type: Date
  },
  
  // هل تم تمديد الإجازة
  extended: {
    type: Boolean,
    default: false
  },
  
  // الإجازة الأصلية (في حالة التمديد)
  originalLeave: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Leave'
  }
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// فهرسة الموظف
LeaveSchema.index({ employee: 1 });

// فهرسة نوع الإجازة
LeaveSchema.index({ type: 1 });

// فهرسة الحالة
LeaveSchema.index({ status: 1 });

// فهرسة تاريخ البداية
LeaveSchema.index({ startDate: 1 });

// فهرسة تاريخ النهاية
LeaveSchema.index({ endDate: 1 });

// فهرسة مركبة للاستعلامات
LeaveSchema.index({ employee: 1, startDate: 1, endDate: 1 });
LeaveSchema.index({ status: 1, startDate: 1 });

// التحقق من صحة التواريخ
LeaveSchema.pre('save', function(next) {
  if (this.startDate >= this.endDate) {
    return next(new Error('تاريخ نهاية الإجازة يجب أن يكون بعد تاريخ البداية'));
  }
  
  // حساب المدة تلقائياً إذا لم تكن محددة
  if (!this.duration) {
    const timeDiff = this.endDate.getTime() - this.startDate.getTime();
    this.duration = Math.ceil(timeDiff / (1000 * 3600 * 24));
  }
  
  next();
});

// خاصية افتراضية لحالة الإجازة
LeaveSchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.status === 'approved' && 
         this.startDate <= now && 
         this.endDate >= now;
});

// خاصية افتراضية للأيام المتبقية
LeaveSchema.virtual('daysRemaining').get(function() {
  if (this.status !== 'approved') return 0;
  
  const now = new Date();
  if (now > this.endDate) return 0;
  if (now < this.startDate) return this.duration;
  
  const timeDiff = this.endDate.getTime() - now.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
});

// طريقة للموافقة على الإجازة
LeaveSchema.methods.approve = function(approvedBy, notes = '') {
  this.status = 'approved';
  this.approvedBy = approvedBy;
  this.approvedDate = new Date();
  if (notes) this.notes = notes;
  return this.save();
};

// طريقة لرفض الإجازة
LeaveSchema.methods.reject = function(rejectedBy, reason) {
  this.status = 'rejected';
  this.approvedBy = rejectedBy;
  this.approvedDate = new Date();
  this.rejectionReason = reason;
  return this.save();
};

// طريقة لإلغاء الإجازة
LeaveSchema.methods.cancel = function(reason = '') {
  this.status = 'cancelled';
  if (reason) this.notes = reason;
  return this.save();
};

// طريقة للتحقق من تداخل الإجازات
LeaveSchema.statics.checkOverlap = async function(employeeId, startDate, endDate, excludeId = null) {
  const query = {
    employee: employeeId,
    status: { $in: ['pending', 'approved'] },
    $or: [
      {
        startDate: { $lte: endDate },
        endDate: { $gte: startDate }
      }
    ]
  };
  
  if (excludeId) {
    query._id = { $ne: excludeId };
  }
  
  const overlappingLeaves = await this.find(query);
  return overlappingLeaves.length > 0;
};

// طريقة لحساب إجمالي الإجازات المستخدمة
LeaveSchema.statics.getTotalUsedLeaves = async function(employeeId, type, year = new Date().getFullYear()) {
  const startDate = new Date(year, 0, 1);
  const endDate = new Date(year, 11, 31);
  
  const result = await this.aggregate([
    {
      $match: {
        employee: mongoose.Types.ObjectId(employeeId),
        type: type,
        status: 'approved',
        startDate: { $gte: startDate },
        endDate: { $lte: endDate }
      }
    },
    {
      $group: {
        _id: null,
        totalDays: { $sum: '$duration' }
      }
    }
  ]);
  
  return result.length > 0 ? result[0].totalDays : 0;
};

// طريقة للحصول على الإجازات القادمة
LeaveSchema.statics.getUpcomingLeaves = async function(days = 7) {
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + days);
  
  return await this.find({
    status: 'approved',
    startDate: { $gte: startDate, $lte: endDate }
  }).populate('employee', 'firstName lastName arabicName department');
};

// طريقة للحصول على الإجازات المعلقة
LeaveSchema.statics.getPendingLeaves = async function() {
  return await this.find({
    status: 'pending'
  }).populate('employee', 'firstName lastName arabicName department')
    .sort({ appliedDate: 1 });
};

module.exports = mongoose.model('Leave', LeaveSchema);
