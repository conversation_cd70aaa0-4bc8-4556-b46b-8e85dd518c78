/**
 * ملف إعداد وتكوين التطبيق
 * يقوم بإعداد وتكوين التطبيق وإضافة الوسائط والمسارات
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const xss = require('xss-clean');
const rateLimit = require('express-rate-limit');
const fileUpload = require('express-fileupload');
const cookieParser = require('cookie-parser');
const compression = require('compression');
const path = require('path');
const config = require('config');
const { errorHandler, notFound } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const swaggerSetup = require('../utils/swagger');

/**
 * تكوين التطبيق وإضافة الوسائط
 * @param {Express} app - تطبيق Express
 */
const setupApp = (app) => {
  // الحصول على إعدادات التطبيق من ملف التكوين
  const corsOptions = config.get('cors');
  const securityConfig = config.get('security');
  const appConfig = config.get('app');
  
  // إعداد وسائط الطلبات
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  app.use(cookieParser());
  
  // إعداد CORS
  app.use(cors(corsOptions));
  
  // إعداد ضغط الاستجابات
  app.use(compression());
  
  // إعداد وسائط الأمان
  if (securityConfig.helmet.enabled) {
    app.use(helmet());
  }
  
  if (securityConfig.xss.enabled) {
    app.use(xss());
  }
  
  // إعداد محدد معدل الطلبات
  if (securityConfig.rateLimiter.enabled) {
    const limiter = rateLimit({
      windowMs: securityConfig.rateLimiter.windowMs,
      max: securityConfig.rateLimiter.max,
      message: { message: securityConfig.rateLimiter.message },
      standardHeaders: true,
      legacyHeaders: false,
    });
    
    app.use(limiter);
  }
  
  // إعداد رفع الملفات
  app.use(fileUpload({
    useTempFiles: true,
    tempFileDir: path.join(process.cwd(), appConfig.tempDir || 'uploads/temp'),
    limits: { fileSize: appConfig.maxUploadSize || 10 * 1024 * 1024 }, // 10MB
    abortOnLimit: true,
    createParentPath: true,
  }));
  
  // إعداد المجلد الثابت للملفات العامة
  app.use(express.static(path.join(process.cwd(), 'public')));
  
  // إعداد مجلد التحميلات
  app.use('/uploads', express.static(path.join(process.cwd(), appConfig.uploadDir || 'uploads')));
  
  // إعداد Swagger
  swaggerSetup(app);
  
  // إضافة معلومات الطلب إلى سجلات التطبيق
  app.use(logger.httpLogger);
  
  // إضافة بادئة API إلى جميع المسارات
  return `${appConfig.apiPrefix}/${appConfig.apiVersion}`;
};

/**
 * إضافة مسارات API إلى التطبيق
 * @param {Express} app - تطبيق Express
 * @param {String} apiPath - مسار API الأساسي
 */
const setupRoutes = (app, apiPath) => {
  // استيراد مسارات API
  const authRoutes = require('../routes/authRoutes');
  const userRoutes = require('../routes/userRoutes');
  const customerRoutes = require('../routes/customerRoutes');
  const orderRoutes = require('../routes/orderRoutes');
  const inventoryRoutes = require('../routes/inventoryRoutes');
  const invoiceRoutes = require('../routes/invoiceRoutes');
  const supplierRoutes = require('../routes/supplierRoutes');
  const reportRoutes = require('../routes/reportRoutes');
  
  // إضافة مسارات API
  app.use(`${apiPath}/auth`, authRoutes);
  app.use(`${apiPath}/users`, userRoutes);
  app.use(`${apiPath}/customers`, customerRoutes);
  app.use(`${apiPath}/orders`, orderRoutes);
  app.use(`${apiPath}/inventory`, inventoryRoutes);
  app.use(`${apiPath}/invoices`, invoiceRoutes);
  app.use(`${apiPath}/suppliers`, supplierRoutes);
  app.use(`${apiPath}/reports`, reportRoutes);
  
  // مسار الصفحة الرئيسية
  app.get('/', (req, res) => {
    res.json({
      message: 'مرحبًا بك في واجهة برمجة تطبيقات نظام المطبعة المتطور',
      documentation: '/api-docs',
      version: config.get('app.version'),
    });
  });
  
  // مسار الحالة
  app.get('/status', (req, res) => {
    res.json({
      status: 'up',
      time: new Date(),
      environment: process.env.NODE_ENV,
      version: config.get('app.version'),
    });
  });
  
  // إضافة وسائط معالجة الأخطاء
  app.use(notFound);
  app.use(errorHandler);
};

/**
 * إعداد خادم التطبيق
 * @param {Express} app - تطبيق Express
 */
const setupServer = (app) => {
  const PORT = config.get('app.port') || 5000;
  
  return app.listen(PORT, () => {
    logger.info(`الخادم يعمل في البيئة ${process.env.NODE_ENV} على المنفذ ${PORT}`);
    logger.info(`واجهة المستخدم متاحة على: http://localhost:${PORT}`);
    logger.info(`توثيق API متاح على: http://localhost:${PORT}/api-docs`);
  });
};

module.exports = {
  setupApp,
  setupRoutes,
  setupServer,
};