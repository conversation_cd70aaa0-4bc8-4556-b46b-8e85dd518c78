import { useSelector } from 'react-redux';
import { RootState } from '../store/store';

/**
 * Hook مخصص للمصادقة
 */
export const useAuth = () => {
  const { user, token, isAuthenticated, loading, error } = useSelector(
    (state: RootState) => state.auth
  );

  return {
    user,
    token,
    isAuthenticated,
    loading,
    error,
    
    // دوال مساعدة للتحقق من الصلاحيات
    hasRole: (role: string) => user?.role === role,
    hasAnyRole: (roles: string[]) => user?.role ? roles.includes(user.role) : false,
    hasPermission: (permission: string) => user?.permissions?.includes(permission) || user?.role === 'admin',
    
    // دوال مساعدة للتحقق من الأدوار المحددة
    isAdmin: () => user?.role === 'admin',
    isManager: () => user?.role === 'manager',
    isAccountant: () => user?.role === 'accountant',
    isSales: () => user?.role === 'sales',
    isProduction: () => user?.role === 'production',
    isInventory: () => user?.role === 'inventory',
    isHR: () => user?.role === 'hr',
    isCustomerService: () => user?.role === 'customer_service',
    isViewer: () => user?.role === 'viewer',
    
    // دوال مساعدة للتحقق من الصلاحيات المجمعة
    canManageUsers: () => user?.role === 'admin',
    canManageCustomers: () => ['admin', 'manager', 'sales', 'customer_service'].includes(user?.role || ''),
    canManageOrders: () => ['admin', 'manager', 'sales', 'production'].includes(user?.role || ''),
    canManageInventory: () => ['admin', 'manager', 'inventory'].includes(user?.role || ''),
    canManageInvoices: () => ['admin', 'manager', 'accountant'].includes(user?.role || ''),
    canManageHR: () => ['admin', 'manager', 'hr'].includes(user?.role || ''),
    canViewReports: () => ['admin', 'manager', 'accountant'].includes(user?.role || ''),
    canManageSettings: () => ['admin', 'manager'].includes(user?.role || ''),
  };
};
