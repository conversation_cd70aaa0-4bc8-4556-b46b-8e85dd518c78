let axios;
try {
  axios = require('axios');
} catch (error) {
  console.warn('axios not installed, SMS service will be disabled');
  axios = null;
}
const crypto = require('crypto');
const config = require('../config/config');

/**
 * خدمة إرسال رسائل SMS
 * تدعم عدة مقدمي خدمة SMS في السعودية
 */
class SMSService {
  constructor() {
    this.provider = process.env.SMS_PROVIDER || 'taqnyat'; // taqnyat, msegat, oursms
    this.apiKey = process.env.SMS_API_KEY;
    this.apiSecret = process.env.SMS_API_SECRET;
    this.senderName = process.env.SMS_SENDER_NAME || 'PrintSys';
    this.baseURL = this.getBaseURL();
  }

  /**
   * الحصول على رابط API حسب مقدم الخدمة
   */
  getBaseURL() {
    const urls = {
      taqnyat: 'https://api.taqnyat.sa',
      msegat: 'https://www.msegat.com/gw',
      oursms: 'https://oursms.net/api'
    };
    return urls[this.provider] || urls.taqnyat;
  }

  /**
   * إنشاء رمز التحقق
   */
  generateVerificationCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * إنشاء توقيع للطلب (للأمان)
   */
  generateSignature(data) {
    const timestamp = Date.now();
    const message = JSON.stringify(data) + timestamp;
    const signature = crypto
      .createHmac('sha256', this.apiSecret)
      .update(message)
      .digest('hex');
    return { signature, timestamp };
  }

  /**
   * إرسال رسالة SMS عبر Taqnyat
   */
  async sendViaTaqnyat(phone, message) {
    if (!axios) {
      throw new Error('axios not available, SMS service disabled');
    }
    try {
      const response = await axios.post(`${this.baseURL}/v1/messages`, {
        recipients: [phone],
        body: message,
        sender: this.senderName
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        messageId: response.data.messageId,
        provider: 'taqnyat'
      };
    } catch (error) {
      console.error('خطأ في إرسال SMS عبر Taqnyat:', error.response?.data || error.message);
      throw new Error('فشل في إرسال الرسالة');
    }
  }

  /**
   * إرسال رسالة SMS عبر Msegat
   */
  async sendViaMsegat(phone, message) {
    if (!axios) {
      throw new Error('axios not available, SMS service disabled');
    }
    try {
      const response = await axios.post(`${this.baseURL}/sendsms.php`, {
        userName: this.apiKey,
        apiKey: this.apiSecret,
        numbers: phone,
        userSender: this.senderName,
        msg: message,
        msgEncoding: 'UTF8'
      });

      if (response.data.code === '1') {
        return {
          success: true,
          messageId: response.data.messageId,
          provider: 'msegat'
        };
      } else {
        throw new Error(response.data.message || 'فشل في إرسال الرسالة');
      }
    } catch (error) {
      console.error('خطأ في إرسال SMS عبر Msegat:', error.response?.data || error.message);
      throw new Error('فشل في إرسال الرسالة');
    }
  }

  /**
   * إرسال رسالة SMS
   */
  async sendSMS(phone, message, type = 'verification') {
    try {
      // تنسيق رقم الهاتف
      const formattedPhone = this.formatPhoneNumber(phone);
      
      // إضافة معلومات إضافية للرسالة حسب النوع
      const fullMessage = this.formatMessage(message, type);

      let result;
      
      // إرسال الرسالة حسب مقدم الخدمة
      switch (this.provider) {
        case 'taqnyat':
          result = await this.sendViaTaqnyat(formattedPhone, fullMessage);
          break;
        case 'msegat':
          result = await this.sendViaMsegat(formattedPhone, fullMessage);
          break;
        default:
          result = await this.sendViaTaqnyat(formattedPhone, fullMessage);
      }

      // حفظ سجل الإرسال
      await this.logSMSActivity(formattedPhone, fullMessage, type, result);

      return result;
    } catch (error) {
      console.error('خطأ في إرسال SMS:', error);
      throw error;
    }
  }

  /**
   * تنسيق رقم الهاتف
   */
  formatPhoneNumber(phone) {
    // إزالة المسافات والرموز
    let formatted = phone.replace(/[\s\-\(\)]/g, '');
    
    // إضافة رمز الدولة إذا لم يكن موجوداً
    if (!formatted.startsWith('+966') && !formatted.startsWith('966')) {
      if (formatted.startsWith('05')) {
        formatted = '+966' + formatted.substring(1);
      } else if (formatted.startsWith('5')) {
        formatted = '+966' + formatted;
      }
    }
    
    return formatted;
  }

  /**
   * تنسيق الرسالة حسب النوع
   */
  formatMessage(message, type) {
    const templates = {
      verification: `رمز التحقق الخاص بك: ${message}\nنظام المطبعة المتطور\nلا تشارك هذا الرمز مع أحد`,
      notification: `تنبيه من نظام المطبعة:\n${message}`,
      alert: `تنبيه عاجل:\n${message}\nنظام المطبعة المتطور`,
      reminder: `تذكير:\n${message}\nنظام المطبعة المتطور`
    };

    return templates[type] || message;
  }

  /**
   * إرسال رمز التحقق
   */
  async sendVerificationCode(phone, code) {
    return await this.sendSMS(phone, code, 'verification');
  }

  /**
   * إرسال تنبيه
   */
  async sendNotification(phone, message) {
    return await this.sendSMS(phone, message, 'notification');
  }

  /**
   * إرسال تنبيه عاجل
   */
  async sendAlert(phone, message) {
    return await this.sendSMS(phone, message, 'alert');
  }

  /**
   * إرسال تذكير
   */
  async sendReminder(phone, message) {
    return await this.sendSMS(phone, message, 'reminder');
  }

  /**
   * حفظ سجل نشاط SMS
   */
  async logSMSActivity(phone, message, type, result) {
    try {
      const SMSLog = require('../models/SMSLog');
      
      await SMSLog.create({
        phone,
        message,
        type,
        provider: this.provider,
        messageId: result.messageId,
        status: result.success ? 'sent' : 'failed',
        sentAt: new Date()
      });
    } catch (error) {
      console.error('خطأ في حفظ سجل SMS:', error);
    }
  }

  /**
   * التحقق من حالة الرسالة
   */
  async checkMessageStatus(messageId) {
    try {
      // هذا يعتمد على مقدم الخدمة
      // مثال لـ Taqnyat
      if (this.provider === 'taqnyat') {
        const response = await axios.get(`${this.baseURL}/v1/messages/${messageId}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`
          }
        });
        
        return response.data;
      }
      
      return null;
    } catch (error) {
      console.error('خطأ في التحقق من حالة الرسالة:', error);
      return null;
    }
  }
}

module.exports = new SMSService();
