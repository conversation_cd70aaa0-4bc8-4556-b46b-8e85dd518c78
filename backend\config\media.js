/**
 * ملف إعداد وتكوين الوسائط المتعددة
 * يقوم بإعداد وتكوين خدمات إدارة الوسائط المتعددة في النظام
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const crypto = require('crypto');
const config = require('config');
const logger = require('../utils/logger');
const { recordError } = require('./monitoring');
const { isAllowedFileType, isAllowedFileSize } = require('./temp');

// الحصول على إعدادات الوسائط المتعددة من ملف التكوين
let mediaConfig;
try {
  mediaConfig = config.get('media');
} catch (error) {
  logger.warn('لم يتم العثور على إعدادات الوسائط المتعددة في ملف التكوين، سيتم استخدام الإعدادات الافتراضية');
  mediaConfig = {
    mediaDir: process.env.MEDIA_DIR || path.join(process.cwd(), 'media'),
    imagesDir: process.env.IMAGES_DIR || path.join(process.cwd(), 'media', 'images'),
    documentsDir: process.env.DOCUMENTS_DIR || path.join(process.cwd(), 'media', 'documents'),
    videosDir: process.env.VIDEOS_DIR || path.join(process.cwd(), 'media', 'videos'),
    maxFileSize: parseInt(process.env.MAX_MEDIA_FILE_SIZE) || 50 * 1024 * 1024, // 50MB
    allowedImageTypes: (process.env.ALLOWED_IMAGE_TYPES || 'jpg,jpeg,png,gif,webp,svg').split(','),
    allowedDocumentTypes: (process.env.ALLOWED_DOCUMENT_TYPES || 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt').split(','),
    allowedVideoTypes: (process.env.ALLOWED_VIDEO_TYPES || 'mp4,avi,mov,wmv,flv,mkv').split(','),
    imageSizes: {
      thumbnail: { width: 150, height: 150 },
      small: { width: 300, height: 300 },
      medium: { width: 600, height: 600 },
      large: { width: 1200, height: 1200 }
    },
    imageQuality: parseInt(process.env.IMAGE_QUALITY) || 80,
    maxImageDimension: parseInt(process.env.MAX_IMAGE_DIMENSION) || 4000,
    watermarkEnabled: process.env.WATERMARK_ENABLED === 'true' || false,
    watermarkPath: process.env.WATERMARK_PATH || path.join(process.cwd(), 'assets', 'watermark.png'),
    watermarkOpacity: parseFloat(process.env.WATERMARK_OPACITY) || 0.3
  };
}

/**
 * إنشاء المجلدات اللازمة للوسائط المتعددة إذا لم تكن موجودة
 */
const setupMediaDirectories = () => {
  try {
    // إنشاء المجلد الرئيسي للوسائط المتعددة
    if (!fs.existsSync(mediaConfig.mediaDir)) {
      fs.mkdirSync(mediaConfig.mediaDir, { recursive: true });
      logger.info(`تم إنشاء مجلد الوسائط المتعددة: ${mediaConfig.mediaDir}`);
    }

    // إنشاء مجلد الصور
    if (!fs.existsSync(mediaConfig.imagesDir)) {
      fs.mkdirSync(mediaConfig.imagesDir, { recursive: true });
      logger.info(`تم إنشاء مجلد الصور: ${mediaConfig.imagesDir}`);
    }

    // إنشاء مجلد المستندات
    if (!fs.existsSync(mediaConfig.documentsDir)) {
      fs.mkdirSync(mediaConfig.documentsDir, { recursive: true });
      logger.info(`تم إنشاء مجلد المستندات: ${mediaConfig.documentsDir}`);
    }

    // إنشاء مجلد الفيديوهات
    if (!fs.existsSync(mediaConfig.videosDir)) {
      fs.mkdirSync(mediaConfig.videosDir, { recursive: true });
      logger.info(`تم إنشاء مجلد الفيديوهات: ${mediaConfig.videosDir}`);
    }

    // إنشاء ملف .gitignore في المجلد الرئيسي للوسائط المتعددة
    const gitignorePath = path.join(mediaConfig.mediaDir, '.gitignore');
    if (!fs.existsSync(gitignorePath)) {
      fs.writeFileSync(gitignorePath, '*\n!.gitignore\n');
      logger.info('تم إنشاء ملف .gitignore في مجلد الوسائط المتعددة');
    }
  } catch (error) {
    logger.error('فشل في إنشاء مجلدات الوسائط المتعددة', error);
    recordError(error);
    throw error;
  }
};

/**
 * إنشاء اسم ملف فريد
 * @param {string} originalName - الاسم الأصلي للملف
 * @param {string} [prefix=''] - بادئة اسم الملف
 * @returns {string} اسم الملف الفريد
 */
const generateUniqueMediaFileName = (originalName, prefix = '') => {
  try {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = path.extname(originalName);
    const fileName = `${prefix}${timestamp}-${randomString}${extension}`;
    return fileName;
  } catch (error) {
    logger.error('فشل في إنشاء اسم ملف وسائط فريد', error);
    recordError(error);
    throw error;
  }
};

/**
 * التحقق من نوع ملف الصورة
 * @param {string} fileName - اسم الملف
 * @returns {boolean} نتيجة التحقق
 */
const isAllowedImageType = (fileName) => {
  try {
    const extension = path.extname(fileName).toLowerCase().substring(1);
    return mediaConfig.allowedImageTypes.includes(extension);
  } catch (error) {
    logger.error('فشل في التحقق من نوع ملف الصورة', error);
    recordError(error);
    return false;
  }
};

/**
 * التحقق من نوع ملف المستند
 * @param {string} fileName - اسم الملف
 * @returns {boolean} نتيجة التحقق
 */
const isAllowedDocumentType = (fileName) => {
  try {
    const extension = path.extname(fileName).toLowerCase().substring(1);
    return mediaConfig.allowedDocumentTypes.includes(extension);
  } catch (error) {
    logger.error('فشل في التحقق من نوع ملف المستند', error);
    recordError(error);
    return false;
  }
};

/**
 * التحقق من نوع ملف الفيديو
 * @param {string} fileName - اسم الملف
 * @returns {boolean} نتيجة التحقق
 */
const isAllowedVideoType = (fileName) => {
  try {
    const extension = path.extname(fileName).toLowerCase().substring(1);
    return mediaConfig.allowedVideoTypes.includes(extension);
  } catch (error) {
    logger.error('فشل في التحقق من نوع ملف الفيديو', error);
    recordError(error);
    return false;
  }
};

/**
 * معالجة وحفظ ملف صورة
 * @param {Object} imageData - بيانات الصورة
 * @param {Buffer} imageData.buffer - محتوى الصورة
 * @param {string} imageData.originalname - الاسم الأصلي للصورة
 * @param {string} imageData.mimetype - نوع MIME للصورة
 * @param {number} imageData.size - حجم الصورة بالبايت
 * @param {Object} [options] - خيارات معالجة الصورة
 * @param {boolean} [options.generateThumbnails=true] - إنشاء صور مصغرة
 * @param {boolean} [options.applyWatermark=false] - تطبيق العلامة المائية
 * @param {string} [options.folder=''] - مجلد فرعي داخل مجلد الصور
 * @returns {Object} معلومات الصورة المحفوظة
 */
const processAndSaveImage = async (imageData, options = {}) => {
  try {
    // التحقق من نوع الصورة
    if (!isAllowedImageType(imageData.originalname)) {
      throw new Error(`نوع الصورة غير مسموح به: ${path.extname(imageData.originalname)}`);
    }

    // التحقق من حجم الصورة
    if (imageData.size > mediaConfig.maxFileSize) {
      throw new Error(`حجم الصورة يتجاوز الحد المسموح به: ${imageData.size} بايت`);
    }

    // تحديد خيارات المعالجة
    const {
      generateThumbnails = true,
      applyWatermark = mediaConfig.watermarkEnabled,
      folder = ''
    } = options;

    // تحديد مجلد الحفظ
    let targetDir = mediaConfig.imagesDir;
    if (folder) {
      targetDir = path.join(targetDir, folder);
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }
    }

    // إنشاء اسم ملف فريد
    const fileName = generateUniqueMediaFileName(imageData.originalname, 'img-');
    const filePath = path.join(targetDir, fileName);

    // تحميل الصورة باستخدام sharp
    let imageProcessor = sharp(imageData.buffer);
    
    // الحصول على معلومات الصورة
    const metadata = await imageProcessor.metadata();
    
    // التحقق من أبعاد الصورة
    if (metadata.width > mediaConfig.maxImageDimension || metadata.height > mediaConfig.maxImageDimension) {
      // تقليل حجم الصورة إذا كانت أبعادها كبيرة جدًا
      imageProcessor = imageProcessor.resize({
        width: Math.min(metadata.width, mediaConfig.maxImageDimension),
        height: Math.min(metadata.height, mediaConfig.maxImageDimension),
        fit: 'inside',
        withoutEnlargement: true
      });
    }
    
    // تطبيق العلامة المائية إذا كان مطلوبًا
    if (applyWatermark && fs.existsSync(mediaConfig.watermarkPath)) {
      imageProcessor = await applyWatermarkToImage(imageProcessor, metadata);
    }
    
    // حفظ الصورة الأصلية بعد المعالجة
    await imageProcessor
      .jpeg({ quality: mediaConfig.imageQuality })
      .toFile(filePath);
    
    // إنشاء معلومات الصورة
    const imageInfo = {
      fileName,
      originalName: imageData.originalname,
      mimeType: 'image/jpeg', // تم تحويل الصورة إلى JPEG
      size: fs.statSync(filePath).size,
      width: metadata.width,
      height: metadata.height,
      path: filePath,
      url: `/media/images/${folder ? folder + '/' : ''}${fileName}`,
      thumbnails: {}
    };
    
    // إنشاء الصور المصغرة إذا كان مطلوبًا
    if (generateThumbnails) {
      for (const [size, dimensions] of Object.entries(mediaConfig.imageSizes)) {
        const thumbnailFileName = `${path.parse(fileName).name}-${size}${path.extname(fileName)}`;
        const thumbnailPath = path.join(targetDir, thumbnailFileName);
        
        await sharp(imageData.buffer)
          .resize({
            width: dimensions.width,
            height: dimensions.height,
            fit: 'cover',
            position: 'centre'
          })
          .jpeg({ quality: mediaConfig.imageQuality })
          .toFile(thumbnailPath);
        
        imageInfo.thumbnails[size] = {
          fileName: thumbnailFileName,
          path: thumbnailPath,
          url: `/media/images/${folder ? folder + '/' : ''}${thumbnailFileName}`,
          width: dimensions.width,
          height: dimensions.height,
          size: fs.statSync(thumbnailPath).size
        };
      }
    }
    
    logger.info(`تم معالجة وحفظ صورة: ${filePath}`);
    return imageInfo;
  } catch (error) {
    logger.error('فشل في معالجة وحفظ صورة', error);
    recordError(error);
    throw error;
  }
};

/**
 * تطبيق العلامة المائية على الصورة
 * @param {Object} imageProcessor - معالج الصورة (sharp)
 * @param {Object} metadata - بيانات وصفية للصورة
 * @returns {Object} معالج الصورة بعد تطبيق العلامة المائية
 */
const applyWatermarkToImage = async (imageProcessor, metadata) => {
  try {
    if (!fs.existsSync(mediaConfig.watermarkPath)) {
      logger.warn(`ملف العلامة المائية غير موجود: ${mediaConfig.watermarkPath}`);
      return imageProcessor;
    }
    
    // تحميل العلامة المائية
    const watermarkBuffer = fs.readFileSync(mediaConfig.watermarkPath);
    const watermarkMetadata = await sharp(watermarkBuffer).metadata();
    
    // حساب حجم العلامة المائية (30% من حجم الصورة)
    const watermarkWidth = Math.floor(metadata.width * 0.3);
    const watermarkHeight = Math.floor(watermarkWidth * (watermarkMetadata.height / watermarkMetadata.width));
    
    // تحجيم العلامة المائية
    const resizedWatermark = await sharp(watermarkBuffer)
      .resize(watermarkWidth, watermarkHeight)
      .composite([
        {
          input: Buffer.from([
            0, 0, 0, Math.floor(mediaConfig.watermarkOpacity * 255)
          ]),
          raw: {
            width: 1,
            height: 1,
            channels: 4
          },
          tile: true,
          blend: 'dest-in'
        }
      ])
      .toBuffer();
    
    // تطبيق العلامة المائية على الصورة
    return imageProcessor.composite([
      {
        input: resizedWatermark,
        gravity: 'center'
      }
    ]);
  } catch (error) {
    logger.error('فشل في تطبيق العلامة المائية على الصورة', error);
    recordError(error);
    return imageProcessor; // إرجاع معالج الصورة بدون تطبيق العلامة المائية في حالة الفشل
  }
};

/**
 * حفظ ملف مستند
 * @param {Object} documentData - بيانات المستند
 * @param {Buffer} documentData.buffer - محتوى المستند
 * @param {string} documentData.originalname - الاسم الأصلي للمستند
 * @param {string} documentData.mimetype - نوع MIME للمستند
 * @param {number} documentData.size - حجم المستند بالبايت
 * @param {Object} [options] - خيارات حفظ المستند
 * @param {string} [options.folder=''] - مجلد فرعي داخل مجلد المستندات
 * @returns {Object} معلومات المستند المحفوظ
 */
const saveDocument = (documentData, options = {}) => {
  try {
    // التحقق من نوع المستند
    if (!isAllowedDocumentType(documentData.originalname)) {
      throw new Error(`نوع المستند غير مسموح به: ${path.extname(documentData.originalname)}`);
    }

    // التحقق من حجم المستند
    if (documentData.size > mediaConfig.maxFileSize) {
      throw new Error(`حجم المستند يتجاوز الحد المسموح به: ${documentData.size} بايت`);
    }

    // تحديد مجلد الحفظ
    const { folder = '' } = options;
    let targetDir = mediaConfig.documentsDir;
    if (folder) {
      targetDir = path.join(targetDir, folder);
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }
    }

    // إنشاء اسم ملف فريد
    const fileName = generateUniqueMediaFileName(documentData.originalname, 'doc-');
    const filePath = path.join(targetDir, fileName);

    // حفظ المستند
    fs.writeFileSync(filePath, documentData.buffer);

    // إنشاء معلومات المستند
    const documentInfo = {
      fileName,
      originalName: documentData.originalname,
      mimeType: documentData.mimetype,
      size: documentData.size,
      path: filePath,
      url: `/media/documents/${folder ? folder + '/' : ''}${fileName}`,
      extension: path.extname(documentData.originalname).substring(1)
    };

    logger.info(`تم حفظ مستند: ${filePath}`);
    return documentInfo;
  } catch (error) {
    logger.error('فشل في حفظ مستند', error);
    recordError(error);
    throw error;
  }
};

/**
 * حفظ ملف فيديو
 * @param {Object} videoData - بيانات الفيديو
 * @param {Buffer} videoData.buffer - محتوى الفيديو
 * @param {string} videoData.originalname - الاسم الأصلي للفيديو
 * @param {string} videoData.mimetype - نوع MIME للفيديو
 * @param {number} videoData.size - حجم الفيديو بالبايت
 * @param {Object} [options] - خيارات حفظ الفيديو
 * @param {string} [options.folder=''] - مجلد فرعي داخل مجلد الفيديوهات
 * @returns {Object} معلومات الفيديو المحفوظ
 */
const saveVideo = (videoData, options = {}) => {
  try {
    // التحقق من نوع الفيديو
    if (!isAllowedVideoType(videoData.originalname)) {
      throw new Error(`نوع الفيديو غير مسموح به: ${path.extname(videoData.originalname)}`);
    }

    // التحقق من حجم الفيديو
    if (videoData.size > mediaConfig.maxFileSize) {
      throw new Error(`حجم الفيديو يتجاوز الحد المسموح به: ${videoData.size} بايت`);
    }

    // تحديد مجلد الحفظ
    const { folder = '' } = options;
    let targetDir = mediaConfig.videosDir;
    if (folder) {
      targetDir = path.join(targetDir, folder);
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }
    }

    // إنشاء اسم ملف فريد
    const fileName = generateUniqueMediaFileName(videoData.originalname, 'vid-');
    const filePath = path.join(targetDir, fileName);

    // حفظ الفيديو
    fs.writeFileSync(filePath, videoData.buffer);

    // إنشاء معلومات الفيديو
    const videoInfo = {
      fileName,
      originalName: videoData.originalname,
      mimeType: videoData.mimetype,
      size: videoData.size,
      path: filePath,
      url: `/media/videos/${folder ? folder + '/' : ''}${fileName}`,
      extension: path.extname(videoData.originalname).substring(1)
    };

    logger.info(`تم حفظ فيديو: ${filePath}`);
    return videoInfo;
  } catch (error) {
    logger.error('فشل في حفظ فيديو', error);
    recordError(error);
    throw error;
  }
};

/**
 * حذف ملف وسائط متعددة
 * @param {string} filePath - مسار الملف
 * @returns {boolean} نتيجة الحذف
 */
const deleteMediaFile = (filePath) => {
  try {
    // التحقق من أن المسار ضمن مجلدات الوسائط المتعددة المسموح بها
    const normalizedPath = path.normalize(filePath);
    const mediaDirNormalized = path.normalize(mediaConfig.mediaDir);
    
    if (!normalizedPath.startsWith(mediaDirNormalized)) {
      logger.warn(`محاولة حذف ملف خارج مجلد الوسائط المتعددة: ${filePath}`);
      return false;
    }

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      logger.warn(`الملف غير موجود: ${filePath}`);
      return false;
    }

    // حذف الملف
    fs.unlinkSync(filePath);
    logger.info(`تم حذف ملف وسائط متعددة: ${filePath}`);
    return true;
  } catch (error) {
    logger.error(`فشل في حذف ملف وسائط متعددة: ${filePath}`, error);
    recordError(error);
    return false;
  }
};

/**
 * حذف صورة مع الصور المصغرة المرتبطة بها
 * @param {Object} imageInfo - معلومات الصورة
 * @returns {boolean} نتيجة الحذف
 */
const deleteImage = (imageInfo) => {
  try {
    let success = true;
    
    // حذف الصورة الأصلية
    if (imageInfo.path && fs.existsSync(imageInfo.path)) {
      if (!deleteMediaFile(imageInfo.path)) {
        success = false;
      }
    }
    
    // حذف الصور المصغرة
    if (imageInfo.thumbnails) {
      for (const thumbnail of Object.values(imageInfo.thumbnails)) {
        if (thumbnail.path && fs.existsSync(thumbnail.path)) {
          if (!deleteMediaFile(thumbnail.path)) {
            success = false;
          }
        }
      }
    }
    
    return success;
  } catch (error) {
    logger.error('فشل في حذف صورة مع الصور المصغرة المرتبطة بها', error);
    recordError(error);
    return false;
  }
};

/**
 * الحصول على معلومات ملف وسائط متعددة
 * @param {string} filePath - مسار الملف
 * @returns {Object|null} معلومات الملف أو null في حالة الفشل
 */
const getMediaFileInfo = (filePath) => {
  try {
    // التحقق من أن المسار ضمن مجلدات الوسائط المتعددة المسموح بها
    const normalizedPath = path.normalize(filePath);
    const mediaDirNormalized = path.normalize(mediaConfig.mediaDir);
    
    if (!normalizedPath.startsWith(mediaDirNormalized)) {
      logger.warn(`محاولة الوصول إلى ملف خارج مجلد الوسائط المتعددة: ${filePath}`);
      return null;
    }

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      logger.warn(`الملف غير موجود: ${filePath}`);
      return null;
    }

    // الحصول على معلومات الملف
    const stats = fs.statSync(filePath);
    const fileName = path.basename(filePath);
    const extension = path.extname(fileName).toLowerCase();
    const mimeType = getMimeType(extension);

    // تحديد نوع الملف ومساره النسبي
    let fileType = 'unknown';
    let relativePath = '';
    
    if (normalizedPath.includes(path.normalize(mediaConfig.imagesDir))) {
      fileType = 'image';
      relativePath = path.relative(mediaConfig.imagesDir, filePath);
    } else if (normalizedPath.includes(path.normalize(mediaConfig.documentsDir))) {
      fileType = 'document';
      relativePath = path.relative(mediaConfig.documentsDir, filePath);
    } else if (normalizedPath.includes(path.normalize(mediaConfig.videosDir))) {
      fileType = 'video';
      relativePath = path.relative(mediaConfig.videosDir, filePath);
    }

    // إنشاء معلومات الملف
    const fileInfo = {
      fileName,
      originalName: fileName.substring(fileName.indexOf('-') + 9), // استخراج الاسم الأصلي من اسم الملف الفريد
      mimeType,
      size: stats.size,
      path: filePath,
      url: `/media/${fileType}s/${relativePath.replace(/\\/g, '/')}`,
      type: fileType,
      extension: extension.substring(1),
      createdAt: stats.birthtime
    };

    return fileInfo;
  } catch (error) {
    logger.error(`فشل في الحصول على معلومات ملف وسائط متعددة: ${filePath}`, error);
    recordError(error);
    return null;
  }
};

/**
 * الحصول على نوع MIME بناءً على امتداد الملف
 * @param {string} extension - امتداد الملف
 * @returns {string} نوع MIME
 */
const getMimeType = (extension) => {
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.svg': 'image/svg+xml',
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    '.txt': 'text/plain',
    '.mp4': 'video/mp4',
    '.avi': 'video/x-msvideo',
    '.mov': 'video/quicktime',
    '.wmv': 'video/x-ms-wmv',
    '.flv': 'video/x-flv',
    '.mkv': 'video/x-matroska'
  };

  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
};

/**
 * تحويل صورة إلى تنسيق آخر
 * @param {string} imagePath - مسار الصورة
 * @param {string} format - التنسيق المطلوب (jpeg, png, webp, etc.)
 * @param {Object} [options] - خيارات التحويل
 * @param {number} [options.quality] - جودة الصورة (1-100)
 * @returns {Promise<Object>} معلومات الصورة المحولة
 */
const convertImageFormat = async (imagePath, format, options = {}) => {
  try {
    // التحقق من وجود الصورة
    if (!fs.existsSync(imagePath)) {
      throw new Error(`الصورة غير موجودة: ${imagePath}`);
    }

    // التحقق من أن الملف هو صورة
    const imageInfo = getMediaFileInfo(imagePath);
    if (!imageInfo || imageInfo.type !== 'image') {
      throw new Error(`الملف ليس صورة: ${imagePath}`);
    }

    // تحديد خيارات التحويل
    const { quality = mediaConfig.imageQuality } = options;

    // إنشاء اسم الملف الجديد
    const originalExt = path.extname(imagePath);
    const newFilePath = imagePath.replace(originalExt, `.${format}`);

    // تحويل الصورة
    let imageProcessor = sharp(imagePath);

    // تطبيق التنسيق المطلوب
    switch (format.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        imageProcessor = imageProcessor.jpeg({ quality });
        break;
      case 'png':
        imageProcessor = imageProcessor.png({ quality });
        break;
      case 'webp':
        imageProcessor = imageProcessor.webp({ quality });
        break;
      case 'avif':
        imageProcessor = imageProcessor.avif({ quality });
        break;
      default:
        throw new Error(`تنسيق غير مدعوم: ${format}`);
    }

    // حفظ الصورة المحولة
    await imageProcessor.toFile(newFilePath);

    // إرجاع معلومات الصورة المحولة
    return getMediaFileInfo(newFilePath);
  } catch (error) {
    logger.error(`فشل في تحويل تنسيق الصورة: ${imagePath}`, error);
    recordError(error);
    throw error;
  }
};

/**
 * تغيير حجم صورة
 * @param {string} imagePath - مسار الصورة
 * @param {Object} dimensions - الأبعاد المطلوبة
 * @param {number} dimensions.width - العرض المطلوب
 * @param {number} dimensions.height - الارتفاع المطلوب
 * @param {Object} [options] - خيارات تغيير الحجم
 * @param {string} [options.fit='cover'] - طريقة تناسب الصورة (cover, contain, fill, inside, outside)
 * @param {string} [options.position='centre'] - موضع الصورة (centre, north, northeast, etc.)
 * @param {boolean} [options.withoutEnlargement=true] - عدم تكبير الصورة إذا كانت أصغر من الأبعاد المطلوبة
 * @returns {Promise<Object>} معلومات الصورة المعدلة
 */
const resizeImage = async (imagePath, dimensions, options = {}) => {
  try {
    // التحقق من وجود الصورة
    if (!fs.existsSync(imagePath)) {
      throw new Error(`الصورة غير موجودة: ${imagePath}`);
    }

    // التحقق من أن الملف هو صورة
    const imageInfo = getMediaFileInfo(imagePath);
    if (!imageInfo || imageInfo.type !== 'image') {
      throw new Error(`الملف ليس صورة: ${imagePath}`);
    }

    // تحديد خيارات تغيير الحجم
    const {
      fit = 'cover',
      position = 'centre',
      withoutEnlargement = true
    } = options;

    // إنشاء اسم الملف الجديد
    const { dir, name, ext } = path.parse(imagePath);
    const newFileName = `${name}-${dimensions.width}x${dimensions.height}${ext}`;
    const newFilePath = path.join(dir, newFileName);

    // تغيير حجم الصورة
    await sharp(imagePath)
      .resize({
        width: dimensions.width,
        height: dimensions.height,
        fit,
        position,
        withoutEnlargement
      })
      .toFile(newFilePath);

    // إرجاع معلومات الصورة المعدلة
    return getMediaFileInfo(newFilePath);
  } catch (error) {
    logger.error(`فشل في تغيير حجم الصورة: ${imagePath}`, error);
    recordError(error);
    throw error;
  }
};

/**
 * اقتصاص صورة
 * @param {string} imagePath - مسار الصورة
 * @param {Object} region - منطقة الاقتصاص
 * @param {number} region.left - المسافة من اليسار
 * @param {number} region.top - المسافة من الأعلى
 * @param {number} region.width - عرض منطقة الاقتصاص
 * @param {number} region.height - ارتفاع منطقة الاقتصاص
 * @returns {Promise<Object>} معلومات الصورة المقتصة
 */
const cropImage = async (imagePath, region) => {
  try {
    // التحقق من وجود الصورة
    if (!fs.existsSync(imagePath)) {
      throw new Error(`الصورة غير موجودة: ${imagePath}`);
    }

    // التحقق من أن الملف هو صورة
    const imageInfo = getMediaFileInfo(imagePath);
    if (!imageInfo || imageInfo.type !== 'image') {
      throw new Error(`الملف ليس صورة: ${imagePath}`);
    }

    // إنشاء اسم الملف الجديد
    const { dir, name, ext } = path.parse(imagePath);
    const newFileName = `${name}-cropped${ext}`;
    const newFilePath = path.join(dir, newFileName);

    // اقتصاص الصورة
    await sharp(imagePath)
      .extract({
        left: region.left,
        top: region.top,
        width: region.width,
        height: region.height
      })
      .toFile(newFilePath);

    // إرجاع معلومات الصورة المقتصة
    return getMediaFileInfo(newFilePath);
  } catch (error) {
    logger.error(`فشل في اقتصاص الصورة: ${imagePath}`, error);
    recordError(error);
    throw error;
  }
};

/**
 * تدوير صورة
 * @param {string} imagePath - مسار الصورة
 * @param {number} angle - زاوية الدوران بالدرجات
 * @returns {Promise<Object>} معلومات الصورة المدورة
 */
const rotateImage = async (imagePath, angle) => {
  try {
    // التحقق من وجود الصورة
    if (!fs.existsSync(imagePath)) {
      throw new Error(`الصورة غير موجودة: ${imagePath}`);
    }

    // التحقق من أن الملف هو صورة
    const imageInfo = getMediaFileInfo(imagePath);
    if (!imageInfo || imageInfo.type !== 'image') {
      throw new Error(`الملف ليس صورة: ${imagePath}`);
    }

    // إنشاء اسم الملف الجديد
    const { dir, name, ext } = path.parse(imagePath);
    const newFileName = `${name}-rotated${ext}`;
    const newFilePath = path.join(dir, newFileName);

    // تدوير الصورة
    await sharp(imagePath)
      .rotate(angle)
      .toFile(newFilePath);

    // إرجاع معلومات الصورة المدورة
    return getMediaFileInfo(newFilePath);
  } catch (error) {
    logger.error(`فشل في تدوير الصورة: ${imagePath}`, error);
    recordError(error);
    throw error;
  }
};

/**
 * إضافة تأثير تمويه (blur) على صورة
 * @param {string} imagePath - مسار الصورة
 * @param {number} [sigma=10] - مقدار التمويه (1-100)
 * @returns {Promise<Object>} معلومات الصورة المعدلة
 */
const blurImage = async (imagePath, sigma = 10) => {
  try {
    // التحقق من وجود الصورة
    if (!fs.existsSync(imagePath)) {
      throw new Error(`الصورة غير موجودة: ${imagePath}`);
    }

    // التحقق من أن الملف هو صورة
    const imageInfo = getMediaFileInfo(imagePath);
    if (!imageInfo || imageInfo.type !== 'image') {
      throw new Error(`الملف ليس صورة: ${imagePath}`);
    }

    // إنشاء اسم الملف الجديد
    const { dir, name, ext } = path.parse(imagePath);
    const newFileName = `${name}-blurred${ext}`;
    const newFilePath = path.join(dir, newFileName);

    // تطبيق تأثير التمويه
    await sharp(imagePath)
      .blur(sigma)
      .toFile(newFilePath);

    // إرجاع معلومات الصورة المعدلة
    return getMediaFileInfo(newFilePath);
  } catch (error) {
    logger.error(`فشل في إضافة تأثير تمويه على الصورة: ${imagePath}`, error);
    recordError(error);
    throw error;
  }
};

// إعداد المجلدات اللازمة للوسائط المتعددة عند تحميل الملف
setupMediaDirectories();

module.exports = {
  // الإعدادات
  mediaConfig,
  setupMediaDirectories,
  
  // وظائف التحقق
  isAllowedImageType,
  isAllowedDocumentType,
  isAllowedVideoType,
  
  // وظائف حفظ الملفات
  processAndSaveImage,
  saveDocument,
  saveVideo,
  
  // وظائف إدارة الملفات
  deleteMediaFile,
  deleteImage,
  getMediaFileInfo,
  
  // وظائف معالجة الصور
  convertImageFormat,
  resizeImage,
  cropImage,
  rotateImage,
  blurImage,
  applyWatermarkToImage
};