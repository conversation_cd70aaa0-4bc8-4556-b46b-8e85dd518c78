const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const inventoryController = require('../controllers/inventoryController');
const auth = require('../middleware/auth');
const checkPermission = require('../middleware/checkPermission');

/**
 * @route   POST api/inventory
 * @desc    إضافة عنصر جديد للمخزون
 * @access  Private
 */
router.post(
  '/',
  [
    auth,
    checkPermission('inventory', 'create'),
    [
      check('name', 'اسم العنصر مطلوب').not().isEmpty(),
      check('category', 'فئة العنصر مطلوبة').not().isEmpty(),
      check('unit', 'وحدة القياس مطلوبة').not().isEmpty(),
      check('quantity', 'الكمية مطلوبة').isNumeric(),
      check('minimumQuantity', 'الحد الأدنى للكمية مطلوب').isNumeric(),
      check('costPrice', 'سعر التكلفة مطلوب').isNumeric()
    ]
  ],
  inventoryController.addInventoryItem
);

/**
 * @route   GET api/inventory
 * @desc    الحصول على قائمة عناصر المخزون
 * @access  Private
 */
router.get(
  '/',
  [auth, checkPermission('inventory', 'read')],
  inventoryController.getInventoryItems
);

/**
 * @route   GET api/inventory/:id
 * @desc    الحصول على عنصر مخزون محدد
 * @access  Private
 */
router.get(
  '/:id',
  [auth, checkPermission('inventory', 'read')],
  inventoryController.getInventoryItemById
);

/**
 * @route   PUT api/inventory/:id
 * @desc    تحديث عنصر مخزون
 * @access  Private
 */
router.put(
  '/:id',
  [
    auth,
    checkPermission('inventory', 'update'),
    [
      check('name', 'اسم العنصر مطلوب').optional().not().isEmpty(),
      check('category', 'فئة العنصر مطلوبة').optional().not().isEmpty(),
      check('unit', 'وحدة القياس مطلوبة').optional().not().isEmpty(),
      check('minimumQuantity', 'الحد الأدنى للكمية مطلوب').optional().isNumeric(),
      check('costPrice', 'سعر التكلفة مطلوب').optional().isNumeric()
    ]
  ],
  inventoryController.updateInventoryItem
);

/**
 * @route   POST api/inventory/transaction/:id
 * @desc    إضافة معاملة لعنصر مخزون (إضافة أو سحب)
 * @access  Private
 */
router.post(
  '/transaction/:id',
  [
    auth,
    checkPermission('inventory', 'update'),
    [
      check('type', 'نوع المعاملة مطلوب').isIn(['إضافة', 'سحب']),
      check('quantity', 'الكمية مطلوبة').isNumeric().withMessage('الكمية يجب أن تكون رقمًا').custom(value => value > 0).withMessage('الكمية يجب أن تكون أكبر من صفر')
    ]
  ],
  inventoryController.addInventoryTransaction
);

/**
 * @route   DELETE api/inventory/:id
 * @desc    حذف عنصر مخزون
 * @access  Private
 */
router.delete(
  '/:id',
  [auth, checkPermission('inventory', 'delete')],
  inventoryController.deleteInventoryItem
);

/**
 * @route   GET api/inventory/categories
 * @desc    الحصول على قائمة فئات المخزون
 * @access  Private
 */
router.get(
  '/categories',
  [auth, checkPermission('inventory', 'read')],
  inventoryController.getInventoryCategories
);

/**
 * @route   GET api/inventory/dashboard/stats
 * @desc    الحصول على إحصائيات المخزون للوحة المعلومات
 * @access  Private
 */
router.get(
  '/dashboard/stats',
  [auth, checkPermission('inventory', 'read')],
  inventoryController.getInventoryStats
);

module.exports = router;