/**
 * وسيط للتحقق من صلاحيات المستخدم
 * يتحقق مما إذا كان المستخدم لديه الصلاحية المطلوبة للوصول إلى مورد معين
 */

const checkPermission = (resource, action) => {
  return (req, res, next) => {
    // التحقق من وجود المستخدم في الطلب (يجب أن يكون قد تم التحقق من المصادقة قبل ذلك)
    if (!req.user) {
      return res.status(401).json({ msg: 'غير مصرح، يرجى تسجيل الدخول أولاً' });
    }

    // التحقق من وجود الصلاحيات في كائن المستخدم
    if (!req.user.permissions) {
      return res.status(403).json({ msg: 'ليس لديك الصلاحيات الكافية' });
    }

    // التحقق من وجود المورد المطلوب في صلاحيات المستخدم
    if (!req.user.permissions[resource]) {
      return res.status(403).json({ msg: `ليس لديك صلاحية للوصول إلى ${resource}` });
    }

    // التحقق من وجود الإجراء المطلوب في صلاحيات المستخدم للمورد المحدد
    const userPermissions = req.user.permissions[resource];
    
    // إذا كان المستخدم مسؤولاً، فلديه جميع الصلاحيات
    if (req.user.role === 'admin') {
      return next();
    }

    // التحقق من الإجراء المطلوب
    if (!userPermissions.includes(action)) {
      return res.status(403).json({ 
        msg: `ليس لديك صلاحية ${action} على ${resource}` 
      });
    }

    // إذا وصل إلى هنا، فإن المستخدم لديه الصلاحية المطلوبة
    next();
  };
};

module.exports = checkPermission;