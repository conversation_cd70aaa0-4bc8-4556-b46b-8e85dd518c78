const jwt = require('jsonwebtoken');
const config = require('config');
const { UnauthorizedError } = require('./errors');

/**
 * إنشاء توكن JWT
 * @param {Object} payload - البيانات التي سيتم تضمينها في التوكن
 * @param {string} expiresIn - مدة صلاحية التوكن (اختياري، يستخدم القيمة من ملف التكوين إذا لم يتم تحديدها)
 * @returns {string} توكن JWT
 */
const generateToken = (payload, expiresIn = config.get('jwtExpiresIn')) => {
  return jwt.sign(payload, config.get('jwtSecret'), { expiresIn });
};

/**
 * التحقق من صحة توكن JWT
 * @param {string} token - توكن JWT للتحقق منه
 * @returns {Object} البيانات المستخرجة من التوكن
 * @throws {UnauthorizedError} إذا كان التوكن غير صالح أو منتهي الصلاحية
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, config.get('jwtSecret'));
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new UnauthorizedError('انتهت صلاحية التوكن، يرجى تسجيل الدخول مرة أخرى');
    }
    throw new UnauthorizedError('توكن غير صالح');
  }
};

/**
 * استخراج التوكن من رأس الطلب
 * @param {Object} req - كائن الطلب
 * @returns {string|null} توكن JWT أو null إذا لم يتم العثور عليه
 */
const extractTokenFromHeader = (req) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  return authHeader.split(' ')[1];
};

/**
 * إنشاء توكن تحديث
 * @param {Object} payload - البيانات التي سيتم تضمينها في التوكن
 * @returns {string} توكن تحديث JWT
 */
const generateRefreshToken = (payload) => {
  return jwt.sign(payload, config.get('jwtRefreshSecret'), { 
    expiresIn: config.get('jwtRefreshExpiresIn') 
  });
};

/**
 * التحقق من صحة توكن التحديث
 * @param {string} refreshToken - توكن التحديث للتحقق منه
 * @returns {Object} البيانات المستخرجة من التوكن
 * @throws {UnauthorizedError} إذا كان التوكن غير صالح أو منتهي الصلاحية
 */
const verifyRefreshToken = (refreshToken) => {
  try {
    return jwt.verify(refreshToken, config.get('jwtRefreshSecret'));
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new UnauthorizedError('انتهت صلاحية توكن التحديث، يرجى تسجيل الدخول مرة أخرى');
    }
    throw new UnauthorizedError('توكن تحديث غير صالح');
  }
};

/**
 * إنشاء توكن إعادة تعيين كلمة المرور
 * @param {Object} payload - البيانات التي سيتم تضمينها في التوكن
 * @returns {string} توكن إعادة تعيين كلمة المرور
 */
const generatePasswordResetToken = (payload) => {
  return jwt.sign(payload, config.get('jwtSecret'), { 
    expiresIn: '1h' // توكن إعادة تعيين كلمة المرور صالح لمدة ساعة واحدة
  });
};

/**
 * إنشاء توكن تفعيل البريد الإلكتروني
 * @param {Object} payload - البيانات التي سيتم تضمينها في التوكن
 * @returns {string} توكن تفعيل البريد الإلكتروني
 */
const generateEmailVerificationToken = (payload) => {
  return jwt.sign(payload, config.get('jwtSecret'), { 
    expiresIn: '24h' // توكن تفعيل البريد الإلكتروني صالح لمدة 24 ساعة
  });
};

module.exports = {
  generateToken,
  verifyToken,
  extractTokenFromHeader,
  generateRefreshToken,
  verifyRefreshToken,
  generatePasswordResetToken,
  generateEmailVerificationToken
};