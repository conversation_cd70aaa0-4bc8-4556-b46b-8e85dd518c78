/**
 * ملف الإعدادات الرئيسي للتطبيق
 */

module.exports = {
  // إعدادات التطبيق الأساسية
  app: {
    name: 'نظام المطبعة المتطور',
    version: '2.0.0',
    description: 'نظام متكامل لإدارة المطابع',
    port: process.env.PORT || 5000,
    env: process.env.NODE_ENV || 'development',
  },

  // إعدادات قاعدة البيانات
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/print_management_system_v2',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    }
  },

  // إعدادات JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'print_management_secret_key_v2',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  },

  // إعدادات التحميل
  upload: {
    path: process.env.UPLOAD_PATH || 'uploads',
    maxSize: process.env.MAX_FILE_SIZE || 10 * 1024 * 1024, // 10MB
    allowedTypes: ['.pdf', '.jpg', '.jpeg', '.png', '.ai', '.psd', '.eps', '.tiff', '.indd'],
  },

  // إعدادات البريد الإلكتروني
  email: {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    from: process.env.EMAIL_FROM || 'نظام المطبعة <<EMAIL>>',
  },

  // إعدادات الشركة
  company: {
    name: 'المطبعة المتطورة',
    address: 'الرياض، المملكة العربية السعودية',
    phone: '+966 12 345 6789',
    email: '<EMAIL>',
    website: 'www.printmanagement.com',
    taxNumber: '*********',
    logo: '/assets/images/logo.png'
  },

  // إعدادات النظام
  system: {
    defaultLanguage: 'ar',
    supportedLanguages: ['ar', 'en'],
    defaultCurrency: 'SAR',
    defaultTaxRate: 15,
    orderNumberPrefix: 'ORD',
    invoiceNumberPrefix: 'INV',
    customerCodePrefix: 'CUST',
  },

  // إعدادات الأمان
  security: {
    bcryptRounds: 12,
    maxLoginAttempts: 5,
    lockoutDuration: 30 * 60 * 1000, // 30 دقيقة
    passwordMinLength: 8,
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 ساعة
  },

  // إعدادات التقارير
  reports: {
    cacheDuration: 3600, // ساعة واحدة
    defaultDateRange: 'month',
    exportFormats: ['pdf', 'excel', 'csv'],
    defaultFormat: 'pdf',
  }
};
