const Customer = require('../models/Customer');
const { validationResult } = require('express-validator');

/**
 * @route   POST api/customers
 * @desc    إضافة عميل جديد
 * @access  Private
 */
exports.createCustomer = async (req, res) => {
  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const {
    name,
    contactPerson,
    email,
    phone,
    address,
    taxNumber,
    customerType,
    notes,
    paymentTerms,
    creditLimit
  } = req.body;

  try {
    // التحقق من وجود العميل بنفس البريد الإلكتروني
    let customer = await Customer.findOne({ email });

    if (customer) {
      return res.status(400).json({ msg: 'العميل موجود بالفعل بنفس البريد الإلكتروني' });
    }

    // إنشاء عميل جديد
    customer = new Customer({
      name,
      contactPerson,
      email,
      phone,
      address,
      taxNumber,
      customerType,
      notes,
      paymentTerms,
      creditLimit,
      createdBy: req.user.id
    });

    // حفظ العميل في قاعدة البيانات
    await customer.save();

    res.json(customer);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/customers
 * @desc    الحصول على قائمة العملاء
 * @access  Private
 */
exports.getCustomers = async (req, res) => {
  try {
    // البحث عن العملاء مع إمكانية التصفية
    const query = {};
    
    // تصفية حسب النوع إذا تم تحديده
    if (req.query.type) {
      query.customerType = req.query.type;
    }
    
    // تصفية حسب الحالة (نشط/غير نشط)
    if (req.query.active) {
      query.active = req.query.active === 'true';
    }
    
    // البحث بالاسم
    if (req.query.search) {
      query.$or = [
        { name: { $regex: req.query.search, $options: 'i' } },
        { email: { $regex: req.query.search, $options: 'i' } },
        { phone: { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    // الحصول على العملاء مع الترتيب والتقسيم
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const customers = await Customer.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    // الحصول على إجمالي عدد العملاء للتصفح
    const total = await Customer.countDocuments(query);
    
    res.json({
      customers,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/customers/:id
 * @desc    الحصول على عميل محدد
 * @access  Private
 */
exports.getCustomerById = async (req, res) => {
  try {
    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return res.status(404).json({ msg: 'العميل غير موجود' });
    }

    res.json(customer);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'العميل غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   PUT api/customers/:id
 * @desc    تحديث بيانات عميل
 * @access  Private
 */
exports.updateCustomer = async (req, res) => {
  const {
    name,
    contactPerson,
    email,
    phone,
    address,
    taxNumber,
    customerType,
    notes,
    paymentTerms,
    creditLimit,
    active
  } = req.body;

  // بناء كائن تحديث العميل
  const customerFields = {};
  if (name) customerFields.name = name;
  if (contactPerson) customerFields.contactPerson = contactPerson;
  if (email) customerFields.email = email;
  if (phone) customerFields.phone = phone;
  if (address) customerFields.address = address;
  if (taxNumber) customerFields.taxNumber = taxNumber;
  if (customerType) customerFields.customerType = customerType;
  if (notes) customerFields.notes = notes;
  if (paymentTerms) customerFields.paymentTerms = paymentTerms;
  if (creditLimit) customerFields.creditLimit = creditLimit;
  if (active !== undefined) customerFields.active = active;

  try {
    let customer = await Customer.findById(req.params.id);

    if (!customer) {
      return res.status(404).json({ msg: 'العميل غير موجود' });
    }

    // تحديث العميل
    customer = await Customer.findByIdAndUpdate(
      req.params.id,
      { $set: customerFields },
      { new: true }
    );

    res.json(customer);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'العميل غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   DELETE api/customers/:id
 * @desc    حذف عميل
 * @access  Private
 */
exports.deleteCustomer = async (req, res) => {
  try {
    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return res.status(404).json({ msg: 'العميل غير موجود' });
    }

    // حذف العميل
    await Customer.findByIdAndRemove(req.params.id);

    res.json({ msg: 'تم حذف العميل' });
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'العميل غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};