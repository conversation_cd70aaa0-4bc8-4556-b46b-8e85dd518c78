/**
 * ملف إعداد وتكوين Swagger
 * يقوم بإعداد وتكوين توثيق API باستخدام Swagger
 */

const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const path = require('path');
const config = require('config');
const logger = require('../utils/logger');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

/**
 * إعداد خيارات Swagger
 */
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: `${appConfig.name} API Documentation`,
      version: appConfig.version || '1.0.0',
      description: appConfig.description || 'API Documentation for Print Shop Management System',
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
      contact: {
        name: 'Support',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: `${appConfig.baseUrl || `http://localhost:${appConfig.port || 3000}`}${appConfig.apiPrefix || '/api'}`,
        description: `${process.env.NODE_ENV || 'development'} server`,
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'error',
            },
            code: {
              type: 'integer',
              example: 400,
            },
            message: {
              type: 'string',
              example: 'Bad Request',
            },
            details: {
              type: 'object',
              example: {},
            },
          },
        },
        ValidationError: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'error',
            },
            code: {
              type: 'integer',
              example: 400,
            },
            message: {
              type: 'string',
              example: 'Validation Error',
            },
            details: {
              type: 'object',
              properties: {
                errors: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      field: {
                        type: 'string',
                        example: 'email',
                      },
                      message: {
                        type: 'string',
                        example: 'Email is required',
                      },
                    },
                  },
                },
              },
            },
          },
        },
        UnauthorizedError: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'error',
            },
            code: {
              type: 'integer',
              example: 401,
            },
            message: {
              type: 'string',
              example: 'Unauthorized',
            },
          },
        },
        ForbiddenError: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'error',
            },
            code: {
              type: 'integer',
              example: 403,
            },
            message: {
              type: 'string',
              example: 'Forbidden',
            },
          },
        },
        NotFoundError: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'error',
            },
            code: {
              type: 'integer',
              example: 404,
            },
            message: {
              type: 'string',
              example: 'Resource not found',
            },
          },
        },
        ServerError: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'error',
            },
            code: {
              type: 'integer',
              example: 500,
            },
            message: {
              type: 'string',
              example: 'Internal Server Error',
            },
          },
        },
        // نماذج المستخدم
        User: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '60d21b4667d0d8992e610c85',
            },
            name: {
              type: 'string',
              example: 'John Doe',
            },
            email: {
              type: 'string',
              example: '<EMAIL>',
            },
            role: {
              type: 'string',
              enum: ['admin', 'manager', 'employee', 'accountant'],
              example: 'employee',
            },
            isActive: {
              type: 'boolean',
              example: true,
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        // نماذج العميل
        Customer: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '60d21b4667d0d8992e610c86',
            },
            name: {
              type: 'string',
              example: 'ABC Company',
            },
            email: {
              type: 'string',
              example: '<EMAIL>',
            },
            phone: {
              type: 'string',
              example: '+**********',
            },
            address: {
              type: 'string',
              example: '123 Main St, City',
            },
            type: {
              type: 'string',
              enum: ['individual', 'company', 'government'],
              example: 'company',
            },
            taxId: {
              type: 'string',
              example: 'TX12345',
            },
            creditLimit: {
              type: 'number',
              example: 5000,
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        // نماذج المنتج
        Product: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '60d21b4667d0d8992e610c87',
            },
            name: {
              type: 'string',
              example: 'Business Cards',
            },
            description: {
              type: 'string',
              example: 'High quality business cards',
            },
            category: {
              type: 'string',
              example: 'printing',
            },
            price: {
              type: 'number',
              example: 50,
            },
            cost: {
              type: 'number',
              example: 20,
            },
            stockQuantity: {
              type: 'number',
              example: 1000,
            },
            unit: {
              type: 'string',
              example: 'pack',
            },
            images: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['image1.jpg', 'image2.jpg'],
            },
            isActive: {
              type: 'boolean',
              example: true,
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        // نماذج الطلب
        Order: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '60d21b4667d0d8992e610c88',
            },
            orderNumber: {
              type: 'string',
              example: 'ORD-2023-001',
            },
            customer: {
              type: 'string',
              example: '60d21b4667d0d8992e610c86',
            },
            items: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  product: {
                    type: 'string',
                    example: '60d21b4667d0d8992e610c87',
                  },
                  quantity: {
                    type: 'number',
                    example: 2,
                  },
                  price: {
                    type: 'number',
                    example: 50,
                  },
                  discount: {
                    type: 'number',
                    example: 5,
                  },
                  total: {
                    type: 'number',
                    example: 95,
                  },
                },
              },
            },
            status: {
              type: 'string',
              enum: ['pending', 'processing', 'completed', 'cancelled'],
              example: 'processing',
            },
            subtotal: {
              type: 'number',
              example: 100,
            },
            tax: {
              type: 'number',
              example: 15,
            },
            discount: {
              type: 'number',
              example: 10,
            },
            total: {
              type: 'number',
              example: 105,
            },
            notes: {
              type: 'string',
              example: 'Deliver to reception',
            },
            createdBy: {
              type: 'string',
              example: '60d21b4667d0d8992e610c85',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        // نماذج الفاتورة
        Invoice: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '60d21b4667d0d8992e610c89',
            },
            invoiceNumber: {
              type: 'string',
              example: 'INV-2023-001',
            },
            order: {
              type: 'string',
              example: '60d21b4667d0d8992e610c88',
            },
            customer: {
              type: 'string',
              example: '60d21b4667d0d8992e610c86',
            },
            issueDate: {
              type: 'string',
              format: 'date',
              example: '2023-01-15',
            },
            dueDate: {
              type: 'string',
              format: 'date',
              example: '2023-02-15',
            },
            status: {
              type: 'string',
              enum: ['draft', 'issued', 'paid', 'partially_paid', 'overdue', 'cancelled'],
              example: 'issued',
            },
            items: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  description: {
                    type: 'string',
                    example: 'Business Cards',
                  },
                  quantity: {
                    type: 'number',
                    example: 2,
                  },
                  price: {
                    type: 'number',
                    example: 50,
                  },
                  discount: {
                    type: 'number',
                    example: 5,
                  },
                  total: {
                    type: 'number',
                    example: 95,
                  },
                },
              },
            },
            subtotal: {
              type: 'number',
              example: 100,
            },
            tax: {
              type: 'number',
              example: 15,
            },
            discount: {
              type: 'number',
              example: 10,
            },
            total: {
              type: 'number',
              example: 105,
            },
            amountPaid: {
              type: 'number',
              example: 50,
            },
            amountDue: {
              type: 'number',
              example: 55,
            },
            notes: {
              type: 'string',
              example: 'Payment due within 30 days',
            },
            createdBy: {
              type: 'string',
              example: '60d21b4667d0d8992e610c85',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        // نماذج المدفوعات
        Payment: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '60d21b4667d0d8992e610c90',
            },
            paymentNumber: {
              type: 'string',
              example: 'PAY-2023-001',
            },
            invoice: {
              type: 'string',
              example: '60d21b4667d0d8992e610c89',
            },
            customer: {
              type: 'string',
              example: '60d21b4667d0d8992e610c86',
            },
            amount: {
              type: 'number',
              example: 50,
            },
            method: {
              type: 'string',
              enum: ['cash', 'credit_card', 'bank_transfer', 'check', 'other'],
              example: 'credit_card',
            },
            status: {
              type: 'string',
              enum: ['pending', 'completed', 'failed', 'refunded'],
              example: 'completed',
            },
            transactionId: {
              type: 'string',
              example: 'txn_123456789',
            },
            paymentDate: {
              type: 'string',
              format: 'date-time',
            },
            notes: {
              type: 'string',
              example: 'Partial payment',
            },
            createdBy: {
              type: 'string',
              example: '60d21b4667d0d8992e610c85',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
      },
      responses: {
        BadRequest: {
          description: 'Bad Request',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error',
              },
            },
          },
        },
        Unauthorized: {
          description: 'Unauthorized',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/UnauthorizedError',
              },
            },
          },
        },
        Forbidden: {
          description: 'Forbidden',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ForbiddenError',
              },
            },
          },
        },
        NotFound: {
          description: 'Not Found',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/NotFoundError',
              },
            },
          },
        },
        ValidationError: {
          description: 'Validation Error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ValidationError',
              },
            },
          },
        },
        ServerError: {
          description: 'Internal Server Error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ServerError',
              },
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: [
    path.join(__dirname, '../routes/**/*.js'),
    path.join(__dirname, '../models/**/*.js'),
    path.join(__dirname, '../controllers/**/*.js'),
  ],
};

/**
 * إنشاء مواصفات Swagger
 */
const swaggerSpec = swaggerJsdoc(swaggerOptions);

/**
 * إعداد خيارات واجهة Swagger
 */
const swaggerUiOptions = {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: `${appConfig.name} API Documentation`,
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    docExpansion: 'none',
    filter: true,
    displayRequestDuration: true,
  },
};

/**
 * إعداد Swagger في التطبيق
 * @param {Object} app - تطبيق Express
 */
const setupSwagger = (app) => {
  // مسار توثيق API
  const apiDocsPath = '/api-docs';
  
  // إعداد مسار توثيق API
  app.use(apiDocsPath, swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));
  
  // إعداد مسار JSON لمواصفات Swagger
  app.get(`${apiDocsPath}.json`, (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });
  
  // سجل معلومات عن توثيق API
  const apiDocsUrl = `${appConfig.baseUrl || `http://localhost:${appConfig.port || 3000}`}${apiDocsPath}`;
  logger.info(`توثيق API متاح على: ${apiDocsUrl}`);
  
  return apiDocsUrl;
};

module.exports = {
  setupSwagger,
  swaggerSpec,
};