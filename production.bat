@echo off
echo ========================================
echo    نظام المطبعة المتطور - الإصدار 2.0
echo           بيئة الإنتاج
echo ========================================
echo.

echo جاري تحضير النظام للإنتاج...
echo.

echo 1. بناء الواجهة الأمامية...
cd frontend
npm run build
cd ..
echo.

echo 2. نسخ ملفات البناء إلى الواجهة الخلفية...
if exist "backend\client" rmdir /s /q "backend\client"
xcopy "frontend\build" "backend\client" /e /i /h /y
echo.

echo 3. تشغيل الخادم في بيئة الإنتاج...
cd backend
set NODE_ENV=production
npm start
cd ..

echo.
echo اضغط أي مفتاح للخروج...
pause > nul
