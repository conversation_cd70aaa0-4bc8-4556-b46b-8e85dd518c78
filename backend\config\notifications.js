/**
 * ملف إعداد وتكوين الإشعارات
 * يقوم بإعداد وتكوين نظام الإشعارات للتطبيق
 */

const config = require('config');
const logger = require('../utils/logger');
const { recordError } = require('./monitoring');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

// قائمة مستمعي الإشعارات
const notificationListeners = new Map();

// قائمة قنوات الإشعارات
const notificationChannels = new Map();

// أنواع الإشعارات
const NOTIFICATION_TYPES = {
  // إشعارات المستخدمين
  USER_REGISTERED: 'user.registered',
  USER_UPDATED: 'user.updated',
  USER_DELETED: 'user.deleted',
  USER_PASSWORD_RESET: 'user.password.reset',
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  
  // إشعارات العملاء
  CUSTOMER_CREATED: 'customer.created',
  CUSTOMER_UPDATED: 'customer.updated',
  CUSTOMER_DELETED: 'customer.deleted',
  
  // إشعارات الطلبات
  ORDER_CREATED: 'order.created',
  ORDER_UPDATED: 'order.updated',
  ORDER_STATUS_CHANGED: 'order.status.changed',
  ORDER_COMPLETED: 'order.completed',
  ORDER_CANCELLED: 'order.cancelled',
  ORDER_PAYMENT_RECEIVED: 'order.payment.received',
  
  // إشعارات المخزون
  INVENTORY_LOW: 'inventory.low',
  INVENTORY_OUT_OF_STOCK: 'inventory.out_of_stock',
  INVENTORY_UPDATED: 'inventory.updated',
  
  // إشعارات الفواتير
  INVOICE_CREATED: 'invoice.created',
  INVOICE_UPDATED: 'invoice.updated',
  INVOICE_PAID: 'invoice.paid',
  INVOICE_OVERDUE: 'invoice.overdue',
  
  // إشعارات الموردين
  SUPPLIER_CREATED: 'supplier.created',
  SUPPLIER_UPDATED: 'supplier.updated',
  SUPPLIER_DELETED: 'supplier.deleted',
  
  // إشعارات النظام
  SYSTEM_ERROR: 'system.error',
  SYSTEM_WARNING: 'system.warning',
  SYSTEM_INFO: 'system.info',
  SYSTEM_BACKUP_COMPLETED: 'system.backup.completed',
  SYSTEM_BACKUP_FAILED: 'system.backup.failed',
};

// مستويات أهمية الإشعارات
const NOTIFICATION_PRIORITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
};

/**
 * تسجيل قناة إشعارات جديدة
 * @param {string} channelName - اسم القناة
 * @param {Function} sendFunction - دالة إرسال الإشعارات
 * @param {Object} options - خيارات إضافية
 * @returns {boolean} نجاح العملية
 */
const registerChannel = (channelName, sendFunction, options = {}) => {
  try {
    if (typeof sendFunction !== 'function') {
      throw new Error(`دالة الإرسال غير صالحة للقناة: ${channelName}`);
    }
    
    notificationChannels.set(channelName, {
      send: sendFunction,
      enabled: options.enabled !== false,
      supportedTypes: options.supportedTypes || Object.values(NOTIFICATION_TYPES),
      supportedPriorities: options.supportedPriorities || Object.values(NOTIFICATION_PRIORITIES),
      options,
    });
    
    logger.info(`تم تسجيل قناة الإشعارات: ${channelName}`);
    return true;
  } catch (error) {
    logger.error(`فشل في تسجيل قناة الإشعارات: ${channelName}`, error);
    recordError(error);
    return false;
  }
};

/**
 * إلغاء تسجيل قناة إشعارات
 * @param {string} channelName - اسم القناة
 * @returns {boolean} نجاح العملية
 */
const unregisterChannel = (channelName) => {
  try {
    if (notificationChannels.has(channelName)) {
      notificationChannels.delete(channelName);
      logger.info(`تم إلغاء تسجيل قناة الإشعارات: ${channelName}`);
      return true;
    }
    
    logger.warn(`محاولة إلغاء تسجيل قناة إشعارات غير موجودة: ${channelName}`);
    return false;
  } catch (error) {
    logger.error(`فشل في إلغاء تسجيل قناة الإشعارات: ${channelName}`, error);
    recordError(error);
    return false;
  }
};

/**
 * تمكين أو تعطيل قناة إشعارات
 * @param {string} channelName - اسم القناة
 * @param {boolean} enabled - حالة التمكين
 * @returns {boolean} نجاح العملية
 */
const setChannelEnabled = (channelName, enabled) => {
  try {
    if (notificationChannels.has(channelName)) {
      const channel = notificationChannels.get(channelName);
      channel.enabled = enabled;
      notificationChannels.set(channelName, channel);
      
      logger.info(`تم ${enabled ? 'تمكين' : 'تعطيل'} قناة الإشعارات: ${channelName}`);
      return true;
    }
    
    logger.warn(`محاولة تغيير حالة قناة إشعارات غير موجودة: ${channelName}`);
    return false;
  } catch (error) {
    logger.error(`فشل في تغيير حالة قناة الإشعارات: ${channelName}`, error);
    recordError(error);
    return false;
  }
};

/**
 * إضافة مستمع للإشعارات
 * @param {string} eventType - نوع الحدث
 * @param {Function} listenerFunction - دالة المستمع
 * @param {Object} options - خيارات إضافية
 * @returns {string} معرف المستمع
 */
const addListener = (eventType, listenerFunction, options = {}) => {
  try {
    if (typeof listenerFunction !== 'function') {
      throw new Error(`دالة المستمع غير صالحة للحدث: ${eventType}`);
    }
    
    const listenerId = `${eventType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    if (!notificationListeners.has(eventType)) {
      notificationListeners.set(eventType, new Map());
    }
    
    notificationListeners.get(eventType).set(listenerId, {
      listener: listenerFunction,
      options,
    });
    
    logger.debug(`تم إضافة مستمع للإشعارات: ${eventType} (${listenerId})`);
    return listenerId;
  } catch (error) {
    logger.error(`فشل في إضافة مستمع للإشعارات: ${eventType}`, error);
    recordError(error);
    throw error;
  }
};

/**
 * إزالة مستمع للإشعارات
 * @param {string} listenerId - معرف المستمع
 * @returns {boolean} نجاح العملية
 */
const removeListener = (listenerId) => {
  try {
    for (const [eventType, listeners] of notificationListeners.entries()) {
      if (listeners.has(listenerId)) {
        listeners.delete(listenerId);
        
        // إزالة نوع الحدث إذا لم يعد هناك مستمعين
        if (listeners.size === 0) {
          notificationListeners.delete(eventType);
        }
        
        logger.debug(`تم إزالة مستمع للإشعارات: ${eventType} (${listenerId})`);
        return true;
      }
    }
    
    logger.warn(`محاولة إزالة مستمع غير موجود: ${listenerId}`);
    return false;
  } catch (error) {
    logger.error(`فشل في إزالة مستمع للإشعارات: ${listenerId}`, error);
    recordError(error);
    return false;
  }
};

/**
 * إرسال إشعار
 * @param {string} type - نوع الإشعار
 * @param {Object} data - بيانات الإشعار
 * @param {Object} options - خيارات الإشعار
 * @returns {Promise<Object>} نتيجة الإرسال
 */
const sendNotification = async (type, data = {}, options = {}) => {
  const startTime = Date.now();
  const results = {
    success: false,
    channels: {},
    listeners: 0,
    errors: [],
  };
  
  try {
    // التحقق من صحة نوع الإشعار
    if (!Object.values(NOTIFICATION_TYPES).includes(type)) {
      logger.warn(`نوع إشعار غير معروف: ${type}`);
    }
    
    // إعداد بيانات الإشعار
    const notification = {
      id: options.id || `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: options.timestamp || new Date(),
      priority: options.priority || NOTIFICATION_PRIORITIES.MEDIUM,
      sender: options.sender || 'system',
      recipients: options.recipients || [],
      metadata: options.metadata || {},
    };
    
    // تنفيذ المستمعين
    let listenerCount = 0;
    if (notificationListeners.has(type)) {
      const listeners = notificationListeners.get(type);
      
      for (const [listenerId, listenerData] of listeners.entries()) {
        try {
          await listenerData.listener(notification);
          listenerCount++;
        } catch (error) {
          logger.error(`فشل في تنفيذ مستمع الإشعارات: ${type} (${listenerId})`, error);
          results.errors.push({
            type: 'listener',
            id: listenerId,
            error: error.message,
          });
        }
      }
    }
    
    results.listeners = listenerCount;
    
    // إرسال الإشعار عبر القنوات المسجلة
    for (const [channelName, channel] of notificationChannels.entries()) {
      // تخطي القنوات المعطلة
      if (!channel.enabled) {
        results.channels[channelName] = { skipped: true, reason: 'disabled' };
        continue;
      }
      
      // تخطي القنوات التي لا تدعم نوع الإشعار
      if (!channel.supportedTypes.includes(type)) {
        results.channels[channelName] = { skipped: true, reason: 'unsupported_type' };
        continue;
      }
      
      // تخطي القنوات التي لا تدعم مستوى أهمية الإشعار
      if (!channel.supportedPriorities.includes(notification.priority)) {
        results.channels[channelName] = { skipped: true, reason: 'unsupported_priority' };
        continue;
      }
      
      try {
        const channelResult = await channel.send(notification);
        results.channels[channelName] = { success: true, result: channelResult };
      } catch (error) {
        logger.error(`فشل في إرسال الإشعار عبر القناة: ${channelName}`, error);
        results.channels[channelName] = { success: false, error: error.message };
        results.errors.push({
          type: 'channel',
          id: channelName,
          error: error.message,
        });
      }
    }
    
    // تحديد نجاح العملية بشكل عام
    const successfulChannels = Object.values(results.channels).filter(r => r.success).length;
    results.success = successfulChannels > 0 || listenerCount > 0;
    
    const duration = Date.now() - startTime;
    logger.info(`تم إرسال الإشعار: ${type} (${duration}ms)`);
    
    return results;
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(`فشل في إرسال الإشعار: ${type} (${duration}ms)`, error);
    recordError(error);
    
    results.success = false;
    results.errors.push({
      type: 'general',
      error: error.message,
    });
    
    return results;
  }
};

/**
 * إعداد قنوات الإشعارات الافتراضية
 */
const setupDefaultChannels = () => {
  // قناة سجل النظام
  registerChannel('logger', async (notification) => {
    const logLevel = notification.priority === NOTIFICATION_PRIORITIES.CRITICAL ? 'error' :
                     notification.priority === NOTIFICATION_PRIORITIES.HIGH ? 'warn' :
                     notification.priority === NOTIFICATION_PRIORITIES.MEDIUM ? 'info' : 'debug';
    
    logger[logLevel](`إشعار: ${notification.type}`, {
      notificationId: notification.id,
      data: notification.data,
      timestamp: notification.timestamp,
      sender: notification.sender,
      recipients: notification.recipients,
    });
    
    return { logged: true };
  });
  
  // قناة البريد الإلكتروني (تحتاج إلى تكوين البريد الإلكتروني)
  registerChannel('email', async (notification) => {
    // تخطي الإشعارات التي لا تحتوي على مستلمين
    if (!notification.recipients || notification.recipients.length === 0) {
      return { skipped: true, reason: 'no_recipients' };
    }
    
    try {
      const emailConfig = require('./email');
      
      // تحديد قالب البريد الإلكتروني بناءً على نوع الإشعار
      let templateName = 'notification';
      let subject = 'إشعار جديد';
      
      switch (notification.type) {
        case NOTIFICATION_TYPES.ORDER_CREATED:
          templateName = 'order-created';
          subject = 'تم إنشاء طلب جديد';
          break;
        case NOTIFICATION_TYPES.INVOICE_CREATED:
          templateName = 'invoice-created';
          subject = 'فاتورة جديدة';
          break;
        case NOTIFICATION_TYPES.INVENTORY_LOW:
          templateName = 'inventory-low';
          subject = 'تنبيه: مخزون منخفض';
          break;
        // يمكن إضافة المزيد من أنواع الإشعارات هنا
      }
      
      // إرسال البريد الإلكتروني لكل مستلم
      const results = [];
      
      for (const recipient of notification.recipients) {
        try {
          const result = await emailConfig.sendEmail({
            to: recipient.email || recipient,
            subject,
            template: templateName,
            context: {
              notification,
              recipient,
              appName: appConfig.name || 'نظام المطبعة',
              year: new Date().getFullYear(),
            },
          });
          
          results.push({ recipient, success: true, messageId: result.messageId });
        } catch (error) {
          logger.error(`فشل في إرسال بريد إلكتروني للمستلم: ${recipient.email || recipient}`, error);
          results.push({ recipient, success: false, error: error.message });
        }
      }
      
      return { sent: results };
    } catch (error) {
      logger.error('فشل في تحميل تكوين البريد الإلكتروني', error);
      throw new Error('تكوين البريد الإلكتروني غير متاح');
    }
  }, {
    supportedPriorities: [NOTIFICATION_PRIORITIES.MEDIUM, NOTIFICATION_PRIORITIES.HIGH, NOTIFICATION_PRIORITIES.CRITICAL],
  });
  
  // قناة الإشعارات داخل التطبيق (سيتم تخزينها في قاعدة البيانات)
  registerChannel('in-app', async (notification) => {
    // هذه مجرد محاكاة، في التطبيق الفعلي سيتم تخزين الإشعارات في قاعدة البيانات
    logger.debug('تخزين إشعار داخل التطبيق', notification);
    
    // يمكن إضافة رمز لتخزين الإشعار في قاعدة البيانات هنا
    
    return { stored: true };
  });
  
  logger.info('تم إعداد قنوات الإشعارات الافتراضية');
};

/**
 * إعداد مستمعي الإشعارات الافتراضيين
 */
const setupDefaultListeners = () => {
  // مستمع لإشعارات الأخطاء في النظام
  addListener(NOTIFICATION_TYPES.SYSTEM_ERROR, async (notification) => {
    // يمكن إضافة منطق إضافي لمعالجة أخطاء النظام هنا
    logger.error('تم استلام إشعار خطأ في النظام', notification.data);
  });
  
  // مستمع لإشعارات المخزون المنخفض
  addListener(NOTIFICATION_TYPES.INVENTORY_LOW, async (notification) => {
    // يمكن إضافة منطق لإنشاء طلبات توريد تلقائية هنا
    logger.info('تم استلام إشعار مخزون منخفض', notification.data);
  });
  
  // مستمع لإشعارات الفواتير المتأخرة
  addListener(NOTIFICATION_TYPES.INVOICE_OVERDUE, async (notification) => {
    // يمكن إضافة منطق لإرسال تذكيرات إضافية هنا
    logger.info('تم استلام إشعار فاتورة متأخرة', notification.data);
  });
  
  logger.info('تم إعداد مستمعي الإشعارات الافتراضيين');
};

/**
 * إعداد نظام الإشعارات
 */
const setupNotifications = () => {
  try {
    // إعداد القنوات الافتراضية
    setupDefaultChannels();
    
    // إعداد المستمعين الافتراضيين
    setupDefaultListeners();
    
    logger.info('تم إعداد نظام الإشعارات بنجاح');
    return true;
  } catch (error) {
    logger.error('فشل في إعداد نظام الإشعارات', error);
    recordError(error);
    return false;
  }
};

module.exports = {
  // الثوابت
  NOTIFICATION_TYPES,
  NOTIFICATION_PRIORITIES,
  
  // وظائف إدارة القنوات
  registerChannel,
  unregisterChannel,
  setChannelEnabled,
  
  // وظائف إدارة المستمعين
  addListener,
  removeListener,
  
  // وظائف الإشعارات
  sendNotification,
  setupNotifications,
  
  // وظائف الإعداد
  setupDefaultChannels,
  setupDefaultListeners,
};