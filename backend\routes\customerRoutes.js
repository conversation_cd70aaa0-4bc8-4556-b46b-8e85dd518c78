const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const customerController = require('../controllers/customerController');
const auth = require('../middleware/auth');
const checkPermission = require('../middleware/checkPermission');

/**
 * @route   POST api/customers
 * @desc    إنشاء عميل جديد
 * @access  Private
 */
router.post(
  '/',
  [
    auth,
    checkPermission('customers', 'create'),
    [
      check('name', 'اسم العميل مطلوب').not().isEmpty(),
      check('email', 'يرجى إدخال بريد إلكتروني صحيح').isEmail(),
      check('phone', 'رقم الهاتف مطلوب').not().isEmpty(),
      check('customerType', 'نوع العميل مطلوب').not().isEmpty()
    ]
  ],
  customerController.createCustomer
);

/**
 * @route   GET api/customers
 * @desc    الحصول على قائمة العملاء
 * @access  Private
 */
router.get(
  '/',
  [auth, checkPermission('customers', 'read')],
  customerController.getCustomers
);

/**
 * @route   GET api/customers/:id
 * @desc    الحصول على عميل محدد
 * @access  Private
 */
router.get(
  '/:id',
  [auth, checkPermission('customers', 'read')],
  customerController.getCustomerById
);

/**
 * @route   PUT api/customers/:id
 * @desc    تحديث عميل
 * @access  Private
 */
router.put(
  '/:id',
  [
    auth,
    checkPermission('customers', 'update'),
    [
      check('name', 'اسم العميل مطلوب').optional().not().isEmpty(),
      check('email', 'يرجى إدخال بريد إلكتروني صحيح').optional().isEmail(),
      check('phone', 'رقم الهاتف مطلوب').optional().not().isEmpty(),
      check('customerType', 'نوع العميل مطلوب').optional().not().isEmpty()
    ]
  ],
  customerController.updateCustomer
);

/**
 * @route   DELETE api/customers/:id
 * @desc    حذف عميل
 * @access  Private
 */
router.delete(
  '/:id',
  [auth, checkPermission('customers', 'delete')],
  customerController.deleteCustomer
);

module.exports = router;