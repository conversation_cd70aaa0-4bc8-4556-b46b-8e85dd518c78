# إعدادات التطبيق
NODE_ENV=development
PORT=5000

# إعدادات قاعدة البيانات
MONGODB_URI=mongodb://localhost:27017/print_management_system_v2

# إعدادات JWT
JWT_SECRET=print_management_secret_key_v2_change_this_in_production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# إعدادات الواجهة الأمامية
FRONTEND_URL=http://localhost:3000

# إعدادات الملفات
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# إعدادات البريد الإلكتروني
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=نظام المطبعة <<EMAIL>>

# إعدادات SMS
SMS_PROVIDER=taqnyat
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=your_sms_api_secret
SMS_SENDER_NAME=PrintSys

# إعدادات الأمان
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=1800000

# إعدادات أخرى
JOBS_ENABLED=false
REDIS_URL=redis://localhost:6379
