const Order = require('../models/Order');
const Customer = require('../models/Customer');
const Inventory = require('../models/Inventory');
const { validationResult } = require('express-validator');

/**
 * @route   POST api/orders
 * @desc    إنشاء طلب جديد
 * @access  Private
 */
exports.createOrder = async (req, res) => {
  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const {
    customer,
    dueDate,
    items,
    notes,
    internalNotes,
    priority,
    files
  } = req.body;

  try {
    // التحقق من وجود العميل
    const customerExists = await Customer.findById(customer);
    if (!customerExists) {
      return res.status(404).json({ msg: 'العميل غير موجود' });
    }

    // حساب المجاميع
    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
    const taxRate = 15; // ضريبة القيمة المضافة 15%
    const discount = req.body.discount || 0;
    const taxAmount = (subtotal - discount) * (taxRate / 100);
    const totalAmount = subtotal - discount + taxAmount;

    // إنشاء طلب جديد
    const order = new Order({
      customer,
      dueDate,
      items,
      notes,
      internalNotes,
      priority,
      files,
      subtotal,
      taxRate,
      discount,
      taxAmount,
      totalAmount,
      createdBy: req.user.id
    });

    // حفظ الطلب في قاعدة البيانات
    await order.save();

    res.json(order);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/orders
 * @desc    الحصول على قائمة الطلبات
 * @access  Private
 */
exports.getOrders = async (req, res) => {
  try {
    // البحث عن الطلبات مع إمكانية التصفية
    const query = {};
    
    // تصفية حسب العميل
    if (req.query.customer) {
      query.customer = req.query.customer;
    }
    
    // تصفية حسب الحالة
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    // تصفية حسب الأولوية
    if (req.query.priority) {
      query.priority = req.query.priority;
    }
    
    // تصفية حسب حالة الدفع
    if (req.query.paymentStatus) {
      query.paymentStatus = req.query.paymentStatus;
    }
    
    // البحث بالرقم
    if (req.query.search) {
      query.$or = [
        { orderNumber: { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    // تصفية حسب التاريخ
    if (req.query.startDate && req.query.endDate) {
      query.orderDate = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    }
    
    // الحصول على الطلبات مع الترتيب والتقسيم
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const orders = await Order.find(query)
      .populate('customer', 'name email phone')
      .populate('createdBy', 'name')
      .sort({ orderDate: -1 })
      .skip(skip)
      .limit(limit);
    
    // الحصول على إجمالي عدد الطلبات للتصفح
    const total = await Order.countDocuments(query);
    
    res.json({
      orders,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/orders/:id
 * @desc    الحصول على طلب محدد
 * @access  Private
 */
exports.getOrderById = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('customer', 'name email phone address')
      .populate('createdBy', 'name')
      .populate('productionStages.assignedTo', 'name');

    if (!order) {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }

    res.json(order);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   PUT api/orders/:id
 * @desc    تحديث طلب
 * @access  Private
 */
exports.updateOrder = async (req, res) => {
  const {
    customer,
    dueDate,
    items,
    notes,
    internalNotes,
    priority,
    files,
    status,
    discount,
    paymentStatus,
    paidAmount
  } = req.body;

  // بناء كائن تحديث الطلب
  const orderFields = {};
  if (customer) orderFields.customer = customer;
  if (dueDate) orderFields.dueDate = dueDate;
  if (items) orderFields.items = items;
  if (notes) orderFields.notes = notes;
  if (internalNotes) orderFields.internalNotes = internalNotes;
  if (priority) orderFields.priority = priority;
  if (files) orderFields.files = files;
  if (status) orderFields.status = status;
  if (discount !== undefined) orderFields.discount = discount;
  if (paymentStatus) orderFields.paymentStatus = paymentStatus;
  if (paidAmount !== undefined) orderFields.paidAmount = paidAmount;

  // إعادة حساب المجاميع إذا تم تغيير العناصر أو الخصم
  if (items || discount !== undefined) {
    const subtotal = items
      ? items.reduce((sum, item) => sum + item.totalPrice, 0)
      : req.body.subtotal;
    
    const taxRate = 15; // ضريبة القيمة المضافة 15%
    const discountValue = discount !== undefined ? discount : (req.body.discount || 0);
    const taxAmount = (subtotal - discountValue) * (taxRate / 100);
    const totalAmount = subtotal - discountValue + taxAmount;
    
    orderFields.subtotal = subtotal;
    orderFields.taxRate = taxRate;
    orderFields.taxAmount = taxAmount;
    orderFields.totalAmount = totalAmount;
  }

  try {
    let order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }

    // تحديث الطلب
    order = await Order.findByIdAndUpdate(
      req.params.id,
      { $set: orderFields },
      { new: true }
    );

    res.json(order);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   PUT api/orders/status/:id
 * @desc    تحديث حالة الطلب
 * @access  Private
 */
exports.updateOrderStatus = async (req, res) => {
  const { status } = req.body;

  try {
    let order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }

    // تحديث حالة الطلب
    order = await Order.findByIdAndUpdate(
      req.params.id,
      { $set: { status } },
      { new: true }
    );

    res.json(order);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   PUT api/orders/production/:id
 * @desc    تحديث مراحل الإنتاج للطلب
 * @access  Private
 */
exports.updateProductionStages = async (req, res) => {
  const { productionStages } = req.body;

  try {
    let order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }

    // تحديث مراحل الإنتاج
    order = await Order.findByIdAndUpdate(
      req.params.id,
      { $set: { productionStages } },
      { new: true }
    );

    res.json(order);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   DELETE api/orders/:id
 * @desc    حذف طلب
 * @access  Private
 */
exports.deleteOrder = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }

    // حذف الطلب
    await Order.findByIdAndRemove(req.params.id);

    res.json({ msg: 'تم حذف الطلب' });
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'الطلب غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/orders/dashboard/stats
 * @desc    الحصول على إحصائيات الطلبات للوحة المعلومات
 * @access  Private
 */
exports.getOrderStats = async (req, res) => {
  try {
    // إجمالي عدد الطلبات
    const totalOrders = await Order.countDocuments();
    
    // عدد الطلبات حسب الحالة
    const ordersByStatus = await Order.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);
    
    // عدد الطلبات حسب الأولوية
    const ordersByPriority = await Order.aggregate([
      { $group: { _id: '$priority', count: { $sum: 1 } } }
    ]);
    
    // إجمالي المبيعات
    const totalSales = await Order.aggregate([
      { $group: { _id: null, total: { $sum: '$totalAmount' } } }
    ]);
    
    // الطلبات المتأخرة (تاريخ الاستحقاق أقل من اليوم والحالة ليست مكتملة)
    const overdueOrders = await Order.countDocuments({
      dueDate: { $lt: new Date() },
      status: { $nin: ['delivered', 'cancelled'] }
    });
    
    // الطلبات حسب الشهر (للرسم البياني)
    const ordersByMonth = await Order.aggregate([
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m', date: '$orderDate' } },
          count: { $sum: 1 },
          revenue: { $sum: '$totalAmount' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);
    
    res.json({
      totalOrders,
      ordersByStatus,
      ordersByPriority,
      totalSales: totalSales.length > 0 ? totalSales[0].total : 0,
      overdueOrders,
      ordersByMonth
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};