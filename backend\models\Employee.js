const mongoose = require('mongoose');

/**
 * نموذج الموظف
 */
const EmployeeSchema = new mongoose.Schema({
  // المعلومات الشخصية
  employeeId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  
  firstName: {
    type: String,
    required: [true, 'الاسم الأول مطلوب'],
    trim: true
  },
  
  lastName: {
    type: String,
    required: [true, 'اسم العائلة مطلوب'],
    trim: true
  },
  
  arabicName: {
    type: String,
    required: [true, 'الاسم بالعربية مطلوب'],
    trim: true
  },
  
  nationalId: {
    type: String,
    required: [true, 'رقم الهوية مطلوب'],
    unique: true,
    trim: true,
    match: [/^\d{10}$/, 'رقم الهوية يجب أن يكون 10 أرقام']
  },
  
  email: {
    type: String,
    required: [true, 'البريد الإلكتروني مطلوب'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'البريد الإلكتروني غير صحيح']
  },
  
  phone: {
    type: String,
    required: [true, 'رقم الهاتف مطلوب'],
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'رقم الهاتف غير صحيح']
  },
  
  emergencyContact: {
    name: { type: String, trim: true },
    relationship: { type: String, trim: true },
    phone: { type: String, trim: true }
  },
  
  dateOfBirth: {
    type: Date,
    required: [true, 'تاريخ الميلاد مطلوب']
  },
  
  gender: {
    type: String,
    enum: ['male', 'female'],
    required: [true, 'الجنس مطلوب']
  },
  
  nationality: {
    type: String,
    default: 'سعودي',
    trim: true
  },
  
  maritalStatus: {
    type: String,
    enum: ['single', 'married', 'divorced', 'widowed'],
    default: 'single'
  },
  
  // معلومات العنوان
  address: {
    street: { type: String, trim: true },
    city: { type: String, trim: true },
    district: { type: String, trim: true },
    postalCode: { type: String, trim: true },
    country: { type: String, default: 'المملكة العربية السعودية' }
  },
  
  // معلومات الوظيفة
  department: {
    type: String,
    required: [true, 'القسم مطلوب'],
    enum: ['administration', 'sales', 'production', 'accounting', 'hr', 'it', 'maintenance'],
    trim: true
  },
  
  position: {
    type: String,
    required: [true, 'المنصب مطلوب'],
    trim: true
  },
  
  jobLevel: {
    type: String,
    enum: ['junior', 'senior', 'supervisor', 'manager', 'director'],
    default: 'junior'
  },
  
  directManager: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee'
  },
  
  hireDate: {
    type: Date,
    required: [true, 'تاريخ التوظيف مطلوب'],
    default: Date.now
  },
  
  contractType: {
    type: String,
    enum: ['permanent', 'temporary', 'contract', 'part_time'],
    default: 'permanent'
  },
  
  workSchedule: {
    type: String,
    enum: ['full_time', 'part_time', 'shift_work'],
    default: 'full_time'
  },
  
  // معلومات الراتب
  salary: {
    basic: {
      type: Number,
      required: [true, 'الراتب الأساسي مطلوب'],
      min: 0
    },
    allowances: {
      housing: { type: Number, default: 0 },
      transportation: { type: Number, default: 0 },
      food: { type: Number, default: 0 },
      other: { type: Number, default: 0 }
    },
    deductions: {
      insurance: { type: Number, default: 0 },
      tax: { type: Number, default: 0 },
      other: { type: Number, default: 0 }
    }
  },
  
  // معلومات البنك
  bankInfo: {
    bankName: { type: String, trim: true },
    accountNumber: { type: String, trim: true },
    iban: { type: String, trim: true }
  },
  
  // الحالة
  status: {
    type: String,
    enum: ['active', 'inactive', 'terminated', 'suspended'],
    default: 'active'
  },
  
  terminationDate: {
    type: Date
  },
  
  terminationReason: {
    type: String,
    trim: true
  },
  
  // الإجازات
  leaveBalance: {
    annual: { type: Number, default: 21 }, // 21 يوم إجازة سنوية
    sick: { type: Number, default: 30 }, // 30 يوم إجازة مرضية
    emergency: { type: Number, default: 5 }, // 5 أيام إجازة طارئة
    maternity: { type: Number, default: 70 }, // 70 يوم إجازة أمومة
    paternity: { type: Number, default: 3 } // 3 أيام إجازة أبوة
  },
  
  // المؤهلات والشهادات
  qualifications: [{
    degree: { type: String, trim: true },
    institution: { type: String, trim: true },
    year: { type: Number },
    field: { type: String, trim: true }
  }],
  
  certifications: [{
    name: { type: String, trim: true },
    issuer: { type: String, trim: true },
    issueDate: { type: Date },
    expiryDate: { type: Date }
  }],
  
  // المهارات
  skills: [{
    name: { type: String, trim: true },
    level: { type: String, enum: ['beginner', 'intermediate', 'advanced', 'expert'] }
  }],
  
  // الملفات والمستندات
  documents: [{
    type: { type: String, trim: true },
    filename: { type: String, trim: true },
    path: { type: String, trim: true },
    uploadDate: { type: Date, default: Date.now }
  }],
  
  // الملاحظات
  notes: {
    type: String,
    trim: true
  },
  
  // معلومات المستخدم المرتبط
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // صورة الموظف
  avatar: {
    type: String,
    default: '/assets/images/default-avatar.png'
  }
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// فهرسة رقم الموظف
EmployeeSchema.index({ employeeId: 1 });

// فهرسة رقم الهوية
EmployeeSchema.index({ nationalId: 1 });

// فهرسة البريد الإلكتروني
EmployeeSchema.index({ email: 1 });

// فهرسة القسم
EmployeeSchema.index({ department: 1 });

// فهرسة الحالة
EmployeeSchema.index({ status: 1 });

// فهرسة تاريخ التوظيف
EmployeeSchema.index({ hireDate: -1 });

// خاصية افتراضية للاسم الكامل
EmployeeSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// خاصية افتراضية للعمر
EmployeeSchema.virtual('age').get(function() {
  if (!this.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
});

// خاصية افتراضية لسنوات الخبرة
EmployeeSchema.virtual('yearsOfService').get(function() {
  if (!this.hireDate) return 0;
  const today = new Date();
  const hireDate = new Date(this.hireDate);
  return Math.floor((today - hireDate) / (365.25 * 24 * 60 * 60 * 1000));
});

// خاصية افتراضية للراتب الإجمالي
EmployeeSchema.virtual('totalSalary').get(function() {
  if (!this.salary) return 0;
  
  const basic = this.salary.basic || 0;
  const allowances = Object.values(this.salary.allowances || {}).reduce((sum, val) => sum + (val || 0), 0);
  const deductions = Object.values(this.salary.deductions || {}).reduce((sum, val) => sum + (val || 0), 0);
  
  return basic + allowances - deductions;
});

// طريقة للتحقق من صحة الموظف للعمل
EmployeeSchema.methods.isActiveEmployee = function() {
  return this.status === 'active' && (!this.terminationDate || this.terminationDate > new Date());
};

// طريقة لحساب الإجازات المستخدمة
EmployeeSchema.methods.getUsedLeaves = async function(year = new Date().getFullYear()) {
  const Leave = require('./Leave');
  
  const startDate = new Date(year, 0, 1);
  const endDate = new Date(year, 11, 31);
  
  const leaves = await Leave.find({
    employee: this._id,
    startDate: { $gte: startDate },
    endDate: { $lte: endDate },
    status: 'approved'
  });
  
  const usedLeaves = {
    annual: 0,
    sick: 0,
    emergency: 0,
    maternity: 0,
    paternity: 0
  };
  
  leaves.forEach(leave => {
    if (usedLeaves.hasOwnProperty(leave.type)) {
      usedLeaves[leave.type] += leave.duration;
    }
  });
  
  return usedLeaves;
};

module.exports = mongoose.model('Employee', EmployeeSchema);
