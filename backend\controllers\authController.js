const User = require('../models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');

/**
 * @route   POST api/auth/login
 * @desc    تسجيل الدخول للمستخدم وإرجاع رمز JWT
 * @access  Public
 */
exports.login = async (req, res) => {
  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { email, password } = req.body;

  try {
    // التحقق من وجود المستخدم
    let user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ msg: 'بيانات الاعتماد غير صحيحة' });
    }

    // التحقق من حالة المستخدم
    if (!user.active) {
      return res.status(400).json({ msg: 'تم تعطيل هذا الحساب، يرجى التواصل مع المسؤول' });
    }

    // التحقق من كلمة المرور
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ msg: 'بيانات الاعتماد غير صحيحة' });
    }

    // إنشاء رمز JWT
    const payload = {
      user: {
        id: user.id,
        role: user.role
      }
    };

    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '24h' },
      (err, token) => {
        if (err) throw err;
        res.json({ token });
      }
    );
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/auth
 * @desc    الحصول على بيانات المستخدم المصادق
 * @access  Private
 */
exports.getAuthUser = async (req, res) => {
  try {
    // الحصول على بيانات المستخدم باستثناء كلمة المرور
    const user = await User.findById(req.user.id).select('-password');
    if (!user) {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }

    res.json(user);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   POST api/auth/change-password
 * @desc    تغيير كلمة المرور للمستخدم المصادق
 * @access  Private
 */
exports.changePassword = async (req, res) => {
  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { currentPassword, newPassword } = req.body;

  try {
    // الحصول على بيانات المستخدم
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }

    // التحقق من كلمة المرور الحالية
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      return res.status(400).json({ msg: 'كلمة المرور الحالية غير صحيحة' });
    }

    // تشفير كلمة المرور الجديدة
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(newPassword, salt);

    // حفظ التغييرات
    await user.save();

    res.json({ msg: 'تم تغيير كلمة المرور بنجاح' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   POST api/auth/reset-password/:id
 * @desc    إعادة تعيين كلمة المرور لمستخدم (بواسطة المسؤول)
 * @access  Private/Admin
 */
exports.resetPassword = async (req, res) => {
  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { newPassword } = req.body;

  try {
    // التحقق من صلاحيات المستخدم الحالي
    const currentUser = await User.findById(req.user.id);
    if (currentUser.role !== 'admin') {
      return res.status(403).json({ msg: 'غير مصرح لك بهذه العملية' });
    }

    // الحصول على بيانات المستخدم المراد تغيير كلمة مروره
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }

    // تشفير كلمة المرور الجديدة
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(newPassword, salt);

    // حفظ التغييرات
    await user.save();

    res.json({ msg: 'تم إعادة تعيين كلمة المرور بنجاح' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};