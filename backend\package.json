{"name": "print-management-system-backend", "version": "2.0.0", "description": "الواجهة الخلفية المحدثة لنظام إدارة المطبعة", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "server": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "seed": "node scripts/seedDatabase.js"}, "keywords": ["مطبعة", "إدارة", "طباعة", "نظام", "backend", "nodejs", "express", "mongodb"], "author": "Print Management System", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "config": "^3.3.9", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"concurrently": "^8.2.2", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}