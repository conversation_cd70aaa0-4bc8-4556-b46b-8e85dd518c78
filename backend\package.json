{"name": "print-management-system-backend", "version": "1.0.0", "description": "الواجهة الخلفية لنظام إدارة المطبعة", "main": "server.js", "scripts": {"start": "node server.js", "server": "nodemon server.js", "client": "npm start --prefix client", "dev": "concurrently \"npm run server\" \"npm run client\""}, "keywords": ["مطبعة", "إدارة", "طباعة", "نظام", "backend"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "config": "^3.3.9", "cors": "^2.8.5", "express": "^4.18.2", "express-fileupload": "^1.4.0", "express-validator": "^6.14.3", "jsonwebtoken": "^9.0.0", "mongoose": "^5.13.17", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.1", "winston": "^3.17.0"}, "devDependencies": {"concurrently": "^8.2.0", "nodemon": "^2.0.20"}}