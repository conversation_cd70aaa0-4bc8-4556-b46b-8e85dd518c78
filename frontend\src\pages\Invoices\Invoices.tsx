import React from 'react';
import { Box, Typo<PERSON>, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import { Add } from '@mui/icons-material';

const Invoices: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          إدارة الفواتير
        </Typography>
        <Button variant="contained" startIcon={<Add />}>
          فاتورة جديدة
        </Button>
      </Box>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            قائمة الفواتير
          </Typography>
          <Typography color="text.secondary">
            سيتم تطوير هذه الصفحة قريباً مع جميع وظائف إدارة الفواتير.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Invoices;
