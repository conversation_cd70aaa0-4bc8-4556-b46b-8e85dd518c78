/**
 * ملف إعداد وتكوين التحميل
 * يقوم بإعداد وتكوين إعدادات تحميل الملفات للتطبيق
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const config = require('config');
const logger = require('../utils/logger');
const { BadRequestError } = require('../utils/errors');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

/**
 * إنشاء المجلدات اللازمة للتحميل إذا لم تكن موجودة
 */
const setupUploadDirectories = () => {
  const uploadDir = path.join(process.cwd(), appConfig.uploadDir || 'uploads');
  const tempDir = path.join(process.cwd(), appConfig.tempDir || 'uploads/temp');
  
  // إنشاء مجلد التحميل الرئيسي
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
    logger.info(`تم إنشاء مجلد التحميل: ${uploadDir}`);
  }
  
  // إنشاء مجلد الملفات المؤقتة
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
    logger.info(`تم إنشاء مجلد الملفات المؤقتة: ${tempDir}`);
  }
  
  // إنشاء المجلدات الفرعية للتحميل
  const subDirs = ['images', 'documents', 'reports', 'invoices', 'products', 'avatars'];
  subDirs.forEach(dir => {
    const subDir = path.join(uploadDir, dir);
    if (!fs.existsSync(subDir)) {
      fs.mkdirSync(subDir, { recursive: true });
      logger.info(`تم إنشاء مجلد فرعي للتحميل: ${subDir}`);
    }
  });
  
  return { uploadDir, tempDir };
};

/**
 * إنشاء اسم ملف فريد
 * @param {string} originalname - الاسم الأصلي للملف
 * @returns {string} اسم الملف الفريد
 */
const generateUniqueFilename = (originalname) => {
  const timestamp = Date.now();
  const randomString = crypto.randomBytes(8).toString('hex');
  const extension = path.extname(originalname);
  const sanitizedName = path.basename(originalname, extension)
    .replace(/[^a-zA-Z0-9]/g, '-')
    .toLowerCase();
  
  return `${sanitizedName}-${timestamp}-${randomString}${extension}`;
};

/**
 * تكوين تخزين الملفات المؤقتة
 */
const tempStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const { tempDir } = setupUploadDirectories();
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    const uniqueFilename = generateUniqueFilename(file.originalname);
    cb(null, uniqueFilename);
  }
});

/**
 * تكوين تخزين الملفات النهائية
 * @param {string} subDir - المجلد الفرعي للتحميل
 * @returns {Object} تكوين التخزين
 */
const createStorage = (subDir) => {
  return multer.diskStorage({
    destination: (req, file, cb) => {
      const { uploadDir } = setupUploadDirectories();
      const targetDir = path.join(uploadDir, subDir);
      cb(null, targetDir);
    },
    filename: (req, file, cb) => {
      const uniqueFilename = generateUniqueFilename(file.originalname);
      cb(null, uniqueFilename);
    }
  });
};

/**
 * فلتر الملفات للتحقق من نوع الملف
 * @param {Array} allowedMimeTypes - أنواع الملفات المسموح بها
 * @returns {Function} دالة الفلتر
 */
const fileFilter = (allowedMimeTypes) => {
  return (req, file, cb) => {
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new BadRequestError(`نوع الملف غير مسموح به. الأنواع المسموح بها: ${allowedMimeTypes.join(', ')}`), false);
    }
  };
};

/**
 * الحصول على حد حجم الملف من التكوين
 * @returns {number} حد حجم الملف بالبايت
 */
const getFileSizeLimit = () => {
  const maxUploadSize = appConfig.maxUploadSize || '10MB';
  const sizeUnit = maxUploadSize.slice(-2);
  const sizeValue = parseInt(maxUploadSize.slice(0, -2), 10);
  
  switch (sizeUnit) {
    case 'KB':
      return sizeValue * 1024;
    case 'MB':
      return sizeValue * 1024 * 1024;
    case 'GB':
      return sizeValue * 1024 * 1024 * 1024;
    default:
      return 10 * 1024 * 1024; // 10MB افتراضي
  }
};

/**
 * إنشاء تكوين multer للتحميل المؤقت
 */
const tempUpload = multer({
  storage: tempStorage,
  limits: {
    fileSize: getFileSizeLimit()
  }
});

/**
 * إنشاء تكوين multer لتحميل الصور
 */
const imageUpload = multer({
  storage: createStorage('images'),
  limits: {
    fileSize: getFileSizeLimit()
  },
  fileFilter: fileFilter([
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ])
});

/**
 * إنشاء تكوين multer لتحميل المستندات
 */
const documentUpload = multer({
  storage: createStorage('documents'),
  limits: {
    fileSize: getFileSizeLimit()
  },
  fileFilter: fileFilter([
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv'
  ])
});

/**
 * إنشاء تكوين multer لتحميل الصور الشخصية
 */
const avatarUpload = multer({
  storage: createStorage('avatars'),
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB حد أقصى للصور الشخصية
  },
  fileFilter: fileFilter([
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp'
  ])
});

/**
 * إنشاء تكوين multer لتحميل صور المنتجات
 */
const productImageUpload = multer({
  storage: createStorage('products'),
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB حد أقصى لصور المنتجات
  },
  fileFilter: fileFilter([
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ])
});

/**
 * نقل ملف من المجلد المؤقت إلى المجلد النهائي
 * @param {string} tempFilePath - مسار الملف المؤقت
 * @param {string} targetDir - المجلد الهدف
 * @param {string} [newFilename] - اسم الملف الجديد (اختياري)
 * @returns {Promise<string>} مسار الملف النهائي
 */
const moveFile = async (tempFilePath, targetDir, newFilename) => {
  try {
    // التأكد من وجود المجلد الهدف
    const { uploadDir } = setupUploadDirectories();
    const targetPath = path.join(uploadDir, targetDir);
    
    if (!fs.existsSync(targetPath)) {
      fs.mkdirSync(targetPath, { recursive: true });
    }
    
    // إنشاء اسم الملف النهائي
    const filename = newFilename || path.basename(tempFilePath);
    const finalPath = path.join(targetPath, filename);
    
    // نقل الملف
    await fs.promises.copyFile(tempFilePath, finalPath);
    await fs.promises.unlink(tempFilePath);
    
    logger.info(`تم نقل الملف من ${tempFilePath} إلى ${finalPath}`);
    
    return finalPath;
  } catch (error) {
    logger.error(`فشل نقل الملف: ${error.message}`);
    throw error;
  }
};

/**
 * حذف ملف
 * @param {string} filePath - مسار الملف
 * @returns {Promise<boolean>} نجاح العملية
 */
const deleteFile = async (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      await fs.promises.unlink(filePath);
      logger.info(`تم حذف الملف: ${filePath}`);
      return true;
    }
    return false;
  } catch (error) {
    logger.error(`فشل حذف الملف: ${error.message}`);
    return false;
  }
};

/**
 * تنظيف المجلد المؤقت من الملفات القديمة
 * @param {number} maxAgeMs - العمر الأقصى للملفات بالمللي ثانية
 * @returns {Promise<number>} عدد الملفات التي تم حذفها
 */
const cleanupTempFiles = async (maxAgeMs = 24 * 60 * 60 * 1000) => {
  try {
    const { tempDir } = setupUploadDirectories();
    const now = Date.now();
    let deletedCount = 0;
    
    const files = await fs.promises.readdir(tempDir);
    
    for (const file of files) {
      const filePath = path.join(tempDir, file);
      const stats = await fs.promises.stat(filePath);
      
      if (now - stats.mtimeMs > maxAgeMs) {
        await fs.promises.unlink(filePath);
        deletedCount++;
      }
    }
    
    if (deletedCount > 0) {
      logger.info(`تم تنظيف ${deletedCount} ملفات مؤقتة قديمة`);
    }
    
    return deletedCount;
  } catch (error) {
    logger.error(`فشل تنظيف الملفات المؤقتة: ${error.message}`);
    return 0;
  }
};

/**
 * الحصول على المسار النسبي للملف
 * @param {string} absolutePath - المسار المطلق للملف
 * @returns {string} المسار النسبي للملف
 */
const getRelativePath = (absolutePath) => {
  const { uploadDir } = setupUploadDirectories();
  return absolutePath.replace(uploadDir, '').replace(/\\/g, '/').replace(/^\//, '');
};

/**
 * الحصول على URL للملف
 * @param {string} relativePath - المسار النسبي للملف
 * @returns {string} URL الملف
 */
const getFileUrl = (relativePath) => {
  const baseUrl = appConfig.baseUrl || `http://localhost:${appConfig.port || 3000}`;
  return `${baseUrl}/uploads/${relativePath}`;
};

// إنشاء المجلدات اللازمة عند تحميل الوحدة
setupUploadDirectories();

// جدولة تنظيف الملفات المؤقتة كل 24 ساعة
setInterval(() => {
  cleanupTempFiles().catch(err => {
    logger.error(`فشل جدولة تنظيف الملفات المؤقتة: ${err.message}`);
  });
}, 24 * 60 * 60 * 1000);

module.exports = {
  tempUpload,
  imageUpload,
  documentUpload,
  avatarUpload,
  productImageUpload,
  moveFile,
  deleteFile,
  cleanupTempFiles,
  getRelativePath,
  getFileUrl,
  setupUploadDirectories,
  generateUniqueFilename
};