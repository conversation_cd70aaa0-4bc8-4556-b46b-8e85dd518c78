const mongoose = require('mongoose');

/**
 * نموذج الحضور والانصراف
 */
const AttendanceSchema = new mongoose.Schema({
  employee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employee',
    required: [true, 'الموظف مطلوب']
  },
  
  date: {
    type: Date,
    required: [true, 'التاريخ مطلوب'],
    default: () => {
      const today = new Date();
      return new Date(today.getFullYear(), today.getMonth(), today.getDate());
    }
  },
  
  checkIn: {
    time: { type: Date },
    location: {
      latitude: { type: Number },
      longitude: { type: Number },
      address: { type: String, trim: true }
    },
    method: {
      type: String,
      enum: ['manual', 'biometric', 'card', 'mobile', 'web'],
      default: 'manual'
    },
    notes: { type: String, trim: true }
  },
  
  checkOut: {
    time: { type: Date },
    location: {
      latitude: { type: Number },
      longitude: { type: Number },
      address: { type: String, trim: true }
    },
    method: {
      type: String,
      enum: ['manual', 'biometric', 'card', 'mobile', 'web'],
      default: 'manual'
    },
    notes: { type: String, trim: true }
  },
  
  // فترات الراحة
  breaks: [{
    startTime: { type: Date },
    endTime: { type: Date },
    type: {
      type: String,
      enum: ['lunch', 'prayer', 'coffee', 'other'],
      default: 'other'
    },
    notes: { type: String, trim: true }
  }],
  
  // ساعات العمل
  workingHours: {
    scheduled: { type: Number, default: 8 }, // الساعات المجدولة
    actual: { type: Number, default: 0 }, // الساعات الفعلية
    overtime: { type: Number, default: 0 }, // ساعات إضافية
    break: { type: Number, default: 0 } // وقت الراحة
  },
  
  // الحالة
  status: {
    type: String,
    enum: ['present', 'absent', 'late', 'early_leave', 'half_day', 'on_leave'],
    default: 'present'
  },
  
  // التأخير والانصراف المبكر
  lateArrival: {
    minutes: { type: Number, default: 0 },
    reason: { type: String, trim: true },
    approved: { type: Boolean, default: false }
  },
  
  earlyDeparture: {
    minutes: { type: Number, default: 0 },
    reason: { type: String, trim: true },
    approved: { type: Boolean, default: false }
  },
  
  // معلومات الإجازة (إذا كان في إجازة)
  leaveInfo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Leave'
  },
  
  // ملاحظات إضافية
  notes: {
    type: String,
    trim: true,
    maxlength: [500, 'الملاحظات لا يجب أن تتجاوز 500 حرف']
  },
  
  // معلومات الموافقة
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  approvedAt: {
    type: Date
  },
  
  // هل تم التعديل يدوياً
  manualEntry: {
    type: Boolean,
    default: false
  },
  
  // معلومات التعديل
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  modifiedAt: {
    type: Date
  },
  
  modificationReason: {
    type: String,
    trim: true
  }
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// فهرسة الموظف والتاريخ (فريدة)
AttendanceSchema.index({ employee: 1, date: 1 }, { unique: true });

// فهرسة التاريخ
AttendanceSchema.index({ date: -1 });

// فهرسة الحالة
AttendanceSchema.index({ status: 1 });

// فهرسة مركبة للاستعلامات
AttendanceSchema.index({ employee: 1, date: -1 });
AttendanceSchema.index({ status: 1, date: -1 });

// حساب الساعات تلقائياً قبل الحفظ
AttendanceSchema.pre('save', function(next) {
  // حساب الساعات الفعلية
  if (this.checkIn.time && this.checkOut.time) {
    const workTime = (this.checkOut.time - this.checkIn.time) / (1000 * 60 * 60);
    
    // حساب وقت الراحة
    let totalBreakTime = 0;
    this.breaks.forEach(breakPeriod => {
      if (breakPeriod.startTime && breakPeriod.endTime) {
        totalBreakTime += (breakPeriod.endTime - breakPeriod.startTime) / (1000 * 60 * 60);
      }
    });
    
    this.workingHours.break = totalBreakTime;
    this.workingHours.actual = Math.max(0, workTime - totalBreakTime);
    
    // حساب الساعات الإضافية
    if (this.workingHours.actual > this.workingHours.scheduled) {
      this.workingHours.overtime = this.workingHours.actual - this.workingHours.scheduled;
    }
  }
  
  // تحديد الحالة تلقائياً
  this.updateStatus();
  
  next();
});

// طريقة لتحديث الحالة
AttendanceSchema.methods.updateStatus = function() {
  if (this.leaveInfo) {
    this.status = 'on_leave';
    return;
  }
  
  if (!this.checkIn.time) {
    this.status = 'absent';
    return;
  }
  
  // التحقق من التأخير (أكثر من 15 دقيقة)
  const scheduledStart = new Date(this.date);
  scheduledStart.setHours(8, 0, 0, 0); // افتراض بداية العمل 8:00 صباحاً
  
  const lateMinutes = Math.max(0, (this.checkIn.time - scheduledStart) / (1000 * 60));
  this.lateArrival.minutes = lateMinutes;
  
  if (lateMinutes > 15) {
    this.status = 'late';
  }
  
  // التحقق من الانصراف المبكر
  if (this.checkOut.time) {
    const scheduledEnd = new Date(this.date);
    scheduledEnd.setHours(17, 0, 0, 0); // افتراض نهاية العمل 5:00 مساءً
    
    const earlyMinutes = Math.max(0, (scheduledEnd - this.checkOut.time) / (1000 * 60));
    this.earlyDeparture.minutes = earlyMinutes;
    
    if (earlyMinutes > 15) {
      this.status = 'early_leave';
    }
  }
  
  // التحقق من نصف يوم
  if (this.workingHours.actual < 4) {
    this.status = 'half_day';
  }
  
  // إذا لم تكن هناك مشاكل، الحالة حاضر
  if (this.status !== 'late' && this.status !== 'early_leave' && this.status !== 'half_day') {
    this.status = 'present';
  }
};

// طريقة لتسجيل الحضور
AttendanceSchema.methods.checkInEmployee = function(location = {}, method = 'manual', notes = '') {
  this.checkIn = {
    time: new Date(),
    location,
    method,
    notes
  };
  
  return this.save();
};

// طريقة لتسجيل الانصراف
AttendanceSchema.methods.checkOutEmployee = function(location = {}, method = 'manual', notes = '') {
  this.checkOut = {
    time: new Date(),
    location,
    method,
    notes
  };
  
  return this.save();
};

// طريقة لبدء فترة راحة
AttendanceSchema.methods.startBreak = function(type = 'other', notes = '') {
  this.breaks.push({
    startTime: new Date(),
    type,
    notes
  });
  
  return this.save();
};

// طريقة لإنهاء فترة راحة
AttendanceSchema.methods.endBreak = function() {
  const lastBreak = this.breaks[this.breaks.length - 1];
  if (lastBreak && !lastBreak.endTime) {
    lastBreak.endTime = new Date();
  }
  
  return this.save();
};

// خاصية افتراضية للتحقق من وجود الموظف حالياً
AttendanceSchema.virtual('isCurrentlyPresent').get(function() {
  return this.checkIn.time && !this.checkOut.time;
});

// خاصية افتراضية للتحقق من وجود فترة راحة نشطة
AttendanceSchema.virtual('isOnBreak').get(function() {
  const lastBreak = this.breaks[this.breaks.length - 1];
  return lastBreak && lastBreak.startTime && !lastBreak.endTime;
});

// طريقة للحصول على إحصائيات الحضور لموظف
AttendanceSchema.statics.getEmployeeStats = async function(employeeId, startDate, endDate) {
  const stats = await this.aggregate([
    {
      $match: {
        employee: mongoose.Types.ObjectId(employeeId),
        date: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: null,
        totalDays: { $sum: 1 },
        presentDays: {
          $sum: {
            $cond: [{ $eq: ['$status', 'present'] }, 1, 0]
          }
        },
        lateDays: {
          $sum: {
            $cond: [{ $eq: ['$status', 'late'] }, 1, 0]
          }
        },
        absentDays: {
          $sum: {
            $cond: [{ $eq: ['$status', 'absent'] }, 1, 0]
          }
        },
        totalWorkingHours: { $sum: '$workingHours.actual' },
        totalOvertimeHours: { $sum: '$workingHours.overtime' },
        averageLateMinutes: { $avg: '$lateArrival.minutes' }
      }
    }
  ]);
  
  return stats.length > 0 ? stats[0] : null;
};

// طريقة للحصول على تقرير الحضور اليومي
AttendanceSchema.statics.getDailyReport = async function(date) {
  const startDate = new Date(date);
  startDate.setHours(0, 0, 0, 0);
  
  const endDate = new Date(date);
  endDate.setHours(23, 59, 59, 999);
  
  return await this.find({
    date: { $gte: startDate, $lte: endDate }
  }).populate('employee', 'firstName lastName arabicName department');
};

module.exports = mongoose.model('Attendance', AttendanceSchema);
