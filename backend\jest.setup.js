// إعداد بيئة اختبار Jest

// تعيين وقت انتهاء مهلة الاختبار
jest.setTimeout(30000);

// تجاهل تحذيرات وحدة mongoose
const mongoose = require('mongoose');
mongoose.set('strictQuery', true);

// إنشاء وظائف مساعدة للاختبارات
global.setupTestDB = () => {
  // إعداد قاعدة بيانات الاختبار قبل كل اختبار
  beforeAll(async () => {
    // استخدام قاعدة بيانات اختبار منفصلة
    const mongoURI = process.env.MONGO_URI_TEST || 'mongodb://localhost:27017/print-system-test';
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
  });

  // مسح البيانات بعد كل اختبار
  afterEach(async () => {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
  });

  // إغلاق الاتصال بقاعدة البيانات بعد الانتهاء من جميع الاختبارات
  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });
};

// إنشاء وظيفة مساعدة لإنشاء رمز JWT للاختبارات
global.generateTestToken = async (userId, role = 'user') => {
  const jwt = require('jsonwebtoken');
  const config = require('config');
  
  return jwt.sign(
    { id: userId, role },
    config.get('jwtSecret'),
    { expiresIn: '1h' }
  );
};