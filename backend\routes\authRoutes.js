const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authController = require('../controllers/authController');
const auth = require('../middleware/auth');
const checkPermission = require('../middleware/checkPermission');

/**
 * @route   POST api/auth/login
 * @desc    تسجيل الدخول للمستخدم وإرجاع رمز JWT
 * @access  Public
 */
router.post(
  '/login',
  [
    check('email', 'يرجى إدخال بريد إلكتروني صحيح').isEmail(),
    check('password', 'كلمة المرور مطلوبة').exists()
  ],
  authController.login
);

/**
 * @route   GET api/auth
 * @desc    الحصول على بيانات المستخدم المصادق
 * @access  Private
 */
router.get('/', auth, authController.getAuthUser);

/**
 * @route   POST api/auth/change-password
 * @desc    تغيير كلمة المرور للمستخدم المصادق
 * @access  Private
 */
router.post(
  '/change-password',
  [
    auth,
    [
      check('currentPassword', 'كلمة المرور الحالية مطلوبة').exists(),
      check('newPassword', 'يرجى إدخال كلمة مرور جديدة لا تقل عن 6 أحرف').isLength({ min: 6 })
    ]
  ],
  authController.changePassword
);

/**
 * @route   POST api/auth/reset-password/:id
 * @desc    إعادة تعيين كلمة المرور لمستخدم (بواسطة المسؤول)
 * @access  Private/Admin
 */
router.post(
  '/reset-password/:id',
  [
    auth,
    checkPermission('users', 'update'),
    [
      check('newPassword', 'يرجى إدخال كلمة مرور جديدة لا تقل عن 6 أحرف').isLength({ min: 6 })
    ]
  ],
  authController.resetPassword
);

module.exports = router;