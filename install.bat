@echo off
echo ========================================
echo    نظام المطبعة المتطور - الإصدار 2.0
echo         تثبيت التبعيات
echo ========================================
echo.

echo جاري تثبيت التبعيات...
echo.

echo 1. تثبيت تبعيات المشروع الرئيسي...
npm install
echo.

echo 2. تثبيت تبعيات الواجهة الخلفية...
cd backend
npm install
cd ..
echo.

echo 3. تثبيت تبعيات الواجهة الأمامية...
cd frontend
npm install
cd ..
echo.

echo 4. نسخ ملفات الإعدادات...
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env"
    echo تم إنشاء ملف backend\.env
)

if not exist "frontend\.env" (
    copy "frontend\.env.example" "frontend\.env"
    echo تم إنشاء ملف frontend\.env
)

echo.
echo ========================================
echo تم تثبيت جميع التبعيات بنجاح!
echo ========================================
echo.
echo الخطوات التالية:
echo 1. تأكد من تشغيل MongoDB على المنفذ 27017
echo 2. قم بتحديث ملفات .env حسب إعداداتك
echo 3. شغل النظام باستخدام start.bat
echo.
echo اضغط أي مفتاح للخروج...
pause > nul
