import React from 'react';
import { Box, Typography, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import { Add } from '@mui/icons-material';

const Inventory: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          إدارة المخزون
        </Typography>
        <Button variant="contained" startIcon={<Add />}>
          إضافة عنصر جديد
        </Button>
      </Box>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            قائمة المخزون
          </Typography>
          <Typography color="text.secondary">
            سيتم تطوير هذه الصفحة قريباً مع جميع وظائف إدارة المخزون.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Inventory;
