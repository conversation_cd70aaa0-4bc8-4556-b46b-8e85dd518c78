/**
 * ملف إعداد وتكوين التقارير
 * يقوم بإعداد وتكوين إعدادات التقارير للتطبيق
 */

const PDFDocument = require('pdfkit-table');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');
const config = require('config');
const logger = require('../utils/logger');

// الحصول على إعدادات التقارير من ملف التكوين
const reportsConfig = config.get('reports');
const appConfig = config.get('app');

/**
 * إنشاء مجلد التقارير إذا لم يكن موجودًا
 * @returns {string} مسار مجلد التقارير
 */
const setupReportsDirectory = () => {
  const reportsDir = path.join(process.cwd(), 'uploads', 'reports');
  
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
    logger.info('تم إنشاء مجلد التقارير');
  }
  
  return reportsDir;
};

/**
 * إنشاء اسم ملف فريد للتقرير
 * @param {string} prefix - بادئة اسم الملف
 * @param {string} extension - امتداد الملف
 * @returns {string} اسم الملف الفريد
 */
const generateReportFilename = (prefix, extension) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  return `${prefix}-${timestamp}.${extension}`;
};

/**
 * إنشاء تقرير PDF
 * @param {Object} options - خيارات التقرير
 * @param {string} options.title - عنوان التقرير
 * @param {string} options.filename - اسم ملف التقرير (اختياري)
 * @param {Array} options.headers - رؤوس جدول التقرير
 * @param {Array} options.data - بيانات التقرير
 * @param {Object} options.metadata - بيانات وصفية للتقرير (اختياري)
 * @param {Object} options.styles - أنماط التقرير (اختياري)
 * @returns {Promise<string>} مسار ملف التقرير
 */
const generatePDFReport = async (options) => {
  return new Promise((resolve, reject) => {
    try {
      // إنشاء مجلد التقارير
      const reportsDir = setupReportsDirectory();
      
      // إنشاء اسم ملف فريد إذا لم يتم توفيره
      const filename = options.filename || generateReportFilename(options.title.replace(/\s+/g, '-').toLowerCase(), 'pdf');
      const filePath = path.join(reportsDir, filename);
      
      // إنشاء مستند PDF
      const doc = new PDFDocument({
        size: 'A4',
        margin: 50,
        info: {
          Title: options.title,
          Author: appConfig.name,
          Subject: options.metadata?.subject || options.title,
          Keywords: options.metadata?.keywords || '',
          CreationDate: new Date(),
        },
        bufferPages: true,
      });
      
      // إنشاء تدفق الكتابة
      const stream = fs.createWriteStream(filePath);
      doc.pipe(stream);
      
      // إضافة الرأس والتذييل
      doc.on('pageAdded', () => {
        // رأس الصفحة
        doc.fontSize(8)
           .text(
             `${appConfig.name} - ${options.title}`,
             50,
             15,
             { align: 'center' }
           );
        
        // تذييل الصفحة
        const pageNumber = doc.bufferedPageRange().count;
        doc.fontSize(8)
           .text(
             `الصفحة ${pageNumber} - تم إنشاؤه في ${new Date().toLocaleString('ar-SA')}`,
             50,
             doc.page.height - 15,
             { align: 'center' }
           );
      });
      
      // إضافة الشعار إذا كان موجودًا
      const logoPath = path.join(process.cwd(), 'public', 'assets', 'images', 'logo.png');
      if (fs.existsSync(logoPath)) {
        doc.image(logoPath, 50, 50, { width: 100 })
           .moveDown();
      }
      
      // إضافة عنوان التقرير
      doc.fontSize(20)
         .font('Helvetica-Bold')
         .text(options.title, { align: 'center' })
         .moveDown();
      
      // إضافة التاريخ والوقت
      doc.fontSize(12)
         .font('Helvetica')
         .text(`تاريخ التقرير: ${new Date().toLocaleString('ar-SA')}`, { align: 'right' })
         .moveDown();
      
      // إضافة البيانات الوصفية إذا كانت موجودة
      if (options.metadata) {
        doc.fontSize(12)
           .font('Helvetica')
           .text('معلومات التقرير:', { align: 'right' })
           .moveDown(0.5);
        
        Object.entries(options.metadata).forEach(([key, value]) => {
          if (key !== 'subject' && key !== 'keywords') {
            doc.fontSize(10)
               .text(`${key}: ${value}`, { align: 'right' })
               .moveDown(0.2);
          }
        });
        
        doc.moveDown();
      }
      
      // إعداد بيانات الجدول
      const tableData = {
        title: options.subtitle || '',
        headers: options.headers.map(header => ({
          label: header.label,
          property: header.key,
          width: header.width || 100,
          align: header.align || 'right',
          renderer: header.renderer,
        })),
        rows: options.data.map(row => {
          const rowData = {};
          options.headers.forEach(header => {
            rowData[header.key] = row[header.key] !== undefined ? row[header.key] : '';
          });
          return rowData;
        }),
      };
      
      // إضافة الجدول
      doc.table(tableData, {
        prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10),
        prepareRow: () => doc.font('Helvetica').fontSize(10),
        ...options.styles,
      });
      
      // إضافة ملاحظات إذا كانت موجودة
      if (options.notes) {
        doc.moveDown()
           .fontSize(10)
           .font('Helvetica-Oblique')
           .text('ملاحظات:', { align: 'right' })
           .moveDown(0.5)
           .font('Helvetica')
           .text(options.notes, { align: 'right' });
      }
      
      // إنهاء المستند
      doc.end();
      
      // الاستماع لحدث إنهاء الكتابة
      stream.on('finish', () => {
        logger.info(`تم إنشاء تقرير PDF: ${filePath}`);
        resolve(filePath);
      });
      
      stream.on('error', (error) => {
        logger.error(`فشل إنشاء تقرير PDF: ${error.message}`);
        reject(error);
      });
    } catch (error) {
      logger.error(`فشل إنشاء تقرير PDF: ${error.message}`);
      reject(error);
    }
  });
};

/**
 * إنشاء تقرير Excel
 * @param {Object} options - خيارات التقرير
 * @param {string} options.title - عنوان التقرير
 * @param {string} options.filename - اسم ملف التقرير (اختياري)
 * @param {Array} options.headers - رؤوس جدول التقرير
 * @param {Array} options.data - بيانات التقرير
 * @param {Object} options.metadata - بيانات وصفية للتقرير (اختياري)
 * @param {Object} options.styles - أنماط التقرير (اختياري)
 * @returns {Promise<string>} مسار ملف التقرير
 */
const generateExcelReport = async (options) => {
  try {
    // إنشاء مجلد التقارير
    const reportsDir = setupReportsDirectory();
    
    // إنشاء اسم ملف فريد إذا لم يتم توفيره
    const filename = options.filename || generateReportFilename(options.title.replace(/\s+/g, '-').toLowerCase(), 'xlsx');
    const filePath = path.join(reportsDir, filename);
    
    // إنشاء مصنف Excel جديد
    const workbook = new ExcelJS.Workbook();
    workbook.creator = appConfig.name;
    workbook.lastModifiedBy = appConfig.name;
    workbook.created = new Date();
    workbook.modified = new Date();
    
    // إضافة البيانات الوصفية
    if (options.metadata) {
      Object.entries(options.metadata).forEach(([key, value]) => {
        workbook.properties.company = appConfig.name;
        if (key === 'subject') {
          workbook.properties.subject = value;
        } else if (key === 'keywords') {
          workbook.properties.keywords = value;
        } else if (key === 'category') {
          workbook.properties.category = value;
        } else if (key === 'description') {
          workbook.properties.description = value;
        }
      });
    }
    
    // إنشاء ورقة عمل جديدة
    const worksheet = workbook.addWorksheet(options.title, {
      properties: {
        tabColor: { argb: '4F81BD' },
        defaultRowHeight: 20,
      },
      pageSetup: {
        paperSize: 9, // A4
        orientation: 'portrait',
        fitToPage: true,
        fitToWidth: 1,
        fitToHeight: 0,
        horizontalCentered: true,
        verticalCentered: false,
      },
    });
    
    // إضافة الرأس
    worksheet.mergeCells('A1:H1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = options.title;
    titleCell.font = {
      name: 'Arial',
      size: 16,
      bold: true,
      color: { argb: '000000' },
    };
    titleCell.alignment = {
      horizontal: 'center',
      vertical: 'middle',
    };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E0E0E0' },
    };
    
    // إضافة التاريخ
    worksheet.mergeCells('A2:H2');
    const dateCell = worksheet.getCell('A2');
    dateCell.value = `تاريخ التقرير: ${new Date().toLocaleString('ar-SA')}`;
    dateCell.font = {
      name: 'Arial',
      size: 12,
      bold: false,
    };
    dateCell.alignment = {
      horizontal: 'right',
      vertical: 'middle',
    };
    
    // إضافة البيانات الوصفية إذا كانت موجودة
    let rowIndex = 3;
    if (options.metadata) {
      worksheet.mergeCells(`A${rowIndex}:H${rowIndex}`);
      const metadataHeaderCell = worksheet.getCell(`A${rowIndex}`);
      metadataHeaderCell.value = 'معلومات التقرير:';
      metadataHeaderCell.font = {
        name: 'Arial',
        size: 12,
        bold: true,
      };
      metadataHeaderCell.alignment = {
        horizontal: 'right',
        vertical: 'middle',
      };
      rowIndex++;
      
      Object.entries(options.metadata).forEach(([key, value]) => {
        if (key !== 'subject' && key !== 'keywords' && key !== 'category' && key !== 'description') {
          worksheet.mergeCells(`A${rowIndex}:H${rowIndex}`);
          const metadataCell = worksheet.getCell(`A${rowIndex}`);
          metadataCell.value = `${key}: ${value}`;
          metadataCell.font = {
            name: 'Arial',
            size: 10,
            bold: false,
          };
          metadataCell.alignment = {
            horizontal: 'right',
            vertical: 'middle',
          };
          rowIndex++;
        }
      });
      
      rowIndex++; // إضافة سطر فارغ
    }
    
    // إضافة رؤوس الجدول
    const headerRow = worksheet.addRow(options.headers.map(header => header.label));
    headerRow.font = {
      name: 'Arial',
      size: 12,
      bold: true,
      color: { argb: 'FFFFFF' },
    };
    headerRow.alignment = {
      horizontal: 'center',
      vertical: 'middle',
    };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4F81BD' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
    
    // إضافة بيانات الجدول
    options.data.forEach(row => {
      const dataRow = worksheet.addRow(options.headers.map(header => row[header.key] !== undefined ? row[header.key] : ''));
      dataRow.font = {
        name: 'Arial',
        size: 11,
      };
      dataRow.alignment = {
        vertical: 'middle',
      };
      dataRow.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
    });
    
    // تعيين عرض الأعمدة
    options.headers.forEach((header, index) => {
      const column = worksheet.getColumn(index + 1);
      column.width = header.width ? header.width / 7 : 15; // تحويل تقريبي من وحدات PDF إلى وحدات Excel
      column.alignment = {
        horizontal: header.align || 'right',
        vertical: 'middle',
      };
    });
    
    // إضافة ملاحظات إذا كانت موجودة
    if (options.notes) {
      const notesRowIndex = worksheet.rowCount + 2;
      worksheet.mergeCells(`A${notesRowIndex}:H${notesRowIndex}`);
      const notesHeaderCell = worksheet.getCell(`A${notesRowIndex}`);
      notesHeaderCell.value = 'ملاحظات:';
      notesHeaderCell.font = {
        name: 'Arial',
        size: 12,
        bold: true,
        italic: true,
      };
      notesHeaderCell.alignment = {
        horizontal: 'right',
        vertical: 'middle',
      };
      
      worksheet.mergeCells(`A${notesRowIndex + 1}:H${notesRowIndex + 1}`);
      const notesCell = worksheet.getCell(`A${notesRowIndex + 1}`);
      notesCell.value = options.notes;
      notesCell.font = {
        name: 'Arial',
        size: 11,
      };
      notesCell.alignment = {
        horizontal: 'right',
        vertical: 'middle',
        wrapText: true,
      };
    }
    
    // حفظ المصنف
    await workbook.xlsx.writeFile(filePath);
    
    logger.info(`تم إنشاء تقرير Excel: ${filePath}`);
    return filePath;
  } catch (error) {
    logger.error(`فشل إنشاء تقرير Excel: ${error.message}`);
    throw error;
  }
};

/**
 * إنشاء تقرير CSV
 * @param {Object} options - خيارات التقرير
 * @param {string} options.title - عنوان التقرير
 * @param {string} options.filename - اسم ملف التقرير (اختياري)
 * @param {Array} options.headers - رؤوس جدول التقرير
 * @param {Array} options.data - بيانات التقرير
 * @returns {Promise<string>} مسار ملف التقرير
 */
const generateCSVReport = async (options) => {
  try {
    // إنشاء مجلد التقارير
    const reportsDir = setupReportsDirectory();
    
    // إنشاء اسم ملف فريد إذا لم يتم توفيره
    const filename = options.filename || generateReportFilename(options.title.replace(/\s+/g, '-').toLowerCase(), 'csv');
    const filePath = path.join(reportsDir, filename);
    
    // إنشاء محتوى CSV
    const headers = options.headers.map(header => header.label).join(',');
    const rows = options.data.map(row => {
      return options.headers.map(header => {
        const value = row[header.key] !== undefined ? row[header.key] : '';
        // تغليف القيم التي تحتوي على فواصل أو أقواس مزدوجة
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',');
    }).join('\n');
    
    // كتابة محتوى CSV إلى الملف
    fs.writeFileSync(filePath, `${headers}\n${rows}`);
    
    logger.info(`تم إنشاء تقرير CSV: ${filePath}`);
    return filePath;
  } catch (error) {
    logger.error(`فشل إنشاء تقرير CSV: ${error.message}`);
    throw error;
  }
};

/**
 * إنشاء تقرير بالتنسيق المطلوب
 * @param {Object} options - خيارات التقرير
 * @param {string} options.format - تنسيق التقرير (pdf, excel, csv)
 * @returns {Promise<string>} مسار ملف التقرير
 */
const generateReport = async (options) => {
  const format = options.format || reportsConfig.defaultFormat;
  
  switch (format.toLowerCase()) {
    case 'pdf':
      return generatePDFReport(options);
    case 'excel':
      return generateExcelReport(options);
    case 'csv':
      return generateCSVReport(options);
    default:
      throw new Error(`تنسيق التقرير غير مدعوم: ${format}`);
  }
};

// إنشاء مجلد التقارير عند تحميل الوحدة
setupReportsDirectory();

module.exports = {
  generateReport,
  generatePDFReport,
  generateExcelReport,
  generateCSVReport,
  setupReportsDirectory,
  generateReportFilename,
};