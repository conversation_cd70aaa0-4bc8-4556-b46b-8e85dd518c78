const swaggerUi = require('swagger-ui-express');
const swaggerDocument = require('../swagger.json');

/**
 * تكوين Swagger لتوثيق واجهة برمجة التطبيقات
 * @param {Object} app - تطبيق Express
 */
const setupSwagger = (app) => {
  // خيارات واجهة Swagger UI
  const options = {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'نظام مطبعة متطور API',
    customfavIcon: '/favicon.ico',
    swaggerOptions: {
      docExpansion: 'none', // none | list | full
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
      filter: true
    }
  };

  // إضافة مسارات Swagger
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, options));
  
  // إضافة مسار لتنزيل ملف JSON للتوثيق
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerDocument);
  });

  console.info('تم تكوين Swagger على المسار: /api-docs');
};

module.exports = setupSwagger;