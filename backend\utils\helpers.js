/**
 * مجموعة من الوظائف المساعدة للاستخدام في جميع أنحاء التطبيق
 */

/**
 * تنسيق التاريخ إلى تنسيق محلي
 * @param {Date|string} date - كائن تاريخ أو سلسلة تاريخ
 * @param {string} locale - الإعدادات المحلية (الافتراضي 'ar-SA')
 * @returns {string} التاريخ المنسق
 */
const formatDate = (date, locale = 'ar-SA') => {
  if (!date) return '';
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * تنسيق التاريخ والوقت إلى تنسيق محلي
 * @param {Date|string} date - كائن تاريخ أو سلسلة تاريخ
 * @param {string} locale - الإعدادات المحلية (الافتراضي 'ar-SA')
 * @returns {string} التاريخ والوقت المنسق
 */
const formatDateTime = (date, locale = 'ar-SA') => {
  if (!date) return '';
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * تنسيق المبلغ المالي
 * @param {number} amount - المبلغ
 * @param {string} currency - رمز العملة (الافتراضي 'SAR')
 * @param {string} locale - الإعدادات المحلية (الافتراضي 'ar-SA')
 * @returns {string} المبلغ المنسق
 */
const formatCurrency = (amount, currency = 'SAR', locale = 'ar-SA') => {
  if (amount === null || amount === undefined) return '';
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency
  }).format(amount);
};

/**
 * تنسيق الرقم
 * @param {number} number - الرقم
 * @param {string} locale - الإعدادات المحلية (الافتراضي 'ar-SA')
 * @returns {string} الرقم المنسق
 */
const formatNumber = (number, locale = 'ar-SA') => {
  if (number === null || number === undefined) return '';
  return new Intl.NumberFormat(locale).format(number);
};

/**
 * تحويل سلسلة إلى Slug (للاستخدام في URLs)
 * @param {string} text - النص المراد تحويله
 * @returns {string} النص المحول إلى slug
 */
const slugify = (text) => {
  return text
    .toString()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-');
};

/**
 * إنشاء رقم تسلسلي للطلب
 * @param {number} lastOrderNumber - آخر رقم طلب (اختياري)
 * @param {string} prefix - بادئة الرقم (الافتراضي 'ORD')
 * @returns {string} رقم الطلب
 */
const generateOrderNumber = (lastOrderNumber = 0, prefix = 'ORD') => {
  const nextNumber = lastOrderNumber + 1;
  return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
};

/**
 * إنشاء رقم تسلسلي للفاتورة
 * @param {number} lastInvoiceNumber - آخر رقم فاتورة (اختياري)
 * @param {string} prefix - بادئة الرقم (الافتراضي 'INV')
 * @returns {string} رقم الفاتورة
 */
const generateInvoiceNumber = (lastInvoiceNumber = 0, prefix = 'INV') => {
  const nextNumber = lastInvoiceNumber + 1;
  return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
};

/**
 * حساب المبلغ الإجمالي للطلب
 * @param {Array} items - عناصر الطلب
 * @returns {number} المبلغ الإجمالي
 */
const calculateOrderTotal = (items) => {
  if (!items || !items.length) return 0;
  return items.reduce((total, item) => {
    return total + (item.quantity * item.unitPrice);
  }, 0);
};

/**
 * حساب مبلغ الضريبة
 * @param {number} amount - المبلغ
 * @param {number} taxRate - نسبة الضريبة
 * @returns {number} مبلغ الضريبة
 */
const calculateTaxAmount = (amount, taxRate) => {
  return amount * (taxRate / 100);
};

/**
 * تحويل حالة الطلب إلى النص العربي
 * @param {string} status - حالة الطلب
 * @returns {string} النص العربي للحالة
 */
const translateOrderStatus = (status) => {
  const statusMap = {
    'pending': 'قيد الانتظار',
    'processing': 'قيد المعالجة',
    'in-production': 'في الإنتاج',
    'ready': 'جاهز للتسليم',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغي'
  };
  return statusMap[status] || status;
};

/**
 * تحويل حالة الدفع إلى النص العربي
 * @param {string} status - حالة الدفع
 * @returns {string} النص العربي للحالة
 */
const translatePaymentStatus = (status) => {
  const statusMap = {
    'unpaid': 'غير مدفوع',
    'partial': 'مدفوع جزئيًا',
    'paid': 'مدفوع بالكامل'
  };
  return statusMap[status] || status;
};

/**
 * تحويل طريقة الدفع إلى النص العربي
 * @param {string} method - طريقة الدفع
 * @returns {string} النص العربي لطريقة الدفع
 */
const translatePaymentMethod = (method) => {
  const methodMap = {
    'cash': 'نقدًا',
    'credit_card': 'بطاقة ائتمان',
    'bank_transfer': 'تحويل بنكي',
    'check': 'شيك'
  };
  return methodMap[method] || method;
};

/**
 * تحويل دور المستخدم إلى النص العربي
 * @param {string} role - دور المستخدم
 * @returns {string} النص العربي للدور
 */
const translateUserRole = (role) => {
  const roleMap = {
    'admin': 'مدير النظام',
    'manager': 'مدير',
    'employee': 'موظف'
  };
  return roleMap[role] || role;
};

/**
 * تحويل نوع العميل إلى النص العربي
 * @param {string} type - نوع العميل
 * @returns {string} النص العربي للنوع
 */
const translateCustomerType = (type) => {
  const typeMap = {
    'individual': 'فرد',
    'company': 'شركة'
  };
  return typeMap[type] || type;
};

/**
 * تنظيف وتنسيق رقم الهاتف
 * @param {string} phone - رقم الهاتف
 * @returns {string} رقم الهاتف المنسق
 */
const formatPhoneNumber = (phone) => {
  if (!phone) return '';
  // إزالة جميع الأحرف غير الرقمية
  const cleaned = phone.replace(/\D/g, '');
  // تنسيق الرقم حسب الطول
  if (cleaned.length === 10) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11) {
    return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  } else if (cleaned.length === 12) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 9)}-${cleaned.slice(9)}`;
  }
  return cleaned;
};

/**
 * تحويل النص إلى حالة العنوان (أول حرف من كل كلمة كبير)
 * @param {string} text - النص
 * @returns {string} النص بحالة العنوان
 */
const toTitleCase = (text) => {
  if (!text) return '';
  return text.replace(
    /\w\S*/g,
    (word) => word.charAt(0).toUpperCase() + word.substr(1).toLowerCase()
  );
};

/**
 * اقتطاع النص إلى طول محدد
 * @param {string} text - النص
 * @param {number} length - الطول الأقصى
 * @param {string} suffix - اللاحقة (الافتراضي '...')
 * @returns {string} النص المقتطع
 */
const truncateText = (text, length = 100, suffix = '...') => {
  if (!text) return '';
  if (text.length <= length) return text;
  return text.substring(0, length).trim() + suffix;
};

/**
 * تحويل التاريخ إلى كائن تاريخ
 * @param {string|Date} date - التاريخ
 * @returns {Date|null} كائن التاريخ أو null إذا كان التاريخ غير صالح
 */
const parseDate = (date) => {
  if (!date) return null;
  if (date instanceof Date) return date;
  const parsed = new Date(date);
  return isNaN(parsed.getTime()) ? null : parsed;
};

/**
 * حساب الفرق بين تاريخين بالأيام
 * @param {Date|string} date1 - التاريخ الأول
 * @param {Date|string} date2 - التاريخ الثاني
 * @returns {number} الفرق بالأيام
 */
const daysBetweenDates = (date1, date2) => {
  const d1 = parseDate(date1);
  const d2 = parseDate(date2);
  if (!d1 || !d2) return 0;
  
  const diffTime = Math.abs(d2 - d1);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * إضافة أيام إلى تاريخ
 * @param {Date|string} date - التاريخ
 * @param {number} days - عدد الأيام
 * @returns {Date} التاريخ الجديد
 */
const addDaysToDate = (date, days) => {
  const d = parseDate(date);
  if (!d) return new Date();
  
  d.setDate(d.getDate() + days);
  return d;
};

/**
 * تحويل كائن إلى سلسلة استعلام URL
 * @param {Object} params - كائن المعلمات
 * @returns {string} سلسلة الاستعلام
 */
const objectToQueryString = (params) => {
  if (!params || typeof params !== 'object') return '';
  
  return Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
};

/**
 * تحويل سلسلة استعلام URL إلى كائن
 * @param {string} queryString - سلسلة الاستعلام
 * @returns {Object} كائن المعلمات
 */
const queryStringToObject = (queryString) => {
  if (!queryString) return {};
  
  const params = {};
  const queries = queryString.replace(/^\?/, '').split('&');
  
  queries.forEach(query => {
    const [key, value] = query.split('=');
    if (key) {
      params[decodeURIComponent(key)] = decodeURIComponent(value || '');
    }
  });
  
  return params;
};

/**
 * تحويل كائن إلى نموذج FormData
 * @param {Object} data - البيانات
 * @returns {FormData} كائن FormData
 */
const objectToFormData = (data) => {
  const formData = new FormData();
  
  Object.keys(data).forEach(key => {
    if (data[key] !== undefined && data[key] !== null) {
      if (Array.isArray(data[key])) {
        data[key].forEach((item, index) => {
          if (typeof item === 'object' && !(item instanceof File)) {
            Object.keys(item).forEach(itemKey => {
              formData.append(`${key}[${index}][${itemKey}]`, item[itemKey]);
            });
          } else {
            formData.append(`${key}[${index}]`, item);
          }
        });
      } else if (typeof data[key] === 'object' && !(data[key] instanceof File)) {
        Object.keys(data[key]).forEach(subKey => {
          formData.append(`${key}[${subKey}]`, data[key][subKey]);
        });
      } else {
        formData.append(key, data[key]);
      }
    }
  });
  
  return formData;
};

/**
 * تحويل حجم الملف إلى تنسيق مقروء
 * @param {number} bytes - حجم الملف بالبايت
 * @param {number} decimals - عدد الأرقام العشرية (الافتراضي 2)
 * @returns {string} حجم الملف المنسق
 */
const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * الحصول على امتداد الملف من اسم الملف
 * @param {string} filename - اسم الملف
 * @returns {string} امتداد الملف
 */
const getFileExtension = (filename) => {
  if (!filename) return '';
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * التحقق مما إذا كان الملف صورة
 * @param {string} filename - اسم الملف
 * @returns {boolean} صحيح إذا كان الملف صورة
 */
const isImageFile = (filename) => {
  if (!filename) return false;
  const ext = getFileExtension(filename).toLowerCase();
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext);
};

/**
 * التحقق مما إذا كان الملف مستند PDF
 * @param {string} filename - اسم الملف
 * @returns {boolean} صحيح إذا كان الملف PDF
 */
const isPdfFile = (filename) => {
  if (!filename) return false;
  const ext = getFileExtension(filename).toLowerCase();
  return ext === 'pdf';
};

/**
 * إنشاء معرف فريد
 * @returns {string} معرف فريد
 */
const generateUniqueId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

module.exports = {
  formatDate,
  formatDateTime,
  formatCurrency,
  formatNumber,
  slugify,
  generateOrderNumber,
  generateInvoiceNumber,
  calculateOrderTotal,
  calculateTaxAmount,
  translateOrderStatus,
  translatePaymentStatus,
  translatePaymentMethod,
  translateUserRole,
  translateCustomerType,
  formatPhoneNumber,
  toTitleCase,
  truncateText,
  parseDate,
  daysBetweenDates,
  addDaysToDate,
  objectToQueryString,
  queryStringToObject,
  objectToFormData,
  formatFileSize,
  getFileExtension,
  isImageFile,
  isPdfFile,
  generateUniqueId
};