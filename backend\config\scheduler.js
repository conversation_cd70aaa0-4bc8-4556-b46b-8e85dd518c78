/**
 * ملف إعداد وتكوين الجدولة
 * يقوم بإعداد وتكوين المهام المجدولة والمتكررة للتطبيق
 */

const cron = require('node-cron');
const config = require('config');
const logger = require('../utils/logger');
const { recordError } = require('./monitoring');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

// قائمة المهام المجدولة
const scheduledTasks = new Map();

/**
 * إضافة مهمة مجدولة
 * @param {string} taskName - اسم المهمة
 * @param {string} cronExpression - تعبير cron للجدولة
 * @param {Function} taskFunction - الدالة التي سيتم تنفيذها
 * @param {Object} options - خيارات إضافية
 * @returns {Object} كائن المهمة المجدولة
 */
const scheduleTask = (taskName, cronExpression, taskFunction, options = {}) => {
  try {
    // التحقق من صحة تعبير cron
    if (!cron.validate(cronExpression)) {
      throw new Error(`تعبير cron غير صالح: ${cronExpression}`);
    }
    
    // إلغاء المهمة إذا كانت موجودة مسبقًا
    if (scheduledTasks.has(taskName)) {
      scheduledTasks.get(taskName).stop();
      logger.info(`تم إلغاء المهمة المجدولة السابقة: ${taskName}`);
    }
    
    // إنشاء مهمة مجدولة جديدة
    const task = cron.schedule(
      cronExpression,
      async () => {
        const startTime = Date.now();
        logger.info(`بدء تنفيذ المهمة المجدولة: ${taskName}`);
        
        try {
          await taskFunction();
          const duration = Date.now() - startTime;
          logger.info(`اكتمل تنفيذ المهمة المجدولة: ${taskName} (${duration}ms)`);
        } catch (error) {
          const duration = Date.now() - startTime;
          logger.error(`فشل تنفيذ المهمة المجدولة: ${taskName} (${duration}ms)`, error);
          recordError(error);
          
          // إعادة المحاولة إذا تم تكوين ذلك
          if (options.retry && options.retry.attempts > 0) {
            const retryDelay = options.retry.delay || 60000; // 1 دقيقة افتراضيًا
            const remainingAttempts = options.retry.attempts - 1;
            
            logger.info(`جدولة إعادة محاولة للمهمة: ${taskName} (المحاولات المتبقية: ${remainingAttempts})`);
            
            setTimeout(() => {
              scheduleTask(taskName, cronExpression, taskFunction, {
                ...options,
                retry: {
                  ...options.retry,
                  attempts: remainingAttempts,
                },
              });
            }, retryDelay);
          }
        }
      },
      {
        scheduled: options.autoStart !== false,
        timezone: options.timezone || appConfig.timezone || 'UTC',
      }
    );
    
    // تخزين المهمة في القائمة
    scheduledTasks.set(taskName, task);
    
    logger.info(`تمت جدولة المهمة: ${taskName} (${cronExpression})`);
    
    return task;
  } catch (error) {
    logger.error(`فشل في جدولة المهمة: ${taskName}`, error);
    recordError(error);
    throw error;
  }
};

/**
 * إلغاء مهمة مجدولة
 * @param {string} taskName - اسم المهمة
 * @returns {boolean} نجاح العملية
 */
const cancelTask = (taskName) => {
  try {
    if (scheduledTasks.has(taskName)) {
      scheduledTasks.get(taskName).stop();
      scheduledTasks.delete(taskName);
      logger.info(`تم إلغاء المهمة المجدولة: ${taskName}`);
      return true;
    }
    
    logger.warn(`محاولة إلغاء مهمة غير موجودة: ${taskName}`);
    return false;
  } catch (error) {
    logger.error(`فشل في إلغاء المهمة: ${taskName}`, error);
    recordError(error);
    return false;
  }
};

/**
 * تنفيذ مهمة مجدولة على الفور
 * @param {string} taskName - اسم المهمة
 * @returns {Promise<boolean>} نجاح العملية
 */
const executeTaskNow = async (taskName) => {
  try {
    if (scheduledTasks.has(taskName)) {
      const task = scheduledTasks.get(taskName);
      
      if (typeof task.taskFunction === 'function') {
        logger.info(`تنفيذ المهمة على الفور: ${taskName}`);
        await task.taskFunction();
        logger.info(`اكتمل تنفيذ المهمة على الفور: ${taskName}`);
        return true;
      }
      
      logger.warn(`لا يمكن تنفيذ المهمة على الفور (دالة غير متاحة): ${taskName}`);
      return false;
    }
    
    logger.warn(`محاولة تنفيذ مهمة غير موجودة: ${taskName}`);
    return false;
  } catch (error) {
    logger.error(`فشل في تنفيذ المهمة على الفور: ${taskName}`, error);
    recordError(error);
    return false;
  }
};

/**
 * الحصول على قائمة المهام المجدولة
 * @returns {Array} قائمة المهام المجدولة
 */
const getScheduledTasks = () => {
  return Array.from(scheduledTasks.keys()).map(taskName => ({
    name: taskName,
    status: scheduledTasks.get(taskName).getStatus(),
  }));
};

/**
 * إيقاف جميع المهام المجدولة
 */
const stopAllTasks = () => {
  try {
    for (const [taskName, task] of scheduledTasks.entries()) {
      task.stop();
      logger.info(`تم إيقاف المهمة المجدولة: ${taskName}`);
    }
    
    logger.info('تم إيقاف جميع المهام المجدولة');
  } catch (error) {
    logger.error('فشل في إيقاف جميع المهام المجدولة', error);
    recordError(error);
  }
};

/**
 * إعادة تشغيل جميع المهام المجدولة
 */
const restartAllTasks = () => {
  try {
    for (const [taskName, task] of scheduledTasks.entries()) {
      task.start();
      logger.info(`تم إعادة تشغيل المهمة المجدولة: ${taskName}`);
    }
    
    logger.info('تم إعادة تشغيل جميع المهام المجدولة');
  } catch (error) {
    logger.error('فشل في إعادة تشغيل جميع المهام المجدولة', error);
    recordError(error);
  }
};

/**
 * إعداد المهام المجدولة الافتراضية
 */
const setupDefaultTasks = () => {
  // مهمة تنظيف الملفات المؤقتة (كل يوم في الساعة 3 صباحًا)
  scheduleTask(
    'cleanup-temp-files',
    '0 3 * * *',
    async () => {
      try {
        const { cleanupTempFiles } = require('./upload');
        const deletedCount = await cleanupTempFiles();
        logger.info(`تم تنظيف ${deletedCount} ملفات مؤقتة`);
      } catch (error) {
        logger.error('فشل في تنظيف الملفات المؤقتة', error);
        throw error;
      }
    },
    {
      retry: { attempts: 3, delay: 30000 }, // 3 محاولات، 30 ثانية بين كل محاولة
    }
  );
  
  // مهمة تحديث مؤشرات لوحة التحكم (كل ساعة)
  scheduleTask(
    'update-dashboard-metrics',
    '0 * * * *',
    async () => {
      try {
        // يمكن تنفيذ عمليات تحديث مؤشرات لوحة التحكم هنا
        logger.info('تم تحديث مؤشرات لوحة التحكم');
      } catch (error) {
        logger.error('فشل في تحديث مؤشرات لوحة التحكم', error);
        throw error;
      }
    }
  );
  
  // مهمة التحقق من المخزون المنخفض (كل يوم في الساعة 9 صباحًا)
  scheduleTask(
    'check-low-inventory',
    '0 9 * * *',
    async () => {
      try {
        // يمكن تنفيذ عمليات التحقق من المخزون المنخفض هنا
        logger.info('تم التحقق من المخزون المنخفض');
      } catch (error) {
        logger.error('فشل في التحقق من المخزون المنخفض', error);
        throw error;
      }
    }
  );
  
  // مهمة إرسال تذكيرات الفواتير المستحقة (كل يوم في الساعة 10 صباحًا)
  scheduleTask(
    'send-invoice-reminders',
    '0 10 * * *',
    async () => {
      try {
        // يمكن تنفيذ عمليات إرسال تذكيرات الفواتير المستحقة هنا
        logger.info('تم إرسال تذكيرات الفواتير المستحقة');
      } catch (error) {
        logger.error('فشل في إرسال تذكيرات الفواتير المستحقة', error);
        throw error;
      }
    }
  );
  
  // مهمة النسخ الاحتياطي للبيانات (كل أسبوع في يوم الأحد الساعة 2 صباحًا)
  scheduleTask(
    'database-backup',
    '0 2 * * 0',
    async () => {
      try {
        // يمكن تنفيذ عمليات النسخ الاحتياطي للبيانات هنا
        logger.info('تم إجراء النسخ الاحتياطي للبيانات');
      } catch (error) {
        logger.error('فشل في إجراء النسخ الاحتياطي للبيانات', error);
        throw error;
      }
    },
    {
      retry: { attempts: 5, delay: 60000 }, // 5 محاولات، دقيقة واحدة بين كل محاولة
    }
  );
  
  // مهمة تنظيف سجلات النظام القديمة (كل شهر في اليوم الأول الساعة 4 صباحًا)
  scheduleTask(
    'cleanup-old-logs',
    '0 4 1 * *',
    async () => {
      try {
        // يمكن تنفيذ عمليات تنظيف سجلات النظام القديمة هنا
        logger.info('تم تنظيف سجلات النظام القديمة');
      } catch (error) {
        logger.error('فشل في تنظيف سجلات النظام القديمة', error);
        throw error;
      }
    }
  );
  
  logger.info('تم إعداد المهام المجدولة الافتراضية');
};

// معالجة إيقاف التطبيق
process.on('SIGTERM', () => {
  logger.info('تم استلام إشارة SIGTERM، إيقاف جميع المهام المجدولة');
  stopAllTasks();
});

process.on('SIGINT', () => {
  logger.info('تم استلام إشارة SIGINT، إيقاف جميع المهام المجدولة');
  stopAllTasks();
});

module.exports = {
  scheduleTask,
  cancelTask,
  executeTaskNow,
  getScheduledTasks,
  stopAllTasks,
  restartAllTasks,
  setupDefaultTasks,
};