/**
 * ملف إدارة الأخطاء المخصصة للتطبيق
 * يحتوي على تعريفات لفئات الأخطاء المختلفة التي يمكن استخدامها في جميع أنحاء التطبيق
 */

/**
 * خطأ API الأساسي
 * الفئة الأساسية لجميع أخطاء API المخصصة
 */
class ApiError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * خطأ البيانات غير الصالحة
 * يستخدم عندما تكون البيانات المقدمة غير صالحة أو لا تلبي متطلبات التحقق
 */
class ValidationError extends ApiError {
  constructor(message) {
    super(message || 'بيانات غير صالحة', 400);
    this.name = 'ValidationError';
    this.validationErrors = {};
  }

  /**
   * إضافة خطأ تحقق
   * @param {string} field - اسم الحقل
   * @param {string} message - رسالة الخطأ
   */
  addValidationError(field, message) {
    this.validationErrors[field] = message;
    return this;
  }

  /**
   * إضافة أخطاء تحقق متعددة
   * @param {Object} errors - كائن يحتوي على أخطاء التحقق
   */
  addValidationErrors(errors) {
    this.validationErrors = { ...this.validationErrors, ...errors };
    return this;
  }
}

/**
 * خطأ غير موجود
 * يستخدم عندما لا يمكن العثور على المورد المطلوب
 */
class NotFoundError extends ApiError {
  constructor(message) {
    super(message || 'المورد غير موجود', 404);
    this.name = 'NotFoundError';
  }
}

/**
 * خطأ غير مصرح
 * يستخدم عندما لا يكون المستخدم مصرحًا له بالوصول إلى المورد
 */
class UnauthorizedError extends ApiError {
  constructor(message) {
    super(message || 'غير مصرح لك بالوصول', 401);
    this.name = 'UnauthorizedError';
  }
}

/**
 * خطأ محظور
 * يستخدم عندما لا يكون لدى المستخدم الصلاحيات الكافية للوصول إلى المورد
 */
class ForbiddenError extends ApiError {
  constructor(message) {
    super(message || 'محظور الوصول إلى هذا المورد', 403);
    this.name = 'ForbiddenError';
  }
}

/**
 * خطأ تعارض
 * يستخدم عندما يكون هناك تعارض مع حالة المورد الحالية
 */
class ConflictError extends ApiError {
  constructor(message) {
    super(message || 'تعارض مع الحالة الحالية للمورد', 409);
    this.name = 'ConflictError';
  }
}

/**
 * خطأ خدمة خارجية
 * يستخدم عندما تفشل خدمة خارجية (مثل قاعدة البيانات أو API خارجي)
 */
class ExternalServiceError extends ApiError {
  constructor(message, service) {
    super(message || `فشل في الخدمة الخارجية: ${service}`, 500);
    this.name = 'ExternalServiceError';
    this.service = service;
  }
}

/**
 * خطأ في المعالجة
 * يستخدم للأخطاء العامة في معالجة الطلب
 */
class ProcessingError extends ApiError {
  constructor(message) {
    super(message || 'حدث خطأ أثناء معالجة الطلب', 500);
    this.name = 'ProcessingError';
  }
}

/**
 * خطأ في الملف
 * يستخدم للأخطاء المتعلقة بمعالجة الملفات (تحميل، تنزيل، إلخ)
 */
class FileError extends ApiError {
  constructor(message) {
    super(message || 'حدث خطأ أثناء معالجة الملف', 400);
    this.name = 'FileError';
  }
}

/**
 * خطأ في المدخلات
 * يستخدم عندما تكون المدخلات غير صحيحة أو غير مكتملة
 */
class InputError extends ApiError {
  constructor(message) {
    super(message || 'مدخلات غير صحيحة أو غير مكتملة', 400);
    this.name = 'InputError';
  }
}

/**
 * خطأ في الدفع
 * يستخدم للأخطاء المتعلقة بمعالجة الدفع
 */
class PaymentError extends ApiError {
  constructor(message) {
    super(message || 'حدث خطأ أثناء معالجة الدفع', 400);
    this.name = 'PaymentError';
  }
}

module.exports = {
  ApiError,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ConflictError,
  ExternalServiceError,
  ProcessingError,
  FileError,
  InputError,
  PaymentError
};