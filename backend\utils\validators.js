const { body, param, query, validationResult } = require('express-validator');
const { ValidationError } = require('./errors');

/**
 * التحقق من نتائج التحقق وإرجاع الأخطاء إذا وجدت
 * @param {Object} req - كائن الطلب
 * @param {Object} res - كائن الاستجابة
 * @param {Function} next - دالة الانتقال إلى الوسيط التالي
 */
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (errors.isEmpty()) {
    return next();
  }

  const extractedErrors = {};
  errors.array().forEach(err => {
    extractedErrors[err.param] = err.msg;
  });

  const validationError = new ValidationError('خطأ في التحقق من البيانات');
  validationError.addValidationErrors(extractedErrors);
  
  return next(validationError);
};

/**
 * قواعد التحقق للمستخدمين
 */
const userValidationRules = {
  create: [
    body('name')
      .notEmpty().withMessage('اسم المستخدم مطلوب')
      .isLength({ min: 3, max: 50 }).withMessage('يجب أن يكون اسم المستخدم بين 3 و 50 حرفًا'),
    body('email')
      .notEmpty().withMessage('البريد الإلكتروني مطلوب')
      .isEmail().withMessage('يرجى إدخال بريد إلكتروني صالح'),
    body('password')
      .notEmpty().withMessage('كلمة المرور مطلوبة')
      .isLength({ min: 6 }).withMessage('يجب أن تكون كلمة المرور 6 أحرف على الأقل')
      .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])/).withMessage('يجب أن تحتوي كلمة المرور على حرف كبير وحرف صغير ورقم واحد على الأقل'),
    body('role')
      .optional()
      .isIn(['admin', 'manager', 'employee']).withMessage('الدور يجب أن يكون admin أو manager أو employee'),
    body('phone')
      .optional()
      .matches(/^\+?[0-9]{10,15}$/).withMessage('يرجى إدخال رقم هاتف صالح'),
    body('isActive')
      .optional()
      .isBoolean().withMessage('يجب أن تكون حالة النشاط قيمة منطقية')
  ],
  update: [
    body('name')
      .optional()
      .isLength({ min: 3, max: 50 }).withMessage('يجب أن يكون اسم المستخدم بين 3 و 50 حرفًا'),
    body('email')
      .optional()
      .isEmail().withMessage('يرجى إدخال بريد إلكتروني صالح'),
    body('password')
      .optional()
      .isLength({ min: 6 }).withMessage('يجب أن تكون كلمة المرور 6 أحرف على الأقل')
      .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])/).withMessage('يجب أن تحتوي كلمة المرور على حرف كبير وحرف صغير ورقم واحد على الأقل'),
    body('role')
      .optional()
      .isIn(['admin', 'manager', 'employee']).withMessage('الدور يجب أن يكون admin أو manager أو employee'),
    body('phone')
      .optional()
      .matches(/^\+?[0-9]{10,15}$/).withMessage('يرجى إدخال رقم هاتف صالح'),
    body('isActive')
      .optional()
      .isBoolean().withMessage('يجب أن تكون حالة النشاط قيمة منطقية')
  ],
  login: [
    body('email')
      .notEmpty().withMessage('البريد الإلكتروني مطلوب')
      .isEmail().withMessage('يرجى إدخال بريد إلكتروني صالح'),
    body('password')
      .notEmpty().withMessage('كلمة المرور مطلوبة')
  ],
  changePassword: [
    body('currentPassword')
      .notEmpty().withMessage('كلمة المرور الحالية مطلوبة'),
    body('newPassword')
      .notEmpty().withMessage('كلمة المرور الجديدة مطلوبة')
      .isLength({ min: 6 }).withMessage('يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل')
      .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])/).withMessage('يجب أن تحتوي كلمة المرور الجديدة على حرف كبير وحرف صغير ورقم واحد على الأقل'),
    body('confirmPassword')
      .notEmpty().withMessage('تأكيد كلمة المرور مطلوب')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('كلمة المرور وتأكيدها غير متطابقين');
        }
        return true;
      })
  ]
};

/**
 * قواعد التحقق للعملاء
 */
const customerValidationRules = {
  create: [
    body('name')
      .notEmpty().withMessage('اسم العميل مطلوب')
      .isLength({ min: 3, max: 100 }).withMessage('يجب أن يكون اسم العميل بين 3 و 100 حرف'),
    body('email')
      .optional({ nullable: true })
      .isEmail().withMessage('يرجى إدخال بريد إلكتروني صالح'),
    body('phone')
      .notEmpty().withMessage('رقم الهاتف مطلوب')
      .matches(/^\+?[0-9]{10,15}$/).withMessage('يرجى إدخال رقم هاتف صالح'),
    body('address')
      .optional()
      .isLength({ min: 5, max: 200 }).withMessage('يجب أن يكون العنوان بين 5 و 200 حرف'),
    body('type')
      .optional()
      .isIn(['individual', 'company']).withMessage('نوع العميل يجب أن يكون individual أو company'),
    body('taxNumber')
      .optional({ nullable: true }),
    body('notes')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن تكون الملاحظات أقل من 500 حرف')
  ],
  update: [
    body('name')
      .optional()
      .isLength({ min: 3, max: 100 }).withMessage('يجب أن يكون اسم العميل بين 3 و 100 حرف'),
    body('email')
      .optional({ nullable: true })
      .isEmail().withMessage('يرجى إدخال بريد إلكتروني صالح'),
    body('phone')
      .optional()
      .matches(/^\+?[0-9]{10,15}$/).withMessage('يرجى إدخال رقم هاتف صالح'),
    body('address')
      .optional()
      .isLength({ min: 5, max: 200 }).withMessage('يجب أن يكون العنوان بين 5 و 200 حرف'),
    body('type')
      .optional()
      .isIn(['individual', 'company']).withMessage('نوع العميل يجب أن يكون individual أو company'),
    body('taxNumber')
      .optional({ nullable: true }),
    body('notes')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن تكون الملاحظات أقل من 500 حرف')
  ]
};

/**
 * قواعد التحقق للطلبات
 */
const orderValidationRules = {
  create: [
    body('customer')
      .notEmpty().withMessage('معرف العميل مطلوب')
      .isMongoId().withMessage('معرف العميل غير صالح'),
    body('orderItems')
      .notEmpty().withMessage('عناصر الطلب مطلوبة')
      .isArray({ min: 1 }).withMessage('يجب أن يحتوي الطلب على عنصر واحد على الأقل'),
    body('orderItems.*.product')
      .notEmpty().withMessage('معرف المنتج مطلوب')
      .isMongoId().withMessage('معرف المنتج غير صالح'),
    body('orderItems.*.quantity')
      .notEmpty().withMessage('الكمية مطلوبة')
      .isInt({ min: 1 }).withMessage('يجب أن تكون الكمية رقمًا صحيحًا أكبر من 0'),
    body('orderItems.*.unitPrice')
      .notEmpty().withMessage('سعر الوحدة مطلوب')
      .isFloat({ min: 0 }).withMessage('يجب أن يكون سعر الوحدة رقمًا موجبًا'),
    body('orderItems.*.specifications')
      .optional(),
    body('totalAmount')
      .notEmpty().withMessage('المبلغ الإجمالي مطلوب')
      .isFloat({ min: 0 }).withMessage('يجب أن يكون المبلغ الإجمالي رقمًا موجبًا'),
    body('status')
      .optional()
      .isIn(['pending', 'processing', 'in-production', 'ready', 'delivered', 'cancelled'])
      .withMessage('حالة الطلب غير صالحة'),
    body('paymentStatus')
      .optional()
      .isIn(['unpaid', 'partial', 'paid'])
      .withMessage('حالة الدفع غير صالحة'),
    body('paymentMethod')
      .optional()
      .isIn(['cash', 'credit_card', 'bank_transfer', 'check'])
      .withMessage('طريقة الدفع غير صالحة'),
    body('paidAmount')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون المبلغ المدفوع رقمًا موجبًا'),
    body('requiredDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ التسليم المطلوب بتنسيق تاريخ صالح'),
    body('notes')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن تكون الملاحظات أقل من 500 حرف')
  ],
  update: [
    body('status')
      .optional()
      .isIn(['pending', 'processing', 'in-production', 'ready', 'delivered', 'cancelled'])
      .withMessage('حالة الطلب غير صالحة'),
    body('paymentStatus')
      .optional()
      .isIn(['unpaid', 'partial', 'paid'])
      .withMessage('حالة الدفع غير صالحة'),
    body('paymentMethod')
      .optional()
      .isIn(['cash', 'credit_card', 'bank_transfer', 'check'])
      .withMessage('طريقة الدفع غير صالحة'),
    body('paidAmount')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون المبلغ المدفوع رقمًا موجبًا'),
    body('requiredDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ التسليم المطلوب بتنسيق تاريخ صالح'),
    body('estimatedDeliveryDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ التسليم المتوقع بتنسيق تاريخ صالح'),
    body('notes')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن تكون الملاحظات أقل من 500 حرف')
  ],
  updateItems: [
    body('orderItems')
      .notEmpty().withMessage('عناصر الطلب مطلوبة')
      .isArray({ min: 1 }).withMessage('يجب أن يحتوي الطلب على عنصر واحد على الأقل'),
    body('orderItems.*.product')
      .notEmpty().withMessage('معرف المنتج مطلوب')
      .isMongoId().withMessage('معرف المنتج غير صالح'),
    body('orderItems.*.quantity')
      .notEmpty().withMessage('الكمية مطلوبة')
      .isInt({ min: 1 }).withMessage('يجب أن تكون الكمية رقمًا صحيحًا أكبر من 0'),
    body('orderItems.*.unitPrice')
      .notEmpty().withMessage('سعر الوحدة مطلوب')
      .isFloat({ min: 0 }).withMessage('يجب أن يكون سعر الوحدة رقمًا موجبًا'),
    body('orderItems.*.specifications')
      .optional(),
    body('totalAmount')
      .notEmpty().withMessage('المبلغ الإجمالي مطلوب')
      .isFloat({ min: 0 }).withMessage('يجب أن يكون المبلغ الإجمالي رقمًا موجبًا')
  ]
};

/**
 * قواعد التحقق للمنتجات
 */
const productValidationRules = {
  create: [
    body('name')
      .notEmpty().withMessage('اسم المنتج مطلوب')
      .isLength({ min: 2, max: 100 }).withMessage('يجب أن يكون اسم المنتج بين 2 و 100 حرف'),
    body('description')
      .optional()
      .isLength({ max: 1000 }).withMessage('يجب أن يكون الوصف أقل من 1000 حرف'),
    body('category')
      .notEmpty().withMessage('فئة المنتج مطلوبة'),
    body('basePrice')
      .notEmpty().withMessage('السعر الأساسي مطلوب')
      .isFloat({ min: 0 }).withMessage('يجب أن يكون السعر الأساسي رقمًا موجبًا'),
    body('options')
      .optional()
      .isArray().withMessage('يجب أن تكون الخيارات مصفوفة'),
    body('options.*.name')
      .optional()
      .notEmpty().withMessage('اسم الخيار مطلوب'),
    body('options.*.choices')
      .optional()
      .isArray().withMessage('يجب أن تكون اختيارات الخيار مصفوفة'),
    body('options.*.choices.*.name')
      .optional()
      .notEmpty().withMessage('اسم الاختيار مطلوب'),
    body('options.*.choices.*.priceAdjustment')
      .optional()
      .isFloat().withMessage('يجب أن يكون تعديل السعر رقمًا'),
    body('isActive')
      .optional()
      .isBoolean().withMessage('يجب أن تكون حالة النشاط قيمة منطقية'),
    body('tags')
      .optional()
      .isArray().withMessage('يجب أن تكون العلامات مصفوفة')
  ],
  update: [
    body('name')
      .optional()
      .isLength({ min: 2, max: 100 }).withMessage('يجب أن يكون اسم المنتج بين 2 و 100 حرف'),
    body('description')
      .optional()
      .isLength({ max: 1000 }).withMessage('يجب أن يكون الوصف أقل من 1000 حرف'),
    body('category')
      .optional(),
    body('basePrice')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون السعر الأساسي رقمًا موجبًا'),
    body('options')
      .optional()
      .isArray().withMessage('يجب أن تكون الخيارات مصفوفة'),
    body('isActive')
      .optional()
      .isBoolean().withMessage('يجب أن تكون حالة النشاط قيمة منطقية'),
    body('tags')
      .optional()
      .isArray().withMessage('يجب أن تكون العلامات مصفوفة')
  ]
};

/**
 * قواعد التحقق للمخزون
 */
const inventoryValidationRules = {
  create: [
    body('itemCode')
      .notEmpty().withMessage('كود العنصر مطلوب')
      .isLength({ min: 2, max: 50 }).withMessage('يجب أن يكون كود العنصر بين 2 و 50 حرف'),
    body('name')
      .notEmpty().withMessage('اسم العنصر مطلوب')
      .isLength({ min: 2, max: 100 }).withMessage('يجب أن يكون اسم العنصر بين 2 و 100 حرف'),
    body('description')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن يكون الوصف أقل من 500 حرف'),
    body('category')
      .notEmpty().withMessage('فئة العنصر مطلوبة'),
    body('unit')
      .notEmpty().withMessage('وحدة القياس مطلوبة'),
    body('currentQuantity')
      .notEmpty().withMessage('الكمية الحالية مطلوبة')
      .isFloat({ min: 0 }).withMessage('يجب أن تكون الكمية الحالية رقمًا موجبًا'),
    body('minQuantity')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون الحد الأدنى للكمية رقمًا موجبًا'),
    body('costPrice')
      .notEmpty().withMessage('سعر التكلفة مطلوب')
      .isFloat({ min: 0 }).withMessage('يجب أن يكون سعر التكلفة رقمًا موجبًا'),
    body('supplier')
      .optional()
      .isMongoId().withMessage('معرف المورد غير صالح'),
    body('location')
      .optional()
      .isLength({ max: 100 }).withMessage('يجب أن يكون الموقع أقل من 100 حرف')
  ],
  update: [
    body('name')
      .optional()
      .isLength({ min: 2, max: 100 }).withMessage('يجب أن يكون اسم العنصر بين 2 و 100 حرف'),
    body('description')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن يكون الوصف أقل من 500 حرف'),
    body('category')
      .optional(),
    body('unit')
      .optional(),
    body('currentQuantity')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن تكون الكمية الحالية رقمًا موجبًا'),
    body('minQuantity')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون الحد الأدنى للكمية رقمًا موجبًا'),
    body('costPrice')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون سعر التكلفة رقمًا موجبًا'),
    body('supplier')
      .optional()
      .isMongoId().withMessage('معرف المورد غير صالح'),
    body('location')
      .optional()
      .isLength({ max: 100 }).withMessage('يجب أن يكون الموقع أقل من 100 حرف')
  ],
  adjustQuantity: [
    body('adjustmentType')
      .notEmpty().withMessage('نوع التعديل مطلوب')
      .isIn(['add', 'subtract']).withMessage('نوع التعديل يجب أن يكون add أو subtract'),
    body('quantity')
      .notEmpty().withMessage('الكمية مطلوبة')
      .isFloat({ min: 0.01 }).withMessage('يجب أن تكون الكمية رقمًا موجبًا أكبر من 0'),
    body('reason')
      .optional()
      .isLength({ max: 200 }).withMessage('يجب أن يكون السبب أقل من 200 حرف')
  ]
};

/**
 * قواعد التحقق للفواتير
 */
const invoiceValidationRules = {
  create: [
    body('order')
      .notEmpty().withMessage('معرف الطلب مطلوب')
      .isMongoId().withMessage('معرف الطلب غير صالح'),
    body('customer')
      .notEmpty().withMessage('معرف العميل مطلوب')
      .isMongoId().withMessage('معرف العميل غير صالح'),
    body('invoiceDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ الفاتورة بتنسيق تاريخ صالح'),
    body('dueDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ الاستحقاق بتنسيق تاريخ صالح'),
    body('items')
      .notEmpty().withMessage('عناصر الفاتورة مطلوبة')
      .isArray({ min: 1 }).withMessage('يجب أن تحتوي الفاتورة على عنصر واحد على الأقل'),
    body('items.*.description')
      .notEmpty().withMessage('وصف العنصر مطلوب'),
    body('items.*.quantity')
      .notEmpty().withMessage('الكمية مطلوبة')
      .isFloat({ min: 0 }).withMessage('يجب أن تكون الكمية رقمًا موجبًا'),
    body('items.*.unitPrice')
      .notEmpty().withMessage('سعر الوحدة مطلوب')
      .isFloat({ min: 0 }).withMessage('يجب أن يكون سعر الوحدة رقمًا موجبًا'),
    body('subtotal')
      .notEmpty().withMessage('المبلغ الفرعي مطلوب')
      .isFloat({ min: 0 }).withMessage('يجب أن يكون المبلغ الفرعي رقمًا موجبًا'),
    body('taxRate')
      .optional()
      .isFloat({ min: 0, max: 100 }).withMessage('يجب أن تكون نسبة الضريبة بين 0 و 100'),
    body('taxAmount')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون مبلغ الضريبة رقمًا موجبًا'),
    body('discount')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون الخصم رقمًا موجبًا'),
    body('totalAmount')
      .notEmpty().withMessage('المبلغ الإجمالي مطلوب')
      .isFloat({ min: 0 }).withMessage('يجب أن يكون المبلغ الإجمالي رقمًا موجبًا'),
    body('paymentStatus')
      .optional()
      .isIn(['unpaid', 'partial', 'paid'])
      .withMessage('حالة الدفع غير صالحة'),
    body('paidAmount')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون المبلغ المدفوع رقمًا موجبًا'),
    body('notes')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن تكون الملاحظات أقل من 500 حرف')
  ],
  update: [
    body('invoiceDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ الفاتورة بتنسيق تاريخ صالح'),
    body('dueDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ الاستحقاق بتنسيق تاريخ صالح'),
    body('paymentStatus')
      .optional()
      .isIn(['unpaid', 'partial', 'paid'])
      .withMessage('حالة الدفع غير صالحة'),
    body('paidAmount')
      .optional()
      .isFloat({ min: 0 }).withMessage('يجب أن يكون المبلغ المدفوع رقمًا موجبًا'),
    body('notes')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن تكون الملاحظات أقل من 500 حرف')
  ],
  recordPayment: [
    body('amount')
      .notEmpty().withMessage('المبلغ مطلوب')
      .isFloat({ min: 0.01 }).withMessage('يجب أن يكون المبلغ رقمًا موجبًا أكبر من 0'),
    body('paymentMethod')
      .notEmpty().withMessage('طريقة الدفع مطلوبة')
      .isIn(['cash', 'credit_card', 'bank_transfer', 'check'])
      .withMessage('طريقة الدفع غير صالحة'),
    body('paymentDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ الدفع بتنسيق تاريخ صالح'),
    body('reference')
      .optional()
      .isLength({ max: 100 }).withMessage('يجب أن يكون المرجع أقل من 100 حرف'),
    body('notes')
      .optional()
      .isLength({ max: 200 }).withMessage('يجب أن تكون الملاحظات أقل من 200 حرف')
  ]
};

/**
 * قواعد التحقق للموردين
 */
const supplierValidationRules = {
  create: [
    body('name')
      .notEmpty().withMessage('اسم المورد مطلوب')
      .isLength({ min: 3, max: 100 }).withMessage('يجب أن يكون اسم المورد بين 3 و 100 حرف'),
    body('contactPerson')
      .optional()
      .isLength({ min: 3, max: 100 }).withMessage('يجب أن يكون اسم جهة الاتصال بين 3 و 100 حرف'),
    body('email')
      .optional({ nullable: true })
      .isEmail().withMessage('يرجى إدخال بريد إلكتروني صالح'),
    body('phone')
      .notEmpty().withMessage('رقم الهاتف مطلوب')
      .matches(/^\+?[0-9]{10,15}$/).withMessage('يرجى إدخال رقم هاتف صالح'),
    body('address')
      .optional()
      .isLength({ min: 5, max: 200 }).withMessage('يجب أن يكون العنوان بين 5 و 200 حرف'),
    body('taxNumber')
      .optional({ nullable: true }),
    body('paymentTerms')
      .optional()
      .isLength({ max: 200 }).withMessage('يجب أن تكون شروط الدفع أقل من 200 حرف'),
    body('notes')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن تكون الملاحظات أقل من 500 حرف'),
    body('categories')
      .optional()
      .isArray().withMessage('يجب أن تكون الفئات مصفوفة')
  ],
  update: [
    body('name')
      .optional()
      .isLength({ min: 3, max: 100 }).withMessage('يجب أن يكون اسم المورد بين 3 و 100 حرف'),
    body('contactPerson')
      .optional()
      .isLength({ min: 3, max: 100 }).withMessage('يجب أن يكون اسم جهة الاتصال بين 3 و 100 حرف'),
    body('email')
      .optional({ nullable: true })
      .isEmail().withMessage('يرجى إدخال بريد إلكتروني صالح'),
    body('phone')
      .optional()
      .matches(/^\+?[0-9]{10,15}$/).withMessage('يرجى إدخال رقم هاتف صالح'),
    body('address')
      .optional()
      .isLength({ min: 5, max: 200 }).withMessage('يجب أن يكون العنوان بين 5 و 200 حرف'),
    body('taxNumber')
      .optional({ nullable: true }),
    body('paymentTerms')
      .optional()
      .isLength({ max: 200 }).withMessage('يجب أن تكون شروط الدفع أقل من 200 حرف'),
    body('notes')
      .optional()
      .isLength({ max: 500 }).withMessage('يجب أن تكون الملاحظات أقل من 500 حرف'),
    body('categories')
      .optional()
      .isArray().withMessage('يجب أن تكون الفئات مصفوفة')
  ]
};

/**
 * قواعد التحقق للمعرفات
 */
const idValidationRules = {
  checkId: [
    param('id')
      .notEmpty().withMessage('المعرف مطلوب')
      .isMongoId().withMessage('المعرف غير صالح')
  ]
};

/**
 * قواعد التحقق للاستعلامات
 */
const queryValidationRules = {
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 }).withMessage('يجب أن تكون الصفحة رقمًا صحيحًا أكبر من 0'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 }).withMessage('يجب أن يكون الحد رقمًا صحيحًا بين 1 و 100'),
    query('sort')
      .optional()
  ],
  dateRange: [
    query('startDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ البداية بتنسيق تاريخ صالح'),
    query('endDate')
      .optional()
      .isISO8601().withMessage('يجب أن يكون تاريخ النهاية بتنسيق تاريخ صالح')
      .custom((value, { req }) => {
        if (req.query.startDate && new Date(value) < new Date(req.query.startDate)) {
          throw new Error('يجب أن يكون تاريخ النهاية بعد تاريخ البداية');
        }
        return true;
      })
  ]
};

module.exports = {
  validate,
  userValidationRules,
  customerValidationRules,
  orderValidationRules,
  productValidationRules,
  inventoryValidationRules,
  invoiceValidationRules,
  supplierValidationRules,
  idValidationRules,
  queryValidationRules
};