const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const supplierController = require('../controllers/supplierController');
const auth = require('../middleware/auth');
const checkPermission = require('../middleware/checkPermission');

/**
 * @route   POST api/suppliers
 * @desc    إنشاء مورد جديد
 * @access  Private
 */
router.post(
  '/',
  [
    auth,
    checkPermission('inventory', 'create'),
    [
      check('name', 'اسم المورد مطلوب').not().isEmpty(),
      check('contactPerson', 'اسم الشخص المسؤول مطلوب').not().isEmpty(),
      check('email', 'يرجى إدخال بريد إلكتروني صحيح').isEmail(),
      check('phone', 'رقم الهاتف مطلوب').not().isEmpty(),
      check('supplierType', 'نوع المورد مطلوب').not().isEmpty()
    ]
  ],
  supplierController.createSupplier
);

/**
 * @route   GET api/suppliers
 * @desc    الحصول على قائمة الموردين
 * @access  Private
 */
router.get(
  '/',
  [auth, checkPermission('inventory', 'read')],
  supplierController.getSuppliers
);

/**
 * @route   GET api/suppliers/:id
 * @desc    الحصول على مورد محدد
 * @access  Private
 */
router.get(
  '/:id',
  [auth, checkPermission('inventory', 'read')],
  supplierController.getSupplierById
);

/**
 * @route   PUT api/suppliers/:id
 * @desc    تحديث مورد
 * @access  Private
 */
router.put(
  '/:id',
  [
    auth,
    checkPermission('inventory', 'update'),
    [
      check('name', 'اسم المورد مطلوب').optional().not().isEmpty(),
      check('contactPerson', 'اسم الشخص المسؤول مطلوب').optional().not().isEmpty(),
      check('email', 'يرجى إدخال بريد إلكتروني صحيح').optional().isEmail(),
      check('phone', 'رقم الهاتف مطلوب').optional().not().isEmpty(),
      check('supplierType', 'نوع المورد مطلوب').optional().not().isEmpty()
    ]
  ],
  supplierController.updateSupplier
);

/**
 * @route   DELETE api/suppliers/:id
 * @desc    حذف مورد
 * @access  Private
 */
router.delete(
  '/:id',
  [auth, checkPermission('inventory', 'delete')],
  supplierController.deleteSupplier
);

/**
 * @route   GET api/suppliers/types
 * @desc    الحصول على قائمة أنواع الموردين
 * @access  Private
 */
router.get(
  '/types',
  [auth, checkPermission('inventory', 'read')],
  supplierController.getSupplierTypes
);

module.exports = router;