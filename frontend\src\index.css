/* إعدادات عامة للتطبيق */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  font-family: 'SF Pro AR Display', 'Cairo', 'Segoe UI', 'Roboto', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  text-align: right;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

/* تحسينات للخطوط العربية */
.arabic-text {
  font-family: 'SF Pro AR Display', 'Cairo', Arial, sans-serif;
  font-feature-settings: 'liga' 1, 'kern' 1;
  text-rendering: optimizeLegibility;
}

/* تحسينات للأرقام العربية */
.arabic-numbers {
  font-variant-numeric: tabular-nums;
  direction: ltr;
  unicode-bidi: embed;
}

/* تحسينات للتخطيط RTL */
.rtl-layout {
  direction: rtl;
  text-align: right;
}

.ltr-layout {
  direction: ltr;
  text-align: left;
}

/* تحسينات للنصوص الطويلة */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* تحسينات للتمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* تحسينات للطباعة */
@media print {
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .no-print {
    display: none !important;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
  body {
    background-color: #ffffff;
    color: #000000;
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
