/**
 * ملف إعداد وتكوين التسجيل (Logging)
 * يقوم بإعداد وتكوين نظام تسجيل الأحداث والأخطاء للتطبيق
 */

const winston = require('winston');
const config = require('config');
const path = require('path');
const fs = require('fs');
const { format } = winston;

// الحصول على إعدادات التسجيل من ملف التكوين
let loggingConfig;
try {
  loggingConfig = config.get('logging');
} catch (error) {
  // استخدام الإعدادات الافتراضية إذا لم يتم العثور على إعدادات في ملف التكوين
  loggingConfig = {
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    console: {
      enabled: true,
      level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
      colorize: true
    },
    file: {
      enabled: true,
      level: 'info',
      filename: 'logs/app-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d'
    },
    error: {
      enabled: true,
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '30d'
    },
    mongodb: {
      enabled: false,
      level: 'info',
      collection: 'logs',
      capped: true,
      cappedSize: 10000000
    },
    slack: {
      enabled: false,
      level: 'error',
      webhookUrl: ''
    },
    telegram: {
      enabled: false,
      level: 'error',
      token: '',
      chatId: ''
    },
    logDirectory: 'logs'
  };
}

// إنشاء دليل السجلات إذا لم يكن موجودًا
const logDir = path.resolve(process.cwd(), loggingConfig.logDirectory || 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// تنسيق السجلات المخصص
const customFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  format.errors({ stack: true }),
  format.splat(),
  format.json()
);

// تنسيق السجلات للطباعة في وحدة التحكم
const consoleFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  format.errors({ stack: true }),
  format.splat(),
  format.colorize(),
  format.printf(({ level, message, timestamp, ...meta }) => {
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
      if (meta.stack) {
        metaStr = `\n${meta.stack}`;
      } else {
        try {
          metaStr = `\n${JSON.stringify(meta, null, 2)}`;
        } catch (e) {
          metaStr = `\n[Meta data could not be stringified]`;
        }
      }
    }
    return `${timestamp} [${level}]: ${message}${metaStr}`;
  })
);

// إنشاء النواقل (Transports)
const transports = [];

// ناقل وحدة التحكم
if (loggingConfig.console && loggingConfig.console.enabled) {
  transports.push(
    new winston.transports.Console({
      level: loggingConfig.console.level || 'debug',
      format: consoleFormat
    })
  );
}

// ناقل الملفات
if (loggingConfig.file && loggingConfig.file.enabled) {
  const { DailyRotateFile } = require('winston-daily-rotate-file');
  
  transports.push(
    new DailyRotateFile({
      level: loggingConfig.file.level || 'info',
      filename: path.join(logDir, loggingConfig.file.filename || 'app-%DATE%.log'),
      datePattern: loggingConfig.file.datePattern || 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: loggingConfig.file.maxSize || '20m',
      maxFiles: loggingConfig.file.maxFiles || '14d',
      format: customFormat
    })
  );
}

// ناقل الأخطاء
if (loggingConfig.error && loggingConfig.error.enabled) {
  const { DailyRotateFile } = require('winston-daily-rotate-file');
  
  transports.push(
    new DailyRotateFile({
      level: 'error',
      filename: path.join(logDir, loggingConfig.error.filename || 'error-%DATE%.log'),
      datePattern: loggingConfig.error.datePattern || 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: loggingConfig.error.maxSize || '20m',
      maxFiles: loggingConfig.error.maxFiles || '30d',
      format: customFormat
    })
  );
}

// ناقل MongoDB
if (loggingConfig.mongodb && loggingConfig.mongodb.enabled) {
  const { MongoDBTransport } = require('winston-mongodb');
  
  transports.push(
    new MongoDBTransport({
      level: loggingConfig.mongodb.level || 'info',
      db: process.env.MONGODB_URI,
      options: { useUnifiedTopology: true },
      collection: loggingConfig.mongodb.collection || 'logs',
      capped: loggingConfig.mongodb.capped,
      cappedSize: loggingConfig.mongodb.cappedSize || 10000000,
      format: format.combine(
        format.timestamp(),
        format.json()
      )
    })
  );
}

// ناقل Slack
if (loggingConfig.slack && loggingConfig.slack.enabled && loggingConfig.slack.webhookUrl) {
  const { SlackHook } = require('winston-slack-webhook-transport');
  
  transports.push(
    new SlackHook({
      level: loggingConfig.slack.level || 'error',
      webhookUrl: loggingConfig.slack.webhookUrl,
      channel: loggingConfig.slack.channel,
      username: loggingConfig.slack.username || 'LoggerBot',
      iconEmoji: loggingConfig.slack.iconEmoji || ':rotating_light:',
      formatter: (info) => {
        return {
          text: `*${info.level.toUpperCase()}*: ${info.message}`,
          attachments: [
            {
              color: info.level === 'error' ? 'danger' : (info.level === 'warn' ? 'warning' : 'good'),
              fields: [
                {
                  title: 'Time',
                  value: new Date().toISOString(),
                  short: true
                },
                {
                  title: 'Environment',
                  value: process.env.NODE_ENV,
                  short: true
                },
                {
                  title: 'Details',
                  value: JSON.stringify(info, null, 2),
                  short: false
                }
              ]
            }
          ]
        };
      }
    })
  );
}

// ناقل Telegram
if (loggingConfig.telegram && loggingConfig.telegram.enabled && loggingConfig.telegram.token && loggingConfig.telegram.chatId) {
  const { TelegramLogger } = require('winston-telegram');
  
  transports.push(
    new TelegramLogger({
      level: loggingConfig.telegram.level || 'error',
      token: loggingConfig.telegram.token,
      chatId: loggingConfig.telegram.chatId,
      parseMode: 'HTML',
      formatMessage: (info) => {
        return `<b>${info.level.toUpperCase()}</b>: ${info.message}\n\n<pre>${JSON.stringify(info, null, 2)}</pre>`;
      }
    })
  );
}

// إنشاء المسجل (Logger)
const logger = winston.createLogger({
  level: loggingConfig.level || 'info',
  format: customFormat,
  defaultMeta: { service: 'print-system' },
  transports,
  exitOnError: false
});

/**
 * تسجيل الطلبات HTTP
 * @param {Express} app - تطبيق Express
 */
const setupRequestLogger = (app) => {
  const morgan = require('morgan');
  
  // تنسيق مخصص لـ morgan
  const morganFormat = ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" - :response-time ms';
  
  // إنشاء ناقل لتسجيل الطلبات
  const stream = {
    write: (message) => {
      logger.http(message.trim());
    }
  };
  
  // استخدام morgan لتسجيل الطلبات
  app.use(morgan(morganFormat, { stream }));
};

/**
 * وسيط لتسجيل الأخطاء
 * @param {Error} err - الخطأ
 * @param {Request} req - الطلب
 * @param {Response} res - الاستجابة
 * @param {Function} next - الدالة التالية
 */
const errorLogger = (err, req, res, next) => {
  const errorInfo = {
    method: req.method,
    url: req.url,
    headers: req.headers,
    query: req.query,
    body: req.body,
    user: req.user ? { id: req.user.id, username: req.user.username } : 'غير مصرح',
    error: {
      message: err.message,
      stack: err.stack,
      status: err.status || 500
    }
  };
  
  logger.error(`خطأ في الطلب: ${req.method} ${req.url}`, errorInfo);
  
  next(err);
};

/**
 * تسجيل بداية التطبيق
 */
const logAppStart = () => {
  logger.info(`بدء تشغيل التطبيق في بيئة ${process.env.NODE_ENV}`);
  logger.info(`الإصدار: ${process.env.npm_package_version || 'غير معروف'}`);
  logger.info(`المنفذ: ${process.env.PORT || 3000}`);
};

/**
 * تسجيل إيقاف التطبيق
 */
const logAppShutdown = () => {
  logger.info('إيقاف تشغيل التطبيق...');
};

/**
 * تسجيل الأخطاء غير المعالجة
 */
const setupUncaughtExceptionHandlers = () => {
  // التقاط الاستثناءات غير المعالجة
  process.on('uncaughtException', (err) => {
    logger.error('استثناء غير معالج', { error: err.message, stack: err.stack });
    // إيقاف التطبيق بعد تسجيل الخطأ
    process.exit(1);
  });
  
  // التقاط الوعود المرفوضة غير المعالجة
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('وعد مرفوض غير معالج', { reason, promise });
  });
};

// تصدير الدوال والمتغيرات
module.exports = {
  logger,
  setupRequestLogger,
  errorLogger,
  logAppStart,
  logAppShutdown,
  setupUncaughtExceptionHandlers
};