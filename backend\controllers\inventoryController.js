const Inventory = require('../models/Inventory');
const Supplier = require('../models/Supplier');
const { validationResult } = require('express-validator');

/**
 * @route   POST api/inventory
 * @desc    إضافة عنصر جديد للمخزون
 * @access  Private
 */
exports.addInventoryItem = async (req, res) => {
  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const {
    name,
    category,
    description,
    unit,
    quantity,
    minimumQuantity,
    costPrice,
    supplier,
    location,
    specifications,
    image,
    active
  } = req.body;

  try {
    // التحقق من وجود المورد إذا تم تحديده
    if (supplier) {
      const supplierExists = await Supplier.findById(supplier);
      if (!supplierExists) {
        return res.status(404).json({ msg: 'المورد غير موجود' });
      }
    }

    // التحقق من عدم وجود عنصر بنفس الاسم والفئة
    const existingItem = await Inventory.findOne({ name, category });
    if (existingItem) {
      return res.status(400).json({ msg: 'يوجد عنصر بنفس الاسم والفئة بالفعل' });
    }

    // إنشاء عنصر مخزون جديد
    const inventoryItem = new Inventory({
      name,
      category,
      description,
      unit,
      quantity,
      minimumQuantity,
      costPrice,
      supplier,
      location,
      specifications,
      image,
      active: active !== undefined ? active : true,
      createdBy: req.user.id
    });

    // إضافة معاملة أولية للمخزون
    inventoryItem.transactions.push({
      type: 'إضافة',
      quantity,
      date: Date.now(),
      notes: 'إضافة أولية للمخزون',
      performedBy: req.user.id
    });

    // حفظ العنصر في قاعدة البيانات
    await inventoryItem.save();

    res.json(inventoryItem);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/inventory
 * @desc    الحصول على قائمة عناصر المخزون
 * @access  Private
 */
exports.getInventoryItems = async (req, res) => {
  try {
    // البحث عن عناصر المخزون مع إمكانية التصفية
    const query = {};
    
    // تصفية حسب الفئة
    if (req.query.category) {
      query.category = req.query.category;
    }
    
    // تصفية حسب المورد
    if (req.query.supplier) {
      query.supplier = req.query.supplier;
    }
    
    // تصفية حسب الحالة النشطة
    if (req.query.active !== undefined) {
      query.active = req.query.active === 'true';
    }
    
    // البحث بالاسم أو الوصف
    if (req.query.search) {
      query.$or = [
        { name: { $regex: req.query.search, $options: 'i' } },
        { description: { $regex: req.query.search, $options: 'i' } },
        { itemCode: { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    // تصفية حسب المخزون المنخفض
    if (req.query.lowStock === 'true') {
      query.$expr = { $lte: ['$quantity', '$minimumQuantity'] };
    }
    
    // الحصول على عناصر المخزون مع الترتيب والتقسيم
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const inventoryItems = await Inventory.find(query)
      .populate('supplier', 'name')
      .populate('createdBy', 'name')
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit);
    
    // الحصول على إجمالي عدد العناصر للتصفح
    const total = await Inventory.countDocuments(query);
    
    res.json({
      inventoryItems,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/inventory/:id
 * @desc    الحصول على عنصر مخزون محدد
 * @access  Private
 */
exports.getInventoryItemById = async (req, res) => {
  try {
    const inventoryItem = await Inventory.findById(req.params.id)
      .populate('supplier', 'name email phone')
      .populate('createdBy', 'name')
      .populate('transactions.performedBy', 'name');

    if (!inventoryItem) {
      return res.status(404).json({ msg: 'عنصر المخزون غير موجود' });
    }

    res.json(inventoryItem);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'عنصر المخزون غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   PUT api/inventory/:id
 * @desc    تحديث عنصر مخزون
 * @access  Private
 */
exports.updateInventoryItem = async (req, res) => {
  const {
    name,
    category,
    description,
    unit,
    minimumQuantity,
    costPrice,
    supplier,
    location,
    specifications,
    image,
    active
  } = req.body;

  // بناء كائن تحديث عنصر المخزون
  const inventoryFields = {};
  if (name) inventoryFields.name = name;
  if (category) inventoryFields.category = category;
  if (description) inventoryFields.description = description;
  if (unit) inventoryFields.unit = unit;
  if (minimumQuantity !== undefined) inventoryFields.minimumQuantity = minimumQuantity;
  if (costPrice !== undefined) inventoryFields.costPrice = costPrice;
  if (supplier) inventoryFields.supplier = supplier;
  if (location) inventoryFields.location = location;
  if (specifications) inventoryFields.specifications = specifications;
  if (image) inventoryFields.image = image;
  if (active !== undefined) inventoryFields.active = active;

  try {
    // التحقق من وجود المورد إذا تم تحديده
    if (supplier) {
      const supplierExists = await Supplier.findById(supplier);
      if (!supplierExists) {
        return res.status(404).json({ msg: 'المورد غير موجود' });
      }
    }

    let inventoryItem = await Inventory.findById(req.params.id);

    if (!inventoryItem) {
      return res.status(404).json({ msg: 'عنصر المخزون غير موجود' });
    }

    // التحقق من عدم وجود عنصر آخر بنفس الاسم والفئة
    if (name && category) {
      const existingItem = await Inventory.findOne({
        _id: { $ne: req.params.id },
        name,
        category
      });
      if (existingItem) {
        return res.status(400).json({ msg: 'يوجد عنصر آخر بنفس الاسم والفئة بالفعل' });
      }
    }

    // تحديث عنصر المخزون
    inventoryItem = await Inventory.findByIdAndUpdate(
      req.params.id,
      { $set: inventoryFields },
      { new: true }
    );

    res.json(inventoryItem);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'عنصر المخزون غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   POST api/inventory/transaction/:id
 * @desc    إضافة معاملة لعنصر مخزون (إضافة أو سحب)
 * @access  Private
 */
exports.addInventoryTransaction = async (req, res) => {
  const { type, quantity, reference, orderId, notes } = req.body;

  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const inventoryItem = await Inventory.findById(req.params.id);

    if (!inventoryItem) {
      return res.status(404).json({ msg: 'عنصر المخزون غير موجود' });
    }

    // التحقق من كمية السحب
    if (type === 'سحب' && quantity > inventoryItem.quantity) {
      return res.status(400).json({ msg: 'الكمية المطلوبة أكبر من المتوفرة في المخزون' });
    }

    // إنشاء معاملة جديدة
    const newTransaction = {
      type,
      quantity,
      date: Date.now(),
      reference,
      orderId,
      notes,
      performedBy: req.user.id
    };

    // تحديث الكمية في المخزون
    if (type === 'إضافة') {
      inventoryItem.quantity += quantity;
    } else if (type === 'سحب') {
      inventoryItem.quantity -= quantity;
    }

    // إضافة المعاملة إلى سجل المعاملات
    inventoryItem.transactions.unshift(newTransaction);

    // حفظ التغييرات
    await inventoryItem.save();

    res.json(inventoryItem);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'عنصر المخزون غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   DELETE api/inventory/:id
 * @desc    حذف عنصر مخزون
 * @access  Private
 */
exports.deleteInventoryItem = async (req, res) => {
  try {
    const inventoryItem = await Inventory.findById(req.params.id);

    if (!inventoryItem) {
      return res.status(404).json({ msg: 'عنصر المخزون غير موجود' });
    }

    // حذف عنصر المخزون
    await Inventory.findByIdAndRemove(req.params.id);

    res.json({ msg: 'تم حذف عنصر المخزون' });
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'عنصر المخزون غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/inventory/categories
 * @desc    الحصول على قائمة فئات المخزون
 * @access  Private
 */
exports.getInventoryCategories = async (req, res) => {
  try {
    const categories = await Inventory.distinct('category');
    res.json(categories);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/inventory/dashboard/stats
 * @desc    الحصول على إحصائيات المخزون للوحة المعلومات
 * @access  Private
 */
exports.getInventoryStats = async (req, res) => {
  try {
    // إجمالي عدد عناصر المخزون
    const totalItems = await Inventory.countDocuments();
    
    // عدد العناصر منخفضة المخزون
    const lowStockItems = await Inventory.countDocuments({
      $expr: { $lte: ['$quantity', '$minimumQuantity'] }
    });
    
    // عدد العناصر حسب الفئة
    const itemsByCategory = await Inventory.aggregate([
      { $group: { _id: '$category', count: { $sum: 1 } } }
    ]);
    
    // إجمالي قيمة المخزون
    const inventoryValue = await Inventory.aggregate([
      { $group: { _id: null, total: { $sum: { $multiply: ['$quantity', '$costPrice'] } } } }
    ]);
    
    // أكثر 5 عناصر استخدامًا (بناءً على عدد معاملات السحب)
    const topUsedItems = await Inventory.aggregate([
      { $unwind: '$transactions' },
      { $match: { 'transactions.type': 'سحب' } },
      { $group: { _id: '$_id', name: { $first: '$name' }, totalUsed: { $sum: '$transactions.quantity' } } },
      { $sort: { totalUsed: -1 } },
      { $limit: 5 }
    ]);
    
    res.json({
      totalItems,
      lowStockItems,
      itemsByCategory,
      inventoryValue: inventoryValue.length > 0 ? inventoryValue[0].total : 0,
      topUsedItems
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};