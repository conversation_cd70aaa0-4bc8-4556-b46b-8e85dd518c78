/* خطوط SF Pro AR Display */

@font-face {
  font-family: 'SF Pro AR Display';
  src: url('./SFProARDisplay-Light.woff2') format('woff2'),
       url('./SFProARDisplay-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro AR Display';
  src: url('./SFProARDisplay-Regular.woff2') format('woff2'),
       url('./SFProARDisplay-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro AR Display';
  src: url('./SFProARDisplay-Medium.woff2') format('woff2'),
       url('./SFProARDisplay-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro AR Display';
  src: url('./SFProARDisplay-Semibold.woff2') format('woff2'),
       url('./SFProARDisplay-Semibold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro AR Display';
  src: url('./SFProARDisplay-Bold.woff2') format('woff2'),
       url('./SFProARDisplay-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SF Pro AR Display';
  src: url('./SFProARDisplay-Heavy.woff2') format('woff2'),
       url('./SFProARDisplay-Heavy.woff') format('woff');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* خطوط احتياطية عربية */

@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
  font-display: swap;
}

/* تحسينات للنصوص العربية */
.arabic-text {
  font-family: 'SF Pro AR Display', 'Cairo', 'Tajawal', 'Segoe UI', 'Tahoma', sans-serif;
  font-feature-settings: 'liga' 1, 'kern' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* أوزان الخطوط */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-heavy { font-weight: 800; }

/* أحجام الخطوط */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }

/* تحسينات للأرقام العربية */
.arabic-numbers {
  font-variant-numeric: tabular-nums;
  direction: ltr;
  unicode-bidi: embed;
  font-family: 'SF Pro AR Display', monospace;
}

/* تحسينات للطباعة */
@media print {
  * {
    font-family: 'SF Pro AR Display', 'Cairo', 'Times New Roman', serif !important;
  }
  
  .arabic-text {
    font-size: 12pt;
    line-height: 1.4;
  }
}
