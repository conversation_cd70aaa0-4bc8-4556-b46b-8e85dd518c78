const jwt = require('jsonwebtoken');
const config = require('config');
const User = require('../models/User');
const { UnauthorizedError, ForbiddenError } = require('../utils/errors');

/**
 * استخراج التوكن من رأس الطلب
 * @param {Object} req - كائن الطلب
 * @returns {string|null} توكن JWT أو null إذا لم يتم العثور عليه
 */
const extractTokenFromHeader = (req) => {
  // محاولة الحصول على التوكن من رأس x-auth-token أولاً (للتوافق مع الإصدارات السابقة)
  let token = req.header('x-auth-token');
  
  // إذا لم يتم العثور على التوكن، حاول الحصول عليه من رأس Authorization
  if (!token) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    }
  }
  
  return token || null;
};

/**
 * وسيط للتحقق من المصادقة
 * يتحقق من وجود توكن JWT صالح في رأس الطلب
 * @param {Object} req - كائن الطلب
 * @param {Object} res - كائن الاستجابة
 * @param {Function} next - دالة الانتقال إلى الوسيط التالي
 */
const authenticate = async (req, res, next) => {
  try {
    // استخراج التوكن من رأس الطلب
    const token = extractTokenFromHeader(req);
    
    if (!token) {
      return next(new UnauthorizedError('الوصول غير مصرح به. يرجى تسجيل الدخول'));
    }
    
    // التحقق من صحة التوكن
    const decoded = jwt.verify(token, config.get('jwtSecret'));
    
    // الحصول على معرف المستخدم من التوكن
    const userId = decoded.user ? decoded.user.id : decoded.id;
    
    // البحث عن المستخدم في قاعدة البيانات
    const user = await User.findById(userId).select('-password');
    
    if (!user) {
      return next(new UnauthorizedError('المستخدم غير موجود'));
    }
    
    // التحقق من أن المستخدم نشط
    if (!user.isActive && !user.active) { // التحقق من كلا الحقلين للتوافق
      return next(new UnauthorizedError('تم تعطيل حساب المستخدم'));
    }
    
    // إضافة معلومات المستخدم إلى كائن الطلب
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return next(new UnauthorizedError('الرمز غير صالح أو منتهي الصلاحية'));
    }
    next(error);
  }
};

/**
 * وسيط للتحقق من الأدوار
 * يتحقق مما إذا كان المستخدم لديه أحد الأدوار المطلوبة
 * @param {...string} roles - الأدوار المطلوبة
 * @returns {Function} وسيط Express
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new UnauthorizedError('الوصول غير مصرح به. يرجى تسجيل الدخول'));
    }
    
    if (!roles.includes(req.user.role)) {
      return next(new ForbiddenError('ليس لديك صلاحية للوصول إلى هذا المورد'));
    }
    
    next();
  };
};

/**
 * وسيط للتحقق من ملكية المورد
 * يتحقق مما إذا كان المستخدم هو مالك المورد أو لديه دور مسموح به
 * @param {Function} getResourceUserId - دالة للحصول على معرف مستخدم المورد
 * @param {Array} allowedRoles - الأدوار المسموح لها بتجاوز التحقق من الملكية (اختياري)
 * @returns {Function} وسيط Express
 */
const checkOwnership = (getResourceUserId, allowedRoles = ['admin']) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return next(new UnauthorizedError('الوصول غير مصرح به. يرجى تسجيل الدخول'));
      }
      
      // السماح للمستخدمين ذوي الأدوار المسموح بها بتجاوز التحقق من الملكية
      if (allowedRoles.includes(req.user.role)) {
        return next();
      }
      
      // الحصول على معرف مستخدم المورد
      const resourceUserId = await getResourceUserId(req);
      
      // التحقق من الملكية
      if (resourceUserId && resourceUserId.toString() !== req.user._id.toString()) {
        return next(new ForbiddenError('ليس لديك صلاحية للوصول إلى هذا المورد'));
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * وسيط للتحقق من وجود توكن اختياري
 * يحاول استخراج معلومات المستخدم من التوكن إذا كان موجودًا، ولكن لا يرفض الطلب إذا لم يكن هناك توكن
 * @param {Object} req - كائن الطلب
 * @param {Object} res - كائن الاستجابة
 * @param {Function} next - دالة الانتقال إلى الوسيط التالي
 */
const optionalAuth = async (req, res, next) => {
  try {
    const token = extractTokenFromHeader(req);
    
    if (!token) {
      return next();
    }
    
    try {
      const decoded = jwt.verify(token, config.get('jwtSecret'));
      const userId = decoded.user ? decoded.user.id : decoded.id;
      const user = await User.findById(userId).select('-password');
      
      if (user && (user.isActive || user.active)) {
        req.user = user;
      }
    } catch (error) {
      // تجاهل أخطاء التوكن في المصادقة الاختيارية
    }
    
    next();
  } catch (error) {
    next(error);
  }
};

// للتوافق مع الإصدارات السابقة
const authMiddleware = authenticate;

module.exports = {
  authenticate,
  authorize,
  checkOwnership,
  optionalAuth,
  extractTokenFromHeader,
  // تصدير الوسيط كدالة افتراضية للتوافق مع الإصدارات السابقة
  default: authMiddleware
};

// للتوافق مع require('../middleware/auth')
module.exports = Object.assign(authMiddleware, module.exports);