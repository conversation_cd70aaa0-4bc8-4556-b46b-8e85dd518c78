const mongoose = require('mongoose');

/**
 * نموذج المورد
 * يستخدم لتخزين بيانات موردي المواد الخام والمستلزمات
 */
const SupplierSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  contactPerson: {
    type: String
  },
  email: {
    type: String,
    required: true
  },
  phone: {
    type: String,
    required: true
  },
  address: {
    street: { type: String },
    city: { type: String },
    state: { type: String },
    postalCode: { type: String },
    country: { type: String, default: 'المملكة العربية السعودية' }
  },
  taxNumber: {
    type: String
  },
  supplierType: {
    type: String,
    enum: ['paper', 'ink', 'equipment', 'binding', 'general', 'other'],
    default: 'general'
  },
  website: {
    type: String
  },
  paymentTerms: {
    type: String
  },
  notes: {
    type: String
  },
  items: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'inventory'
  }],
  active: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// تحديث تاريخ التعديل عند تحديث البيانات
SupplierSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// إضافة طريقة للحصول على ملخص المورد
SupplierSchema.methods.getSummary = function() {
  return {
    id: this._id,
    name: this.name,
    email: this.email,
    phone: this.phone,
    type: this.supplierType
  };
};

// إضافة طريقة للحصول على عنوان المورد كاملاً
SupplierSchema.methods.getFullAddress = function() {
  const { street, city, state, postalCode, country } = this.address;
  const addressParts = [street, city, state, postalCode, country].filter(Boolean);
  return addressParts.join('، ');
};

module.exports = mongoose.model('supplier', SupplierSchema);