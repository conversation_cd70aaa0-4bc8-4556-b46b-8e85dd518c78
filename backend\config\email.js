/**
 * ملف إعداد وتكوين البريد الإلكتروني
 * يقوم بإعداد وتكوين خدمة إرسال البريد الإلكتروني
 */

const nodemailer = require('nodemailer');
const config = require('config');
const path = require('path');
const fs = require('fs');
const handlebars = require('handlebars');
const logger = require('../utils/logger');

// الحصول على إعدادات البريد الإلكتروني من ملف التكوين
const emailConfig = config.get('email');

/**
 * إنشاء ناقل البريد الإلكتروني
 * @returns {Object} كائن ناقل البريد الإلكتروني
 */
const createTransporter = async () => {
  // إذا كانت خدمة البريد الإلكتروني معطلة، قم بإرجاع ناقل وهمي
  if (!emailConfig.enabled) {
    return {
      sendMail: (options) => {
        logger.info(`[البريد الإلكتروني معطل] محاولة إرسال بريد إلكتروني إلى: ${options.to}`);
        logger.debug('محتوى البريد الإلكتروني:', options);
        return Promise.resolve({ messageId: 'dummy-id' });
      }
    };
  }

  // إذا كنا في بيئة التطوير، قم بإنشاء حساب اختبار Ethereal
  if (process.env.NODE_ENV === 'development' && !emailConfig.auth.user) {
    try {
      const testAccount = await nodemailer.createTestAccount();
      logger.info('تم إنشاء حساب بريد إلكتروني اختباري:');
      logger.info(`- البريد الإلكتروني: ${testAccount.user}`);
      logger.info(`- كلمة المرور: ${testAccount.pass}`);
      logger.info(`- خادم SMTP: ${testAccount.smtp.host}`);
      logger.info(`- المنفذ: ${testAccount.smtp.port}`);

      return nodemailer.createTransport({
        host: testAccount.smtp.host,
        port: testAccount.smtp.port,
        secure: testAccount.smtp.secure,
        auth: {
          user: testAccount.user,
          pass: testAccount.pass,
        },
      });
    } catch (error) {
      logger.error('فشل إنشاء حساب بريد إلكتروني اختباري:', error);
    }
  }

  // إنشاء ناقل البريد الإلكتروني باستخدام الإعدادات من ملف التكوين
  return nodemailer.createTransport({
    host: emailConfig.host,
    port: emailConfig.port,
    secure: emailConfig.secure,
    auth: emailConfig.auth,
  });
};

/**
 * قراءة قالب البريد الإلكتروني
 * @param {string} templateName - اسم قالب البريد الإلكتروني
 * @returns {Function} دالة معالجة القالب
 */
const getEmailTemplate = (templateName) => {
  try {
    const templatePath = path.join(process.cwd(), 'templates', 'emails', `${templateName}.html`);
    const templateSource = fs.readFileSync(templatePath, 'utf-8');
    return handlebars.compile(templateSource);
  } catch (error) {
    logger.error(`فشل قراءة قالب البريد الإلكتروني ${templateName}:`, error);
    // إرجاع قالب بسيط في حالة عدم وجود القالب المطلوب
    return handlebars.compile(`
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #777; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>{{title}}</h1>
            </div>
            <div class="content">
              {{#each paragraphs}}
                <p>{{{this}}}</p>
              {{/each}}
            </div>
            <div class="footer">
              <p>© {{year}} {{companyName}}. جميع الحقوق محفوظة.</p>
            </div>
          </div>
        </body>
      </html>
    `);
  }
};

/**
 * إرسال بريد إلكتروني
 * @param {Object} options - خيارات البريد الإلكتروني
 * @param {string} options.to - عنوان البريد الإلكتروني للمستلم
 * @param {string} options.subject - موضوع البريد الإلكتروني
 * @param {string} options.template - اسم قالب البريد الإلكتروني
 * @param {Object} options.data - بيانات القالب
 * @param {Array} options.attachments - مرفقات البريد الإلكتروني (اختياري)
 * @returns {Promise} وعد يتم حله عند إرسال البريد الإلكتروني بنجاح
 */
const sendEmail = async (options) => {
  try {
    const transporter = await createTransporter();
    
    // إعداد بيانات القالب الافتراضية
    const defaultData = {
      companyName: config.get('app.name'),
      year: new Date().getFullYear(),
      baseUrl: config.get('app.baseUrl'),
      frontendUrl: config.get('app.frontendUrl'),
      ...options.data,
    };
    
    // تحضير محتوى البريد الإلكتروني
    let html;
    if (options.template) {
      const template = getEmailTemplate(options.template);
      html = template(defaultData);
    } else if (options.html) {
      html = options.html;
    } else if (options.text) {
      html = `<pre>${options.text}</pre>`;
    } else {
      throw new Error('يجب توفير قالب أو محتوى HTML أو نص للبريد الإلكتروني');
    }
    
    // إعداد خيارات البريد الإلكتروني
    const mailOptions = {
      from: options.from || emailConfig.from,
      to: options.to,
      cc: options.cc,
      bcc: options.bcc,
      subject: options.subject || emailConfig.defaultSubject,
      html,
      attachments: options.attachments || [],
    };
    
    // إرسال البريد الإلكتروني
    const info = await transporter.sendMail(mailOptions);
    
    logger.info(`تم إرسال البريد الإلكتروني إلى ${options.to} بنجاح. معرف الرسالة: ${info.messageId}`);
    
    // إذا كنا نستخدم Ethereal، قم بعرض رابط معاينة البريد الإلكتروني
    if (process.env.NODE_ENV === 'development' && nodemailer.getTestMessageUrl(info)) {
      logger.info(`رابط معاينة البريد الإلكتروني: ${nodemailer.getTestMessageUrl(info)}`);
    }
    
    return info;
  } catch (error) {
    logger.error(`فشل إرسال البريد الإلكتروني إلى ${options.to}:`, error);
    throw error;
  }
};

/**
 * إنشاء مجلدات القوالب إذا لم تكن موجودة
 */
const setupTemplateDirectories = () => {
  const templatesDir = path.join(process.cwd(), 'templates');
  const emailsDir = path.join(templatesDir, 'emails');
  
  if (!fs.existsSync(templatesDir)) {
    fs.mkdirSync(templatesDir, { recursive: true });
    logger.info('تم إنشاء مجلد القوالب');
  }
  
  if (!fs.existsSync(emailsDir)) {
    fs.mkdirSync(emailsDir, { recursive: true });
    logger.info('تم إنشاء مجلد قوالب البريد الإلكتروني');
  }
};

// إعداد مجلدات القوالب عند تحميل الوحدة
setupTemplateDirectories();

module.exports = {
  sendEmail,
  getEmailTemplate,
  createTransporter,
};