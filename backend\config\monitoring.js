/**
 * ملف إعداد وتكوين المراقبة
 * يقوم بإعداد وتكوين أدوات المراقبة والتتبع للتطبيق
 */

const express = require('express');
const promClient = require('prom-client');
const responseTime = require('response-time');
const config = require('config');
const logger = require('../utils/logger');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

// إنشاء سجل Prometheus
const register = new promClient.Registry();

// إضافة المقاييس الافتراضية
promClient.collectDefaultMetrics({ register });

// إنشاء مقاييس مخصصة
const httpRequestDurationMicroseconds = new promClient.Histogram({
  name: 'http_request_duration_ms',
  help: 'Duration of HTTP requests in ms',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [1, 5, 15, 50, 100, 200, 300, 400, 500, 1000, 2000, 5000, 10000],
});

const httpRequestCounter = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
});

const errorCounter = new promClient.Counter({
  name: 'errors_total',
  help: 'Total number of errors',
  labelNames: ['type', 'message'],
});

const databaseOperationDurationMicroseconds = new promClient.Histogram({
  name: 'database_operation_duration_ms',
  help: 'Duration of database operations in ms',
  labelNames: ['operation', 'collection'],
  buckets: [1, 5, 15, 50, 100, 200, 300, 400, 500, 1000, 2000, 5000, 10000],
});

const activeConnections = new promClient.Gauge({
  name: 'active_connections',
  help: 'Number of active connections',
});

const memoryUsage = new promClient.Gauge({
  name: 'memory_usage_bytes',
  help: 'Memory usage in bytes',
  labelNames: ['type'],
});

// تسجيل المقاييس المخصصة
register.registerMetric(httpRequestDurationMicroseconds);
register.registerMetric(httpRequestCounter);
register.registerMetric(errorCounter);
register.registerMetric(databaseOperationDurationMicroseconds);
register.registerMetric(activeConnections);
register.registerMetric(memoryUsage);

/**
 * تحديث مقاييس استخدام الذاكرة
 */
const updateMemoryUsage = () => {
  const usage = process.memoryUsage();
  memoryUsage.set({ type: 'rss' }, usage.rss);
  memoryUsage.set({ type: 'heapTotal' }, usage.heapTotal);
  memoryUsage.set({ type: 'heapUsed' }, usage.heapUsed);
  memoryUsage.set({ type: 'external' }, usage.external);
};

// تحديث مقاييس استخدام الذاكرة كل دقيقة
setInterval(updateMemoryUsage, 60000);

// تحديث مقاييس استخدام الذاكرة عند بدء التشغيل
updateMemoryUsage();

/**
 * إنشاء وسيط لقياس وقت الاستجابة
 */
const responseTimeMiddleware = responseTime((req, res, time) => {
  const route = req.route ? req.route.path : req.path;
  const method = req.method;
  const statusCode = res.statusCode;
  
  httpRequestDurationMicroseconds.observe(
    { method, route, status_code: statusCode },
    time
  );
  
  httpRequestCounter.inc({
    method,
    route,
    status_code: statusCode,
  });
});

/**
 * إنشاء وسيط لتتبع الاتصالات النشطة
 */
const connectionTrackingMiddleware = (req, res, next) => {
  activeConnections.inc();
  
  res.on('finish', () => {
    activeConnections.dec();
  });
  
  next();
};

/**
 * تسجيل خطأ في مقاييس الأخطاء
 * @param {Error} error - الخطأ المراد تسجيله
 */
const recordError = (error) => {
  const errorType = error.name || 'UnknownError';
  const errorMessage = error.message || 'Unknown error';
  
  errorCounter.inc({
    type: errorType,
    message: errorMessage,
  });
};

/**
 * تسجيل عملية قاعدة البيانات
 * @param {string} operation - نوع العملية (find, insert, update, delete, etc.)
 * @param {string} collection - اسم المجموعة
 * @param {number} duration - مدة العملية بالمللي ثانية
 */
const recordDatabaseOperation = (operation, collection, duration) => {
  databaseOperationDurationMicroseconds.observe(
    { operation, collection },
    duration
  );
};

/**
 * إعداد مسار المقاييس
 * @param {Object} app - تطبيق Express
 */
const setupMetricsRoute = (app) => {
  // إنشاء مسار المقاييس
  const metricsPath = '/metrics';
  
  // إنشاء موجه منفصل للمقاييس
  const metricsRouter = express.Router();
  
  // إضافة وسيط المصادقة للمقاييس إذا كان في بيئة الإنتاج
  if (process.env.NODE_ENV === 'production') {
    metricsRouter.use((req, res, next) => {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Basic ')) {
        res.set('WWW-Authenticate', 'Basic realm="Metrics"');
        return res.status(401).send('Authentication required');
      }
      
      const credentials = Buffer.from(authHeader.split(' ')[1], 'base64').toString();
      const [username, password] = credentials.split(':');
      
      // التحقق من صحة بيانات الاعتماد (يجب تكوين هذه القيم في ملف التكوين)
      if (username !== 'metrics' || password !== 'metrics_password') {
        return res.status(403).send('Invalid credentials');
      }
      
      next();
    });
  }
  
  // إضافة مسار المقاييس
  metricsRouter.get('/', async (req, res) => {
    try {
      res.set('Content-Type', register.contentType);
      res.end(await register.metrics());
    } catch (error) {
      logger.error(`فشل في استرجاع المقاييس: ${error.message}`);
      res.status(500).send('فشل في استرجاع المقاييس');
    }
  });
  
  // إضافة موجه المقاييس إلى التطبيق
  app.use(metricsPath, metricsRouter);
  
  logger.info(`مسار المقاييس متاح على: ${appConfig.baseUrl || `http://localhost:${appConfig.port || 3000}`}${metricsPath}`);
};

/**
 * إعداد مسار الحالة
 * @param {Object} app - تطبيق Express
 */
const setupHealthRoute = (app) => {
  // إنشاء مسار الحالة
  const healthPath = '/health';
  
  // إضافة مسار الحالة
  app.get(healthPath, (req, res) => {
    const healthcheck = {
      status: 'UP',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      service: appConfig.name,
      version: appConfig.version,
      environment: process.env.NODE_ENV || 'development',
    };
    
    try {
      res.status(200).json(healthcheck);
    } catch (error) {
      logger.error(`فشل في استرجاع حالة الصحة: ${error.message}`);
      healthcheck.status = 'DOWN';
      healthcheck.error = error.message;
      res.status(503).json(healthcheck);
    }
  });
  
  logger.info(`مسار الحالة متاح على: ${appConfig.baseUrl || `http://localhost:${appConfig.port || 3000}`}${healthPath}`);
};

/**
 * إعداد المراقبة في التطبيق
 * @param {Object} app - تطبيق Express
 */
const setupMonitoring = (app) => {
  // إضافة وسائط المراقبة
  app.use(responseTimeMiddleware);
  app.use(connectionTrackingMiddleware);
  
  // إعداد مسارات المراقبة
  setupMetricsRoute(app);
  setupHealthRoute(app);
  
  // إعداد معالج الأخطاء العام
  app.use((err, req, res, next) => {
    recordError(err);
    next(err);
  });
  
  // تسجيل معلومات عن إعداد المراقبة
  logger.info('تم إعداد المراقبة بنجاح');
};

module.exports = {
  setupMonitoring,
  recordError,
  recordDatabaseOperation,
  register,
  httpRequestDurationMicroseconds,
  httpRequestCounter,
  errorCounter,
  databaseOperationDurationMicroseconds,
  activeConnections,
  memoryUsage,
};