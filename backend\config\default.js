/**
 * ملف الإعدادات الافتراضية للتطبيق
 * يحتوي على جميع إعدادات التطبيق التي يمكن تخصيصها
 */

module.exports = {
  // إعدادات التطبيق الأساسية
  app: {
    name: 'نظام المطبعة المتطور',
    version: '1.0.0',
    description: 'نظام متكامل لإدارة المطابع',
    baseUrl: process.env.BASE_URL || 'http://localhost:5000',
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
    port: process.env.PORT || 5000,
    env: process.env.NODE_ENV || 'development',
    apiPrefix: '/api',
    apiVersion: 'v1',
    uploadDir: process.env.UPLOAD_DIR || 'uploads',
    tempDir: process.env.TEMP_DIR || 'uploads/temp',
    maxUploadSize: process.env.MAX_UPLOAD_SIZE || 10 * 1024 * 1024, // 10MB
    defaultLanguage: 'ar',
    supportedLanguages: ['ar', 'en'],
    defaultTimezone: 'Asia/Riyadh',
    defaultCurrency: 'SAR',
    defaultPageSize: 10,
    maxPageSize: 100,
  },

  // إعدادات قاعدة البيانات
  db: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/print-system',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      autoIndex: process.env.NODE_ENV !== 'production',
    },
  },

  // إعدادات المصادقة والأمان
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'print-system-secret-key-change-in-production',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1d',
    refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET || 'print-system-refresh-token-secret-key',
    refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
    resetPasswordTokenExpiresIn: process.env.RESET_PASSWORD_TOKEN_EXPIRES_IN || '1h',
    verificationTokenExpiresIn: process.env.VERIFICATION_TOKEN_EXPIRES_IN || '24h',
    saltRounds: 10,
    accessTokenCookieName: 'access_token',
    refreshTokenCookieName: 'refresh_token',
    cookieSecure: process.env.NODE_ENV === 'production',
    cookieHttpOnly: true,
    cookieSameSite: 'lax',
    cookieMaxAge: 24 * 60 * 60 * 1000, // 1 day
    passwordMinLength: 8,
    passwordMaxLength: 128,
    passwordRequireUppercase: true,
    passwordRequireLowercase: true,
    passwordRequireNumbers: true,
    passwordRequireSymbols: true,
  },

  // إعدادات البريد الإلكتروني
  email: {
    enabled: process.env.EMAIL_ENABLED === 'true',
    from: process.env.EMAIL_FROM || 'نظام المطبعة المتطور <<EMAIL>>',
    host: process.env.EMAIL_HOST || 'smtp.example.com',
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER || '<EMAIL>',
      pass: process.env.EMAIL_PASS || 'password',
    },
    defaultSubject: 'رسالة من نظام المطبعة المتطور',
    templates: {
      welcome: 'welcome',
      resetPassword: 'reset-password',
      verifyEmail: 'verify-email',
      orderConfirmation: 'order-confirmation',
      orderStatusUpdate: 'order-status-update',
      invoice: 'invoice',
      lowStockAlert: 'low-stock-alert',
    },
  },

  // إعدادات التسجيل والمراقبة
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
    dir: process.env.LOG_DIR || 'logs',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d',
    colorize: process.env.LOG_COLORIZE !== 'false',
    timestamp: true,
    handleExceptions: true,
    handleRejections: true,
    exitOnError: false,
    silent: process.env.NODE_ENV === 'test',
  },

  // إعدادات CORS
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token'],
    exposedHeaders: ['Content-Range', 'X-Total-Count'],
    credentials: true,
    maxAge: 86400, // 1 day
  },

  // إعدادات الضرائب والحسابات
  billing: {
    taxRate: process.env.TAX_RATE || 0.15, // 15% ضريبة القيمة المضافة
    taxIncluded: process.env.TAX_INCLUDED === 'true',
    currency: process.env.CURRENCY || 'SAR',
    currencySymbol: process.env.CURRENCY_SYMBOL || 'ر.س',
    decimalPlaces: 2,
    invoicePrefix: process.env.INVOICE_PREFIX || 'INV',
    orderPrefix: process.env.ORDER_PREFIX || 'ORD',
    quotationPrefix: process.env.QUOTATION_PREFIX || 'QUO',
    paymentTerms: process.env.PAYMENT_TERMS || 30, // 30 يوم
    lateFeePercentage: process.env.LATE_FEE_PERCENTAGE || 0.02, // 2% رسوم تأخير
  },

  // إعدادات المخزون
  inventory: {
    lowStockThreshold: process.env.LOW_STOCK_THRESHOLD || 10,
    criticalStockThreshold: process.env.CRITICAL_STOCK_THRESHOLD || 5,
    enableLowStockAlerts: process.env.ENABLE_LOW_STOCK_ALERTS !== 'false',
    autoDeductStock: process.env.AUTO_DEDUCT_STOCK !== 'false',
    allowNegativeStock: process.env.ALLOW_NEGATIVE_STOCK === 'true',
    stockAuditFrequency: process.env.STOCK_AUDIT_FREQUENCY || 30, // بالأيام
  },

  // إعدادات الطلبات
  orders: {
    statuses: [
      'pending', // قيد الانتظار
      'confirmed', // مؤكد
      'processing', // قيد المعالجة
      'ready', // جاهز
      'completed', // مكتمل
      'cancelled', // ملغي
      'on-hold', // معلق
      'refunded', // مسترجع
    ],
    defaultStatus: 'pending',
    allowPartialPayment: process.env.ALLOW_PARTIAL_PAYMENT !== 'false',
    requireApprovalAboveAmount: process.env.REQUIRE_APPROVAL_ABOVE_AMOUNT || 5000,
    autoConfirmBelowAmount: process.env.AUTO_CONFIRM_BELOW_AMOUNT || 1000,
    sendOrderConfirmationEmail: process.env.SEND_ORDER_CONFIRMATION_EMAIL !== 'false',
    sendOrderStatusUpdateEmail: process.env.SEND_ORDER_STATUS_UPDATE_EMAIL !== 'false',
  },

  // إعدادات المدفوعات
  payments: {
    methods: [
      'cash', // نقدي
      'credit_card', // بطاقة ائتمان
      'bank_transfer', // تحويل بنكي
      'check', // شيك
      'online', // دفع إلكتروني
    ],
    defaultMethod: 'cash',
    statuses: [
      'pending', // قيد الانتظار
      'completed', // مكتمل
      'failed', // فاشل
      'refunded', // مسترجع
      'partially_refunded', // مسترجع جزئيًا
    ],
    defaultStatus: 'pending',
  },

  // إعدادات المستخدمين
  users: {
    roles: [
      'admin', // مدير النظام
      'manager', // مدير
      'accountant', // محاسب
      'sales', // مبيعات
      'production', // إنتاج
      'inventory', // مخزون
      'customer_service', // خدمة العملاء
      'viewer', // مشاهد فقط
    ],
    defaultRole: 'viewer',
    defaultAvatar: '/assets/images/default-avatar.png',
    passwordResetEnabled: true,
    emailVerificationRequired: process.env.EMAIL_VERIFICATION_REQUIRED === 'true',
    maxLoginAttempts: process.env.MAX_LOGIN_ATTEMPTS || 5,
    lockoutDuration: process.env.LOCKOUT_DURATION || 30 * 60 * 1000, // 30 دقيقة
  },

  // إعدادات العملاء
  customers: {
    types: [
      'individual', // فرد
      'company', // شركة
      'government', // حكومي
      'non_profit', // غير ربحي
    ],
    defaultType: 'individual',
    defaultCreditLimit: process.env.DEFAULT_CREDIT_LIMIT || 5000,
    requireVerification: process.env.REQUIRE_CUSTOMER_VERIFICATION === 'true',
  },

  // إعدادات التقارير
  reports: {
    cacheDuration: process.env.REPORT_CACHE_DURATION || 3600, // 1 ساعة بالثواني
    defaultDateRange: 'month', // day, week, month, quarter, year
    exportFormats: ['pdf', 'excel', 'csv'],
    defaultFormat: 'pdf',
  },

  // إعدادات الإشعارات
  notifications: {
    enabled: process.env.NOTIFICATIONS_ENABLED !== 'false',
    email: process.env.EMAIL_NOTIFICATIONS !== 'false',
    sms: process.env.SMS_NOTIFICATIONS === 'true',
    push: process.env.PUSH_NOTIFICATIONS === 'true',
    inApp: process.env.IN_APP_NOTIFICATIONS !== 'false',
  },

  // إعدادات الأمان
  security: {
    rateLimiter: {
      enabled: process.env.RATE_LIMITER_ENABLED !== 'false',
      windowMs: process.env.RATE_LIMITER_WINDOW_MS || 15 * 60 * 1000, // 15 دقيقة
      max: process.env.RATE_LIMITER_MAX || 100, // الحد الأقصى للطلبات لكل IP
      message: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقًا',
    },
    helmet: {
      enabled: process.env.HELMET_ENABLED !== 'false',
    },
    xss: {
      enabled: process.env.XSS_PROTECTION_ENABLED !== 'false',
    },
    csrf: {
      enabled: process.env.CSRF_PROTECTION_ENABLED === 'true',
      cookieName: 'csrf-token',
      headerName: 'X-CSRF-Token',
    },
  },

  // إعدادات الواجهة الأمامية
  frontend: {
    title: 'نظام المطبعة المتطور',
    description: 'نظام متكامل لإدارة المطابع',
    logo: '/assets/images/logo.png',
    favicon: '/assets/images/favicon.ico',
    themeColor: '#1976d2',
    defaultLocale: 'ar',
    supportedLocales: ['ar', 'en'],
    defaultTheme: 'light', // light, dark, system
    defaultDirection: 'rtl', // rtl, ltr
  },
};