const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const invoiceController = require('../controllers/invoiceController');
const auth = require('../middleware/auth');
const checkPermission = require('../middleware/checkPermission');

/**
 * @route   POST api/invoices
 * @desc    إنشاء فاتورة جديدة
 * @access  Private
 */
router.post(
  '/',
  [
    auth,
    checkPermission('invoices', 'create'),
    [
      check('customer', 'معرف العميل مطلوب').not().isEmpty(),
      check('order', 'معرف الطلب مطلوب').not().isEmpty(),
      check('items', 'يجب إضافة عنصر واحد على الأقل').isArray({ min: 1 }),
      check('items.*.description', 'وصف العنصر مطلوب').not().isEmpty(),
      check('items.*.quantity', 'الكمية مطلوبة').isNumeric(),
      check('items.*.unitPrice', 'سعر الوحدة مطلوب').isNumeric(),
      check('items.*.totalPrice', 'السعر الإجمالي مطلوب').isNumeric(),
      check('taxRate', 'معدل الضريبة مطلوب').isNumeric(),
      check('paymentTerms', 'شروط الدفع مطلوبة').not().isEmpty()
    ]
  ],
  invoiceController.createInvoice
);

/**
 * @route   GET api/invoices
 * @desc    الحصول على قائمة الفواتير
 * @access  Private
 */
router.get(
  '/',
  [auth, checkPermission('invoices', 'read')],
  invoiceController.getInvoices
);

/**
 * @route   GET api/invoices/:id
 * @desc    الحصول على فاتورة محددة
 * @access  Private
 */
router.get(
  '/:id',
  [auth, checkPermission('invoices', 'read')],
  invoiceController.getInvoiceById
);

/**
 * @route   PUT api/invoices/:id
 * @desc    تحديث فاتورة
 * @access  Private
 */
router.put(
  '/:id',
  [
    auth,
    checkPermission('invoices', 'update'),
    [
      check('customer', 'معرف العميل مطلوب').optional().not().isEmpty(),
      check('order', 'معرف الطلب مطلوب').optional().not().isEmpty(),
      check('items', 'يجب إضافة عنصر واحد على الأقل').optional().isArray({ min: 1 }),
      check('items.*.description', 'وصف العنصر مطلوب').optional().not().isEmpty(),
      check('items.*.quantity', 'الكمية مطلوبة').optional().isNumeric(),
      check('items.*.unitPrice', 'سعر الوحدة مطلوب').optional().isNumeric(),
      check('items.*.totalPrice', 'السعر الإجمالي مطلوب').optional().isNumeric(),
      check('taxRate', 'معدل الضريبة مطلوب').optional().isNumeric(),
      check('paymentTerms', 'شروط الدفع مطلوبة').optional().not().isEmpty()
    ]
  ],
  invoiceController.updateInvoice
);

/**
 * @route   POST api/invoices/payment/:id
 * @desc    إضافة دفعة لفاتورة
 * @access  Private
 */
router.post(
  '/payment/:id',
  [
    auth,
    checkPermission('invoices', 'update'),
    [
      check('amount', 'مبلغ الدفعة مطلوب').isNumeric().withMessage('المبلغ يجب أن يكون رقمًا').custom(value => value > 0).withMessage('المبلغ يجب أن يكون أكبر من صفر'),
      check('paymentMethod', 'طريقة الدفع مطلوبة').not().isEmpty(),
      check('paymentDate', 'تاريخ الدفع مطلوب').isISO8601().toDate()
    ]
  ],
  invoiceController.addPayment
);

/**
 * @route   DELETE api/invoices/:id
 * @desc    حذف فاتورة
 * @access  Private
 */
router.delete(
  '/:id',
  [auth, checkPermission('invoices', 'delete')],
  invoiceController.deleteInvoice
);

/**
 * @route   GET api/invoices/dashboard/stats
 * @desc    الحصول على إحصائيات الفواتير للوحة المعلومات
 * @access  Private
 */
router.get(
  '/dashboard/stats',
  [auth, checkPermission('invoices', 'read')],
  invoiceController.getInvoiceStats
);

module.exports = router;