{"name": "print-management-system", "version": "1.0.0", "description": "نظام إدارة مطبعة متكامل", "main": "index.js", "scripts": {"start": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run server", "client": "cd frontend && npm start", "dev": "concurrently \"npm run server\" \"npm run client\"", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "prod": "cd backend && npm start"}, "keywords": ["مطبعة", "إدارة", "طباعة", "نظام"], "author": "", "license": "MIT", "dependencies": {"concurrently": "^8.2.0", "redis": "^5.5.6"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}