{"name": "print-management-system-new", "version": "2.0.0", "description": "نظام إدارة مطبعة متطور - إصد<PERSON><PERSON> جديد ومحسن", "main": "index.js", "scripts": {"start": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm start", "dev": "concurrently \"npm run server\" \"npm run client\"", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "prod": "cd backend && npm start", "setup": "npm run install-all && npm run dev"}, "keywords": ["مطبعة", "إدارة", "طباعة", "نظام", "عربي", "react", "nodejs", "mongodb"], "author": "Print Management System", "license": "MIT", "dependencies": {"concurrently": "^8.2.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/print-management/system.git"}}