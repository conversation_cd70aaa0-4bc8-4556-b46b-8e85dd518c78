/**
 * ملف إعداد وتكوين قاعدة البيانات
 * يقوم بإعداد الاتصال بقاعدة البيانات وتكوين الإعدادات الخاصة بها
 */

const mongoose = require('mongoose');
const config = require('config');
const { logger } = require('./logging');

// الحصول على إعدادات قاعدة البيانات من ملف التكوين
let dbConfig;
try {
  dbConfig = config.get('database');
} catch (error) {
  // استخدام الإعدادات الافتراضية إذا لم يتم العثور على إعدادات في ملف التكوين
  dbConfig = {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/print_system',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      autoIndex: process.env.NODE_ENV !== 'production',
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      maxPoolSize: 10
    },
    debug: process.env.NODE_ENV !== 'production',
    seedData: process.env.NODE_ENV !== 'production'
  };
}

/**
 * إنشاء اتصال بقاعدة البيانات
 * @returns {Promise} وعد يتم حله عند الاتصال بنجاح
 */
const connectDB = async () => {
  try {
    // تعيين خيارات التصحيح
    mongoose.set('debug', dbConfig.debug);
    
    // الاتصال بقاعدة البيانات
    const conn = await mongoose.connect(dbConfig.uri, dbConfig.options);
    
    logger.info(`تم الاتصال بقاعدة البيانات: ${conn.connection.host}`);
    
    // إعداد مستمعي الأحداث
    setupMongooseEventListeners();
    
    return conn;
  } catch (error) {
    logger.error(`خطأ في الاتصال بقاعدة البيانات: ${error.message}`, { error });
    process.exit(1);
  }
};

/**
 * إعداد مستمعي أحداث Mongoose
 */
const setupMongooseEventListeners = () => {
  const db = mongoose.connection;
  
  db.on('error', (err) => {
    logger.error(`خطأ في اتصال قاعدة البيانات: ${err.message}`, { error: err });
  });
  
  db.on('disconnected', () => {
    logger.warn('تم قطع الاتصال بقاعدة البيانات');
  });
  
  db.on('reconnected', () => {
    logger.info('تم إعادة الاتصال بقاعدة البيانات');
  });
  
  // التعامل مع إغلاق التطبيق
  process.on('SIGINT', async () => {
    await mongoose.connection.close();
    logger.info('تم إغلاق اتصال قاعدة البيانات بسبب إنهاء التطبيق');
    process.exit(0);
  });
};

/**
 * إنشاء نسخة احتياطية من قاعدة البيانات
 * @param {string} backupPath - مسار حفظ النسخة الاحتياطية
 * @returns {Promise} وعد يتم حله عند اكتمال النسخ الاحتياطي
 */
const backupDatabase = async (backupPath) => {
  try {
    const { spawn } = require('child_process');
    const path = require('path');
    const fs = require('fs');
    
    // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجودًا
    if (!fs.existsSync(backupPath)) {
      fs.mkdirSync(backupPath, { recursive: true });
    }
    
    // استخراج اسم قاعدة البيانات من URI
    const dbName = dbConfig.uri.split('/').pop().split('?')[0];
    
    // إنشاء اسم ملف النسخة الاحتياطية
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `${dbName}-${timestamp}.gz`;
    const backupFilePath = path.join(backupPath, backupFileName);
    
    // إنشاء أمر mongodump
    const mongodump = spawn('mongodump', [
      `--uri=${dbConfig.uri}`,
      `--archive=${backupFilePath}`,
      '--gzip'
    ]);
    
    return new Promise((resolve, reject) => {
      mongodump.on('close', (code) => {
        if (code === 0) {
          logger.info(`تم إنشاء نسخة احتياطية بنجاح: ${backupFilePath}`);
          resolve(backupFilePath);
        } else {
          const error = new Error(`فشل إنشاء نسخة احتياطية، رمز الخروج: ${code}`);
          logger.error(error.message);
          reject(error);
        }
      });
      
      mongodump.stderr.on('data', (data) => {
        logger.error(`خطأ في إنشاء نسخة احتياطية: ${data}`);
      });
    });
  } catch (error) {
    logger.error(`خطأ في إنشاء نسخة احتياطية: ${error.message}`, { error });
    throw error;
  }
};

/**
 * استعادة قاعدة البيانات من نسخة احتياطية
 * @param {string} backupFilePath - مسار ملف النسخة الاحتياطية
 * @returns {Promise} وعد يتم حله عند اكتمال الاستعادة
 */
const restoreDatabase = async (backupFilePath) => {
  try {
    const { spawn } = require('child_process');
    const fs = require('fs');
    
    // التحقق من وجود ملف النسخة الاحتياطية
    if (!fs.existsSync(backupFilePath)) {
      throw new Error(`ملف النسخة الاحتياطية غير موجود: ${backupFilePath}`);
    }
    
    // إنشاء أمر mongorestore
    const mongorestore = spawn('mongorestore', [
      `--uri=${dbConfig.uri}`,
      `--archive=${backupFilePath}`,
      '--gzip',
      '--drop'
    ]);
    
    return new Promise((resolve, reject) => {
      mongorestore.on('close', (code) => {
        if (code === 0) {
          logger.info(`تم استعادة قاعدة البيانات بنجاح من: ${backupFilePath}`);
          resolve(true);
        } else {
          const error = new Error(`فشل استعادة قاعدة البيانات، رمز الخروج: ${code}`);
          logger.error(error.message);
          reject(error);
        }
      });
      
      mongorestore.stderr.on('data', (data) => {
        logger.error(`خطأ في استعادة قاعدة البيانات: ${data}`);
      });
    });
  } catch (error) {
    logger.error(`خطأ في استعادة قاعدة البيانات: ${error.message}`, { error });
    throw error;
  }
};

/**
 * إنشاء وسيط للتحقق من حالة الاتصال بقاعدة البيانات
 * @returns {Function} وسيط Express
 */
const checkDatabaseConnection = () => {
  return (req, res, next) => {
    if (mongoose.connection.readyState !== 1) {
      return res.status(503).json({
        message: 'خدمة قاعدة البيانات غير متاحة حاليًا، يرجى المحاولة لاحقًا'
      });
    }
    next();
  };
};

/**
 * تنفيذ استعلام مع إعادة المحاولة في حالة الفشل
 * @param {Function} queryFn - دالة الاستعلام
 * @param {number} maxRetries - الحد الأقصى لعدد المحاولات
 * @param {number} retryDelay - التأخير بين المحاولات بالمللي ثانية
 * @returns {Promise} نتيجة الاستعلام
 */
const executeWithRetry = async (queryFn, maxRetries = 3, retryDelay = 1000) => {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await queryFn();
    } catch (error) {
      lastError = error;
      
      // التحقق مما إذا كان الخطأ متعلقًا بالاتصال
      const isConnectionError = [
        'MongoNetworkError',
        'MongoTimeoutError',
        'MongoServerSelectionError'
      ].includes(error.name);
      
      if (!isConnectionError || attempt === maxRetries) {
        throw error;
      }
      
      logger.warn(`فشل الاستعلام (المحاولة ${attempt}/${maxRetries})، إعادة المحاولة بعد ${retryDelay}ms...`, {
        error: error.message,
        attempt,
        maxRetries
      });
      
      // الانتظار قبل إعادة المحاولة
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      
      // زيادة وقت التأخير تدريجيًا
      retryDelay *= 2;
    }
  }
  
  throw lastError;
};

/**
 * إنشاء مؤشرات قاعدة البيانات
 */
const createIndexes = async () => {
  try {
    // سيتم استدعاء هذه الدالة من النماذج المختلفة لإنشاء المؤشرات الخاصة بها
    logger.info('تم إنشاء مؤشرات قاعدة البيانات بنجاح');
  } catch (error) {
    logger.error(`خطأ في إنشاء مؤشرات قاعدة البيانات: ${error.message}`, { error });
  }
};

/**
 * زرع البيانات الأولية في قاعدة البيانات
 */
const seedDatabase = async () => {
  try {
    if (dbConfig.seedData) {
      // سيتم استدعاء دوال زرع البيانات من هنا
      logger.info('تم زرع البيانات الأولية بنجاح');
    }
  } catch (error) {
    logger.error(`خطأ في زرع البيانات الأولية: ${error.message}`, { error });
  }
};

/**
 * إعداد قاعدة البيانات بالكامل
 */
const setupDatabase = async () => {
  await connectDB();
  await createIndexes();
  await seedDatabase();
  
  logger.info('تم إعداد قاعدة البيانات بنجاح');
};

module.exports = {
  connectDB,
  backupDatabase,
  restoreDatabase,
  checkDatabaseConnection,
  executeWithRetry,
  createIndexes,
  seedDatabase,
  setupDatabase
};