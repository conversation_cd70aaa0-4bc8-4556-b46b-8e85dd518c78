const User = require('../models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');

/**
 * @route   POST api/users
 * @desc    تسجيل مستخدم جديد
 * @access  Private/Admin
 */
exports.registerUser = async (req, res) => {
  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { name, email, password, role, phone } = req.body;

  try {
    // التحقق من وجود المستخدم
    let user = await User.findOne({ email });

    if (user) {
      return res.status(400).json({ msg: 'المستخدم موجود بالفعل' });
    }

    // إنشاء مستخدم جديد
    user = new User({
      name,
      email,
      password,
      role,
      phone
    });

    // تشفير كلمة المرور
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(password, salt);

    // حفظ المستخدم في قاعدة البيانات
    await user.save();

    // إنشاء رمز JWT
    const payload = {
      user: {
        id: user.id,
        role: user.role
      }
    };

    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '5 days' },
      (err, token) => {
        if (err) throw err;
        res.json({ token });
      }
    );
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/users
 * @desc    الحصول على قائمة المستخدمين
 * @access  Private/Admin
 */
exports.getUsers = async (req, res) => {
  try {
    const users = await User.find().select('-password');
    res.json(users);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/users/:id
 * @desc    الحصول على مستخدم محدد
 * @access  Private/Admin
 */
exports.getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }

    res.json(user);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   PUT api/users/:id
 * @desc    تحديث بيانات مستخدم
 * @access  Private/Admin
 */
exports.updateUser = async (req, res) => {
  const { name, email, role, phone, permissions, active } = req.body;

  // بناء كائن تحديث المستخدم
  const userFields = {};
  if (name) userFields.name = name;
  if (email) userFields.email = email;
  if (role) userFields.role = role;
  if (phone) userFields.phone = phone;
  if (permissions) userFields.permissions = permissions;
  if (active !== undefined) userFields.active = active;

  try {
    let user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }

    // تحديث المستخدم
    user = await User.findByIdAndUpdate(
      req.params.id,
      { $set: userFields },
      { new: true }
    ).select('-password');

    res.json(user);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   PUT api/users/password/:id
 * @desc    تغيير كلمة مرور المستخدم
 * @access  Private/Admin
 */
exports.changePassword = async (req, res) => {
  const { password } = req.body;

  try {
    let user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }

    // تشفير كلمة المرور الجديدة
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // تحديث كلمة المرور
    user = await User.findByIdAndUpdate(
      req.params.id,
      { $set: { password: hashedPassword } },
      { new: true }
    ).select('-password');

    res.json({ msg: 'تم تغيير كلمة المرور بنجاح' });
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   DELETE api/users/:id
 * @desc    حذف مستخدم
 * @access  Private/Admin
 */
exports.deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }

    // حذف المستخدم
    await User.findByIdAndRemove(req.params.id);

    res.json({ msg: 'تم حذف المستخدم' });
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/users/me
 * @desc    الحصول على بيانات المستخدم الحالي
 * @access  Private
 */
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');

    if (!user) {
      return res.status(404).json({ msg: 'المستخدم غير موجود' });
    }

    res.json(user);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};