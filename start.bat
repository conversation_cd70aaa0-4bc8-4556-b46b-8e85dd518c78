@echo off
echo ========================================
echo    نظام المطبعة المتطور - الإصدار 2.0
echo ========================================
echo.

echo جاري تشغيل النظام...
echo.

echo 1. تشغيل الواجهة الخلفية...
cd backend
start "Backend Server" cmd /k "npm run dev"
cd ..

echo 2. انتظار 5 ثوان...
timeout /t 5 /nobreak > nul

echo 3. تشغيل الواجهة الأمامية...
cd frontend
start "Frontend Server" cmd /k "npm start"
cd ..

echo.
echo تم تشغيل النظام بنجاح!
echo.
echo الواجهة الخلفية: http://localhost:5000
echo الواجهة الأمامية: http://localhost:3000
echo.
echo اضغط أي مفتاح للخروج...
pause > nul
