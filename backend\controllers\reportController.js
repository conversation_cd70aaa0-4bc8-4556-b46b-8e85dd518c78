const Order = require('../models/Order');
const Invoice = require('../models/Invoice');
const Customer = require('../models/Customer');
const Inventory = require('../models/Inventory');
const User = require('../models/User');

/**
 * @route   GET api/reports/sales
 * @desc    تقرير المبيعات
 * @access  Private
 */
exports.getSalesReport = async (req, res) => {
  try {
    // تحديد نطاق التاريخ
    const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date(new Date().setMonth(new Date().getMonth() - 1));
    const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
    
    // تجميع البيانات حسب الفترة الزمنية
    const groupBy = req.query.groupBy || 'day';
    let groupFormat;
    
    switch (groupBy) {
      case 'day':
        groupFormat = '%Y-%m-%d';
        break;
      case 'week':
        groupFormat = '%Y-%U';
        break;
      case 'month':
        groupFormat = '%Y-%m';
        break;
      case 'quarter':
        groupFormat = '%Y-%m';
        break;
      case 'year':
        groupFormat = '%Y';
        break;
      default:
        groupFormat = '%Y-%m-%d';
    }
    
    // الحصول على بيانات المبيعات
    const salesData = await Order.aggregate([
      {
        $match: {
          orderDate: { $gte: startDate, $lte: endDate },
          status: { $nin: ['cancelled'] }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: groupFormat, date: '$orderDate' } },
          totalSales: { $sum: '$totalAmount' },
          totalOrders: { $sum: 1 },
          averageOrderValue: { $avg: '$totalAmount' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);
    
    // الحصول على إجماليات المبيعات
    const salesSummary = await Order.aggregate([
      {
        $match: {
          orderDate: { $gte: startDate, $lte: endDate },
          status: { $nin: ['cancelled'] }
        }
      },
      {
        $group: {
          _id: null,
          totalSales: { $sum: '$totalAmount' },
          totalOrders: { $sum: 1 },
          averageOrderValue: { $avg: '$totalAmount' }
        }
      }
    ]);
    
    // المبيعات حسب نوع المنتج
    const salesByProductType = await Order.aggregate([
      {
        $match: {
          orderDate: { $gte: startDate, $lte: endDate },
          status: { $nin: ['cancelled'] }
        }
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.productType',
          totalSales: { $sum: '$items.totalPrice' },
          count: { $sum: 1 }
        }
      },
      { $sort: { 'totalSales': -1 } }
    ]);
    
    // أفضل العملاء مبيعًا
    const topCustomers = await Order.aggregate([
      {
        $match: {
          orderDate: { $gte: startDate, $lte: endDate },
          status: { $nin: ['cancelled'] }
        }
      },
      {
        $group: {
          _id: '$customer',
          totalSales: { $sum: '$totalAmount' },
          orderCount: { $sum: 1 }
        }
      },
      { $sort: { 'totalSales': -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'customers',
          localField: '_id',
          foreignField: '_id',
          as: 'customerInfo'
        }
      },
      {
        $project: {
          _id: 1,
          totalSales: 1,
          orderCount: 1,
          customerName: { $arrayElemAt: ['$customerInfo.name', 0] }
        }
      }
    ]);
    
    res.json({
      salesData,
      salesSummary: salesSummary.length > 0 ? salesSummary[0] : { totalSales: 0, totalOrders: 0, averageOrderValue: 0 },
      salesByProductType,
      topCustomers
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/reports/financial
 * @desc    التقرير المالي
 * @access  Private
 */
exports.getFinancialReport = async (req, res) => {
  try {
    // تحديد نطاق التاريخ
    const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date(new Date().setMonth(new Date().getMonth() - 1));
    const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
    
    // إجمالي الإيرادات (الفواتير المدفوعة)
    const revenue = await Invoice.aggregate([
      {
        $match: {
          issueDate: { $gte: startDate, $lte: endDate }
        }
      },
      { $unwind: { path: '$payments', preserveNullAndEmptyArrays: true } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m', date: '$payments.date' } },
          amount: { $sum: '$payments.amount' }
        }
      },
      { $match: { '_id': { $ne: null } } },
      { $sort: { '_id': 1 } }
    ]);
    
    // إجمالي المستحقات (الفواتير غير المدفوعة)
    const receivables = await Invoice.aggregate([
      {
        $match: {
          issueDate: { $gte: startDate, $lte: endDate },
          status: { $ne: 'paid' }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m', date: '$issueDate' } },
          amount: { $sum: { $subtract: ['$totalAmount', { $sum: { $map: { input: '$payments', as: 'payment', in: '$$payment.amount' } } }] } }
        }
      },
      { $sort: { '_id': 1 } }
    ]);
    
    // الفواتير المتأخرة
    const overdueInvoices = await Invoice.find({
      dueDate: { $lt: new Date() },
      status: { $ne: 'paid' }
    })
    .populate('customer', 'name phone email')
    .select('invoiceNumber customer issueDate dueDate totalAmount payments')
    .sort({ dueDate: 1 });
    
    // ملخص الحالة المالية
    const financialSummary = {
      totalRevenue: await Invoice.aggregate([
        { $unwind: { path: '$payments', preserveNullAndEmptyArrays: true } },
        { $group: { _id: null, total: { $sum: '$payments.amount' } } }
      ]).then(result => result.length > 0 ? result[0].total : 0),
      totalReceivables: await Invoice.aggregate([
        { $match: { status: { $ne: 'paid' } } },
        { $group: { _id: null, total: { $sum: { $subtract: ['$totalAmount', { $sum: { $map: { input: '$payments', as: 'payment', in: '$$payment.amount' } } }] } } } }
      ]).then(result => result.length > 0 ? result[0].total : 0),
      overdueAmount: await Invoice.aggregate([
        { $match: { dueDate: { $lt: new Date() }, status: { $ne: 'paid' } } },
        { $group: { _id: null, total: { $sum: { $subtract: ['$totalAmount', { $sum: { $map: { input: '$payments', as: 'payment', in: '$$payment.amount' } } }] } } } }
      ]).then(result => result.length > 0 ? result[0].total : 0)
    };
    
    res.json({
      revenue,
      receivables,
      overdueInvoices,
      financialSummary
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/reports/inventory
 * @desc    تقرير المخزون
 * @access  Private
 */
exports.getInventoryReport = async (req, res) => {
  try {
    // عناصر المخزون المنخفضة
    const lowStockItems = await Inventory.find({
      $expr: { $lte: ['$quantity', '$minimumQuantity'] }
    })
    .populate('supplier', 'name phone')
    .select('name category quantity minimumQuantity unit costPrice supplier')
    .sort({ quantity: 1 });
    
    // قيمة المخزون حسب الفئة
    const inventoryValueByCategory = await Inventory.aggregate([
      {
        $group: {
          _id: '$category',
          totalValue: { $sum: { $multiply: ['$quantity', '$costPrice'] } },
          itemCount: { $sum: 1 }
        }
      },
      { $sort: { 'totalValue': -1 } }
    ]);
    
    // حركة المخزون (المعاملات)
    const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date(new Date().setMonth(new Date().getMonth() - 1));
    const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
    
    const inventoryTransactions = await Inventory.aggregate([
      { $unwind: '$transactions' },
      {
        $match: {
          'transactions.date': { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$transactions.date' } },
            type: '$transactions.type'
          },
          count: { $sum: 1 },
          totalQuantity: { $sum: '$transactions.quantity' }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);
    
    // ملخص المخزون
    const inventorySummary = {
      totalItems: await Inventory.countDocuments(),
      totalValue: await Inventory.aggregate([
        { $group: { _id: null, total: { $sum: { $multiply: ['$quantity', '$costPrice'] } } } }
      ]).then(result => result.length > 0 ? result[0].total : 0),
      lowStockItemsCount: lowStockItems.length,
      outOfStockItemsCount: await Inventory.countDocuments({ quantity: 0 })
    };
    
    res.json({
      lowStockItems,
      inventoryValueByCategory,
      inventoryTransactions,
      inventorySummary
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/reports/production
 * @desc    تقرير الإنتاج
 * @access  Private
 */
exports.getProductionReport = async (req, res) => {
  try {
    // تحديد نطاق التاريخ
    const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date(new Date().setMonth(new Date().getMonth() - 1));
    const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
    
    // الطلبات حسب المرحلة الإنتاجية
    const ordersByStatus = await Order.aggregate([
      {
        $match: {
          orderDate: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      },
      { $sort: { 'count': -1 } }
    ]);
    
    // متوسط وقت الإنتاج للطلبات المكتملة
    const completedOrders = await Order.find({
      status: 'delivered',
      orderDate: { $gte: startDate, $lte: endDate }
    }).select('orderDate updatedAt');
    
    let averageProductionTime = 0;
    if (completedOrders.length > 0) {
      const totalProductionTime = completedOrders.reduce((sum, order) => {
        const productionTime = order.updatedAt.getTime() - order.orderDate.getTime();
        return sum + productionTime;
      }, 0);
      averageProductionTime = totalProductionTime / completedOrders.length / (1000 * 60 * 60 * 24); // بالأيام
    }
    
    // الطلبات المتأخرة
    const overdueOrders = await Order.find({
      dueDate: { $lt: new Date() },
      status: { $nin: ['delivered', 'cancelled'] }
    })
    .populate('customer', 'name')
    .select('orderNumber customer orderDate dueDate status priority')
    .sort({ dueDate: 1 });
    
    // إنتاجية الموظفين
    const employeeProductivity = await Order.aggregate([
      { $unwind: '$productionStages' },
      {
        $match: {
          'productionStages.completedAt': { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$productionStages.assignedTo',
          completedTasks: { $sum: 1 },
          stages: { $addToSet: '$productionStages.stage' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'userInfo'
        }
      },
      {
        $project: {
          _id: 1,
          completedTasks: 1,
          stages: 1,
          userName: { $arrayElemAt: ['$userInfo.name', 0] }
        }
      },
      { $sort: { 'completedTasks': -1 } }
    ]);
    
    res.json({
      ordersByStatus,
      averageProductionTime,
      overdueOrders,
      employeeProductivity
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/reports/customers
 * @desc    تقرير العملاء
 * @access  Private
 */
exports.getCustomerReport = async (req, res) => {
  try {
    // تحديد نطاق التاريخ
    const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date(new Date().setFullYear(new Date().getFullYear() - 1));
    const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
    
    // أفضل العملاء من حيث المبيعات
    const topCustomersByRevenue = await Order.aggregate([
      {
        $match: {
          orderDate: { $gte: startDate, $lte: endDate },
          status: { $nin: ['cancelled'] }
        }
      },
      {
        $group: {
          _id: '$customer',
          totalRevenue: { $sum: '$totalAmount' },
          orderCount: { $sum: 1 },
          averageOrderValue: { $avg: '$totalAmount' }
        }
      },
      { $sort: { 'totalRevenue': -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'customers',
          localField: '_id',
          foreignField: '_id',
          as: 'customerInfo'
        }
      },
      {
        $project: {
          _id: 1,
          totalRevenue: 1,
          orderCount: 1,
          averageOrderValue: 1,
          customerName: { $arrayElemAt: ['$customerInfo.name', 0] },
          customerType: { $arrayElemAt: ['$customerInfo.customerType', 0] },
          contactPerson: { $arrayElemAt: ['$customerInfo.contactPerson', 0] }
        }
      }
    ]);
    
    // العملاء حسب النوع
    const customersByType = await Customer.aggregate([
      {
        $group: {
          _id: '$customerType',
          count: { $sum: 1 }
        }
      },
      { $sort: { 'count': -1 } }
    ]);
    
    // العملاء الجدد حسب الشهر
    const newCustomersByMonth = await Customer.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);
    
    // العملاء غير النشطين (لم يطلبوا منذ 6 أشهر)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    const inactiveCustomers = await Customer.aggregate([
      {
        $lookup: {
          from: 'orders',
          localField: '_id',
          foreignField: 'customer',
          as: 'orders'
        }
      },
      {
        $match: {
          $or: [
            { orders: { $size: 0 } },
            { 'orders.orderDate': { $lt: sixMonthsAgo } }
          ]
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          email: 1,
          phone: 1,
          customerType: 1,
          lastOrderDate: { $max: '$orders.orderDate' }
        }
      },
      { $sort: { 'lastOrderDate': 1 } }
    ]);
    
    res.json({
      topCustomersByRevenue,
      customersByType,
      newCustomersByMonth,
      inactiveCustomers
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};