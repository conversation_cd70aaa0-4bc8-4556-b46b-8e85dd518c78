import React from 'react';
import { Box, Typography, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import { PersonAdd } from '@mui/icons-material';

const Customers: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          إدارة العملاء
        </Typography>
        <Button variant="contained" startIcon={<PersonAdd />}>
          إضافة عميل جديد
        </Button>
      </Box>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            قائمة العملاء
          </Typography>
          <Typography color="text.secondary">
            سيتم تطوير هذه الصفحة قريباً مع جميع وظائف إدارة العملاء.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Customers;
