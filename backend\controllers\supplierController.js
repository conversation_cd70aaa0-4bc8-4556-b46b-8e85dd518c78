const Supplier = require('../models/Supplier');
const Inventory = require('../models/Inventory');
const { validationResult } = require('express-validator');

/**
 * @route   POST api/suppliers
 * @desc    إنشاء مورد جديد
 * @access  Private
 */
exports.createSupplier = async (req, res) => {
  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const {
    name,
    contactPerson,
    email,
    phone,
    address,
    taxNumber,
    supplierType,
    website,
    paymentTerms,
    notes,
    active
  } = req.body;

  try {
    // التحقق من عدم وجود مورد بنفس البريد الإلكتروني
    let supplier = await Supplier.findOne({ email });
    if (supplier) {
      return res.status(400).json({ msg: 'يوجد مورد بنفس البريد الإلكتروني بالفعل' });
    }

    // إنشاء مورد جديد
    supplier = new Supplier({
      name,
      contactPerson,
      email,
      phone,
      address,
      taxNumber,
      supplierType,
      website,
      paymentTerms,
      notes,
      active: active !== undefined ? active : true,
      createdBy: req.user.id
    });

    // حفظ المورد في قاعدة البيانات
    await supplier.save();

    res.json(supplier);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/suppliers
 * @desc    الحصول على قائمة الموردين
 * @access  Private
 */
exports.getSuppliers = async (req, res) => {
  try {
    // البحث عن الموردين مع إمكانية التصفية
    const query = {};
    
    // تصفية حسب نوع المورد
    if (req.query.supplierType) {
      query.supplierType = req.query.supplierType;
    }
    
    // تصفية حسب الحالة النشطة
    if (req.query.active !== undefined) {
      query.active = req.query.active === 'true';
    }
    
    // البحث بالاسم أو البريد الإلكتروني أو رقم الهاتف
    if (req.query.search) {
      query.$or = [
        { name: { $regex: req.query.search, $options: 'i' } },
        { email: { $regex: req.query.search, $options: 'i' } },
        { phone: { $regex: req.query.search, $options: 'i' } },
        { contactPerson: { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    // الحصول على الموردين مع الترتيب والتقسيم
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const suppliers = await Supplier.find(query)
      .populate('createdBy', 'name')
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit);
    
    // الحصول على إجمالي عدد الموردين للتصفح
    const total = await Supplier.countDocuments(query);
    
    res.json({
      suppliers,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/suppliers/:id
 * @desc    الحصول على مورد محدد
 * @access  Private
 */
exports.getSupplierById = async (req, res) => {
  try {
    const supplier = await Supplier.findById(req.params.id)
      .populate('createdBy', 'name');

    if (!supplier) {
      return res.status(404).json({ msg: 'المورد غير موجود' });
    }

    // الحصول على عناصر المخزون المرتبطة بهذا المورد
    const inventoryItems = await Inventory.find({ supplier: req.params.id })
      .select('name category quantity unit costPrice');

    // إضافة عناصر المخزون إلى بيانات المورد
    const supplierData = supplier.toObject();
    supplierData.inventoryItems = inventoryItems;

    res.json(supplierData);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'المورد غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   PUT api/suppliers/:id
 * @desc    تحديث مورد
 * @access  Private
 */
exports.updateSupplier = async (req, res) => {
  const {
    name,
    contactPerson,
    email,
    phone,
    address,
    taxNumber,
    supplierType,
    website,
    paymentTerms,
    notes,
    active
  } = req.body;

  // بناء كائن تحديث المورد
  const supplierFields = {};
  if (name) supplierFields.name = name;
  if (contactPerson) supplierFields.contactPerson = contactPerson;
  if (email) supplierFields.email = email;
  if (phone) supplierFields.phone = phone;
  if (address) supplierFields.address = address;
  if (taxNumber) supplierFields.taxNumber = taxNumber;
  if (supplierType) supplierFields.supplierType = supplierType;
  if (website) supplierFields.website = website;
  if (paymentTerms) supplierFields.paymentTerms = paymentTerms;
  if (notes) supplierFields.notes = notes;
  if (active !== undefined) supplierFields.active = active;

  try {
    let supplier = await Supplier.findById(req.params.id);

    if (!supplier) {
      return res.status(404).json({ msg: 'المورد غير موجود' });
    }

    // التحقق من عدم وجود مورد آخر بنفس البريد الإلكتروني
    if (email && email !== supplier.email) {
      const existingSupplier = await Supplier.findOne({ email });
      if (existingSupplier) {
        return res.status(400).json({ msg: 'يوجد مورد آخر بنفس البريد الإلكتروني بالفعل' });
      }
    }

    // تحديث المورد
    supplier = await Supplier.findByIdAndUpdate(
      req.params.id,
      { $set: supplierFields },
      { new: true }
    );

    res.json(supplier);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'المورد غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   DELETE api/suppliers/:id
 * @desc    حذف مورد
 * @access  Private
 */
exports.deleteSupplier = async (req, res) => {
  try {
    const supplier = await Supplier.findById(req.params.id);

    if (!supplier) {
      return res.status(404).json({ msg: 'المورد غير موجود' });
    }

    // التحقق من عدم وجود عناصر مخزون مرتبطة بهذا المورد
    const inventoryItems = await Inventory.find({ supplier: req.params.id });
    if (inventoryItems.length > 0) {
      return res.status(400).json({ 
        msg: 'لا يمكن حذف المورد لأنه مرتبط بعناصر في المخزون',
        items: inventoryItems.map(item => ({ id: item._id, name: item.name }))
      });
    }

    // حذف المورد
    await Supplier.findByIdAndRemove(req.params.id);

    res.json({ msg: 'تم حذف المورد' });
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'المورد غير موجود' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/suppliers/types
 * @desc    الحصول على قائمة أنواع الموردين
 * @access  Private
 */
exports.getSupplierTypes = async (req, res) => {
  try {
    const types = await Supplier.distinct('supplierType');
    res.json(types);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};