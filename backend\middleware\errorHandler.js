const { logger } = require('../utils/logger');

/**
 * وسيط لمعالجة الأخطاء في Express
 */
const errorHandler = (err, req, res, next) => {
  // تسجيل الخطأ
  logger.error(`${err.name}: ${err.message}`, {
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    user: req.user ? req.user.id : 'anonymous'
  });

  // تحديد نوع الخطأ وإعداد الاستجابة المناسبة
  let statusCode = 500;
  let message = 'خطأ في الخادم';
  let errors = [];

  // أخطاء التحقق من الصحة
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'خطأ في التحقق من صحة البيانات';
    
    // معالجة أخطاء Mongoose
    if (err.errors) {
      Object.keys(err.errors).forEach(key => {
        errors.push({
          param: key,
          msg: err.errors[key].message,
          value: err.errors[key].value
        });
      });
    }
  }
  
  // أخطاء التحقق من Express Validator
  else if (Array.isArray(err) && err.length > 0 && err[0].msg) {
    statusCode = 400;
    message = 'خطأ في التحقق من صحة البيانات';
    errors = err;
  }
  
  // أخطاء JWT
  else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'خطأ في المصادقة';
  }
  
  // أخطاء Mongoose CastError (مثل ObjectId غير صالح)
  else if (err.name === 'CastError') {
    statusCode = 400;
    message = `قيمة غير صالحة للحقل: ${err.path}`;
  }
  
  // أخطاء Mongoose DuplicateKey
  else if (err.name === 'MongoError' && err.code === 11000) {
    statusCode = 400;
    message = 'القيمة موجودة بالفعل';
    const field = Object.keys(err.keyValue)[0];
    errors.push({
      param: field,
      msg: `القيمة '${err.keyValue[field]}' مستخدمة بالفعل`,
      value: err.keyValue[field]
    });
  }
  
  // أخطاء مخصصة
  else if (err.statusCode) {
    statusCode = err.statusCode;
    message = err.message;
    errors = err.errors || [];
  }

  // إرسال الاستجابة
  res.status(statusCode).json({
    success: false,
    message,
    errors: errors.length > 0 ? errors : undefined,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
};

/**
 * وسيط للتعامل مع المسارات غير الموجودة
 */
const notFound = (req, res, next) => {
  const error = new Error(`المسار غير موجود - ${req.originalUrl}`);
  error.statusCode = 404;
  next(error);
};

module.exports = { errorHandler, notFound };