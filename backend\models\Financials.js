const mongoose = require('mongoose');

const financialSchema = new mongoose.Schema(
  {
    type: {
      type: String,
      required: [true, 'Please add a type (income or expense)'],
      enum: ['income', 'expense'],
    },
    description: {
      type: String,
      required: [true, 'Please add a description'],
      trim: true,
    },
    amount: {
      type: Number,
      required: [true, 'Please add a positive or negative number'],
    },
    category: {
      type: String,
      required: [true, 'Please add a category'],
      trim: true,
    },
    date: {
      type: Date,
      default: Date.now,
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: true,
    },
    related: {
      type: mongoose.Schema.ObjectId,
      refPath: 'onModel'
    },
    onModel: {
      type: String,
      enum: ['Order', 'Invoice', 'Supplier']
    }
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model('Financials', financialSchema);
