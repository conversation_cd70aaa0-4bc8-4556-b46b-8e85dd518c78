/**
 * ملف إعداد وتكوين الأذونات والصلاحيات
 * يقوم بإعداد وتكوين نظام الأذونات والصلاحيات للتطبيق
 */

const config = require('config');
const logger = require('../utils/logger');
const { recordError } = require('./monitoring');

// الحصول على إعدادات التطبيق من ملف التكوين
const appConfig = config.get('app');

// تعريف الأدوار في النظام
const ROLES = {
  SUPER_ADMIN: 'super_admin',     // مدير النظام الأعلى
  ADMIN: 'admin',                 // مدير النظام
  MANAGER: 'manager',             // مدير
  ACCOUNTANT: 'accountant',       // محاسب
  SALES: 'sales',                 // مبيعات
  INVENTORY: 'inventory',         // مخزون
  PRODUCTION: 'production',       // إنتاج
  CUSTOMER_SERVICE: 'customer_service', // خدمة العملاء
  EMPLOYEE: 'employee',           // موظف
  CUSTOMER: 'customer',           // عميل
  GUEST: 'guest',                 // زائر
};

// تعريف الأذونات في النظام
const PERMISSIONS = {
  // أذونات المستخدمين
  USER_VIEW: 'user:view',
  USER_CREATE: 'user:create',
  USER_EDIT: 'user:edit',
  USER_DELETE: 'user:delete',
  USER_MANAGE_ROLES: 'user:manage:roles',
  
  // أذونات العملاء
  CUSTOMER_VIEW: 'customer:view',
  CUSTOMER_CREATE: 'customer:create',
  CUSTOMER_EDIT: 'customer:edit',
  CUSTOMER_DELETE: 'customer:delete',
  
  // أذونات الطلبات
  ORDER_VIEW: 'order:view',
  ORDER_CREATE: 'order:create',
  ORDER_EDIT: 'order:edit',
  ORDER_DELETE: 'order:delete',
  ORDER_APPROVE: 'order:approve',
  ORDER_CANCEL: 'order:cancel',
  ORDER_PROCESS: 'order:process',
  ORDER_COMPLETE: 'order:complete',
  
  // أذونات المنتجات والمخزون
  PRODUCT_VIEW: 'product:view',
  PRODUCT_CREATE: 'product:create',
  PRODUCT_EDIT: 'product:edit',
  PRODUCT_DELETE: 'product:delete',
  INVENTORY_VIEW: 'inventory:view',
  INVENTORY_MANAGE: 'inventory:manage',
  
  // أذونات الفواتير والمدفوعات
  INVOICE_VIEW: 'invoice:view',
  INVOICE_CREATE: 'invoice:create',
  INVOICE_EDIT: 'invoice:edit',
  INVOICE_DELETE: 'invoice:delete',
  PAYMENT_VIEW: 'payment:view',
  PAYMENT_CREATE: 'payment:create',
  PAYMENT_EDIT: 'payment:edit',
  PAYMENT_DELETE: 'payment:delete',
  
  // أذونات الموردين
  SUPPLIER_VIEW: 'supplier:view',
  SUPPLIER_CREATE: 'supplier:create',
  SUPPLIER_EDIT: 'supplier:edit',
  SUPPLIER_DELETE: 'supplier:delete',
  
  // أذونات التقارير
  REPORT_VIEW: 'report:view',
  REPORT_SALES: 'report:sales',
  REPORT_INVENTORY: 'report:inventory',
  REPORT_FINANCIAL: 'report:financial',
  REPORT_CUSTOMER: 'report:customer',
  REPORT_EXPORT: 'report:export',
  
  // أذونات النظام
  SYSTEM_SETTINGS: 'system:settings',
  SYSTEM_LOGS: 'system:logs',
  SYSTEM_BACKUP: 'system:backup',
  SYSTEM_RESTORE: 'system:restore',
  SYSTEM_MAINTENANCE: 'system:maintenance',
};

// تعريف مجموعات الأذونات
const PERMISSION_GROUPS = {
  USER_MANAGEMENT: [
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_EDIT,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_MANAGE_ROLES,
  ],
  CUSTOMER_MANAGEMENT: [
    PERMISSIONS.CUSTOMER_VIEW,
    PERMISSIONS.CUSTOMER_CREATE,
    PERMISSIONS.CUSTOMER_EDIT,
    PERMISSIONS.CUSTOMER_DELETE,
  ],
  ORDER_MANAGEMENT: [
    PERMISSIONS.ORDER_VIEW,
    PERMISSIONS.ORDER_CREATE,
    PERMISSIONS.ORDER_EDIT,
    PERMISSIONS.ORDER_DELETE,
    PERMISSIONS.ORDER_APPROVE,
    PERMISSIONS.ORDER_CANCEL,
    PERMISSIONS.ORDER_PROCESS,
    PERMISSIONS.ORDER_COMPLETE,
  ],
  PRODUCT_MANAGEMENT: [
    PERMISSIONS.PRODUCT_VIEW,
    PERMISSIONS.PRODUCT_CREATE,
    PERMISSIONS.PRODUCT_EDIT,
    PERMISSIONS.PRODUCT_DELETE,
  ],
  INVENTORY_MANAGEMENT: [
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.INVENTORY_MANAGE,
  ],
  INVOICE_MANAGEMENT: [
    PERMISSIONS.INVOICE_VIEW,
    PERMISSIONS.INVOICE_CREATE,
    PERMISSIONS.INVOICE_EDIT,
    PERMISSIONS.INVOICE_DELETE,
  ],
  PAYMENT_MANAGEMENT: [
    PERMISSIONS.PAYMENT_VIEW,
    PERMISSIONS.PAYMENT_CREATE,
    PERMISSIONS.PAYMENT_EDIT,
    PERMISSIONS.PAYMENT_DELETE,
  ],
  SUPPLIER_MANAGEMENT: [
    PERMISSIONS.SUPPLIER_VIEW,
    PERMISSIONS.SUPPLIER_CREATE,
    PERMISSIONS.SUPPLIER_EDIT,
    PERMISSIONS.SUPPLIER_DELETE,
  ],
  REPORT_MANAGEMENT: [
    PERMISSIONS.REPORT_VIEW,
    PERMISSIONS.REPORT_SALES,
    PERMISSIONS.REPORT_INVENTORY,
    PERMISSIONS.REPORT_FINANCIAL,
    PERMISSIONS.REPORT_CUSTOMER,
    PERMISSIONS.REPORT_EXPORT,
  ],
  SYSTEM_MANAGEMENT: [
    PERMISSIONS.SYSTEM_SETTINGS,
    PERMISSIONS.SYSTEM_LOGS,
    PERMISSIONS.SYSTEM_BACKUP,
    PERMISSIONS.SYSTEM_RESTORE,
    PERMISSIONS.SYSTEM_MAINTENANCE,
  ],
};

// تعريف الأذونات لكل دور
const ROLE_PERMISSIONS = {
  [ROLES.SUPER_ADMIN]: [
    // مدير النظام الأعلى لديه جميع الأذونات
    ...Object.values(PERMISSIONS),
  ],
  [ROLES.ADMIN]: [
    // مدير النظام لديه معظم الأذونات باستثناء بعض أذونات النظام الحساسة
    ...PERMISSION_GROUPS.USER_MANAGEMENT,
    ...PERMISSION_GROUPS.CUSTOMER_MANAGEMENT,
    ...PERMISSION_GROUPS.ORDER_MANAGEMENT,
    ...PERMISSION_GROUPS.PRODUCT_MANAGEMENT,
    ...PERMISSION_GROUPS.INVENTORY_MANAGEMENT,
    ...PERMISSION_GROUPS.INVOICE_MANAGEMENT,
    ...PERMISSION_GROUPS.PAYMENT_MANAGEMENT,
    ...PERMISSION_GROUPS.SUPPLIER_MANAGEMENT,
    ...PERMISSION_GROUPS.REPORT_MANAGEMENT,
    PERMISSIONS.SYSTEM_SETTINGS,
    PERMISSIONS.SYSTEM_LOGS,
  ],
  [ROLES.MANAGER]: [
    // المدير لديه أذونات الإدارة العامة
    PERMISSIONS.USER_VIEW,
    ...PERMISSION_GROUPS.CUSTOMER_MANAGEMENT,
    ...PERMISSION_GROUPS.ORDER_MANAGEMENT,
    ...PERMISSION_GROUPS.PRODUCT_MANAGEMENT,
    ...PERMISSION_GROUPS.INVENTORY_MANAGEMENT,
    ...PERMISSION_GROUPS.INVOICE_MANAGEMENT,
    ...PERMISSION_GROUPS.PAYMENT_MANAGEMENT,
    ...PERMISSION_GROUPS.SUPPLIER_MANAGEMENT,
    ...PERMISSION_GROUPS.REPORT_MANAGEMENT,
  ],
  [ROLES.ACCOUNTANT]: [
    // المحاسب لديه أذونات مالية
    PERMISSIONS.CUSTOMER_VIEW,
    PERMISSIONS.ORDER_VIEW,
    PERMISSIONS.PRODUCT_VIEW,
    PERMISSIONS.INVENTORY_VIEW,
    ...PERMISSION_GROUPS.INVOICE_MANAGEMENT,
    ...PERMISSION_GROUPS.PAYMENT_MANAGEMENT,
    PERMISSIONS.REPORT_VIEW,
    PERMISSIONS.REPORT_FINANCIAL,
    PERMISSIONS.REPORT_EXPORT,
  ],
  [ROLES.SALES]: [
    // موظف المبيعات لديه أذونات المبيعات والعملاء
    ...PERMISSION_GROUPS.CUSTOMER_MANAGEMENT,
    PERMISSIONS.ORDER_VIEW,
    PERMISSIONS.ORDER_CREATE,
    PERMISSIONS.ORDER_EDIT,
    PERMISSIONS.PRODUCT_VIEW,
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.INVOICE_VIEW,
    PERMISSIONS.PAYMENT_VIEW,
    PERMISSIONS.PAYMENT_CREATE,
    PERMISSIONS.REPORT_VIEW,
    PERMISSIONS.REPORT_SALES,
    PERMISSIONS.REPORT_CUSTOMER,
  ],
  [ROLES.INVENTORY]: [
    // موظف المخزون لديه أذونات المخزون والمنتجات
    PERMISSIONS.PRODUCT_VIEW,
    PERMISSIONS.PRODUCT_CREATE,
    PERMISSIONS.PRODUCT_EDIT,
    ...PERMISSION_GROUPS.INVENTORY_MANAGEMENT,
    ...PERMISSION_GROUPS.SUPPLIER_MANAGEMENT,
    PERMISSIONS.ORDER_VIEW,
    PERMISSIONS.REPORT_VIEW,
    PERMISSIONS.REPORT_INVENTORY,
  ],
  [ROLES.PRODUCTION]: [
    // موظف الإنتاج لديه أذونات الإنتاج والطلبات
    PERMISSIONS.ORDER_VIEW,
    PERMISSIONS.ORDER_PROCESS,
    PERMISSIONS.ORDER_COMPLETE,
    PERMISSIONS.PRODUCT_VIEW,
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.CUSTOMER_VIEW,
  ],
  [ROLES.CUSTOMER_SERVICE]: [
    // موظف خدمة العملاء لديه أذونات العملاء والطلبات
    ...PERMISSION_GROUPS.CUSTOMER_MANAGEMENT,
    PERMISSIONS.ORDER_VIEW,
    PERMISSIONS.ORDER_CREATE,
    PERMISSIONS.ORDER_EDIT,
    PERMISSIONS.PRODUCT_VIEW,
    PERMISSIONS.INVOICE_VIEW,
  ],
  [ROLES.EMPLOYEE]: [
    // الموظف العادي لديه أذونات محدودة
    PERMISSIONS.CUSTOMER_VIEW,
    PERMISSIONS.ORDER_VIEW,
    PERMISSIONS.PRODUCT_VIEW,
    PERMISSIONS.INVENTORY_VIEW,
  ],
  [ROLES.CUSTOMER]: [
    // العميل لديه أذونات محدودة جدًا
    // عادة ما يتم التحكم في هذه الأذونات من خلال واجهة العميل
  ],
  [ROLES.GUEST]: [
    // الزائر ليس لديه أذونات
  ],
};

/**
 * التحقق من امتلاك المستخدم للإذن
 * @param {Object} user - كائن المستخدم
 * @param {string} permission - الإذن المطلوب
 * @returns {boolean} نتيجة التحقق
 */
const hasPermission = (user, permission) => {
  try {
    // التحقق من وجود المستخدم
    if (!user) {
      return false;
    }
    
    // مدير النظام الأعلى لديه جميع الأذونات
    if (user.roles && user.roles.includes(ROLES.SUPER_ADMIN)) {
      return true;
    }
    
    // التحقق من الأذونات المباشرة
    if (user.permissions && user.permissions.includes(permission)) {
      return true;
    }
    
    // التحقق من الأذونات بناءً على الأدوار
    if (user.roles && Array.isArray(user.roles)) {
      for (const role of user.roles) {
        if (ROLE_PERMISSIONS[role] && ROLE_PERMISSIONS[role].includes(permission)) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    logger.error(`فشل في التحقق من الإذن: ${permission}`, error);
    recordError(error);
    return false;
  }
};

/**
 * التحقق من امتلاك المستخدم لأي من الأذونات المحددة
 * @param {Object} user - كائن المستخدم
 * @param {Array<string>} permissions - قائمة الأذونات المطلوبة
 * @returns {boolean} نتيجة التحقق
 */
const hasAnyPermission = (user, permissions) => {
  try {
    if (!user || !permissions || !Array.isArray(permissions)) {
      return false;
    }
    
    for (const permission of permissions) {
      if (hasPermission(user, permission)) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    logger.error(`فشل في التحقق من الأذونات: ${permissions.join(', ')}`, error);
    recordError(error);
    return false;
  }
};

/**
 * التحقق من امتلاك المستخدم لجميع الأذونات المحددة
 * @param {Object} user - كائن المستخدم
 * @param {Array<string>} permissions - قائمة الأذونات المطلوبة
 * @returns {boolean} نتيجة التحقق
 */
const hasAllPermissions = (user, permissions) => {
  try {
    if (!user || !permissions || !Array.isArray(permissions)) {
      return false;
    }
    
    for (const permission of permissions) {
      if (!hasPermission(user, permission)) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    logger.error(`فشل في التحقق من الأذونات: ${permissions.join(', ')}`, error);
    recordError(error);
    return false;
  }
};

/**
 * التحقق من امتلاك المستخدم للدور
 * @param {Object} user - كائن المستخدم
 * @param {string} role - الدور المطلوب
 * @returns {boolean} نتيجة التحقق
 */
const hasRole = (user, role) => {
  try {
    if (!user || !user.roles || !Array.isArray(user.roles)) {
      return false;
    }
    
    return user.roles.includes(role);
  } catch (error) {
    logger.error(`فشل في التحقق من الدور: ${role}`, error);
    recordError(error);
    return false;
  }
};

/**
 * التحقق من امتلاك المستخدم لأي من الأدوار المحددة
 * @param {Object} user - كائن المستخدم
 * @param {Array<string>} roles - قائمة الأدوار المطلوبة
 * @returns {boolean} نتيجة التحقق
 */
const hasAnyRole = (user, roles) => {
  try {
    if (!user || !user.roles || !Array.isArray(user.roles) || !roles || !Array.isArray(roles)) {
      return false;
    }
    
    for (const role of roles) {
      if (user.roles.includes(role)) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    logger.error(`فشل في التحقق من الأدوار: ${roles.join(', ')}`, error);
    recordError(error);
    return false;
  }
};

/**
 * الحصول على جميع أذونات المستخدم
 * @param {Object} user - كائن المستخدم
 * @returns {Array<string>} قائمة الأذونات
 */
const getUserPermissions = (user) => {
  try {
    if (!user) {
      return [];
    }
    
    const permissions = new Set();
    
    // إضافة الأذونات المباشرة
    if (user.permissions && Array.isArray(user.permissions)) {
      user.permissions.forEach(permission => permissions.add(permission));
    }
    
    // إضافة الأذونات بناءً على الأدوار
    if (user.roles && Array.isArray(user.roles)) {
      for (const role of user.roles) {
        if (ROLE_PERMISSIONS[role]) {
          ROLE_PERMISSIONS[role].forEach(permission => permissions.add(permission));
        }
      }
    }
    
    return Array.from(permissions);
  } catch (error) {
    logger.error('فشل في الحصول على أذونات المستخدم', error);
    recordError(error);
    return [];
  }
};

/**
 * إنشاء وسيط للتحقق من الأذونات
 * @param {string|Array<string>} requiredPermissions - الإذن أو الأذونات المطلوبة
 * @param {Object} options - خيارات إضافية
 * @returns {Function} وسيط Express
 */
const requirePermission = (requiredPermissions, options = {}) => {
  return (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'غير مصرح به: يجب تسجيل الدخول',
        });
      }
      
      const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
      
      const hasPermissions = options.requireAll ?
        hasAllPermissions(user, permissions) :
        hasAnyPermission(user, permissions);
      
      if (!hasPermissions) {
        return res.status(403).json({
          success: false,
          message: 'ممنوع: ليس لديك الأذونات المطلوبة',
          requiredPermissions: permissions,
        });
      }
      
      next();
    } catch (error) {
      logger.error('فشل في التحقق من الأذونات في الوسيط', error);
      recordError(error);
      
      return res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء التحقق من الأذونات',
      });
    }
  };
};

/**
 * إنشاء وسيط للتحقق من الأدوار
 * @param {string|Array<string>} requiredRoles - الدور أو الأدوار المطلوبة
 * @param {Object} options - خيارات إضافية
 * @returns {Function} وسيط Express
 */
const requireRole = (requiredRoles, options = {}) => {
  return (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'غير مصرح به: يجب تسجيل الدخول',
        });
      }
      
      const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
      
      // التحقق من وجود دور مدير النظام الأعلى
      if (user.roles && user.roles.includes(ROLES.SUPER_ADMIN)) {
        return next();
      }
      
      const hasRoles = options.requireAll ?
        roles.every(role => user.roles && user.roles.includes(role)) :
        hasAnyRole(user, roles);
      
      if (!hasRoles) {
        return res.status(403).json({
          success: false,
          message: 'ممنوع: ليس لديك الأدوار المطلوبة',
          requiredRoles: roles,
        });
      }
      
      next();
    } catch (error) {
      logger.error('فشل في التحقق من الأدوار في الوسيط', error);
      recordError(error);
      
      return res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء التحقق من الأدوار',
      });
    }
  };
};

/**
 * إنشاء وسيط للتحقق من ملكية المورد
 * @param {Function} getResourceOwnerId - دالة للحصول على معرف مالك المورد
 * @param {Object} options - خيارات إضافية
 * @returns {Function} وسيط Express
 */
const requireOwnership = (getResourceOwnerId, options = {}) => {
  return async (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'غير مصرح به: يجب تسجيل الدخول',
        });
      }
      
      // السماح لمدير النظام الأعلى والمدير بتجاوز التحقق من الملكية
      if (options.adminOverride !== false && 
          user.roles && 
          (user.roles.includes(ROLES.SUPER_ADMIN) || user.roles.includes(ROLES.ADMIN))) {
        return next();
      }
      
      // الحصول على معرف مالك المورد
      const ownerId = await getResourceOwnerId(req);
      
      if (!ownerId) {
        return res.status(404).json({
          success: false,
          message: 'المورد غير موجود',
        });
      }
      
      // التحقق من الملكية
      if (ownerId.toString() !== user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'ممنوع: ليس لديك إذن للوصول إلى هذا المورد',
        });
      }
      
      next();
    } catch (error) {
      logger.error('فشل في التحقق من ملكية المورد في الوسيط', error);
      recordError(error);
      
      return res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء التحقق من ملكية المورد',
      });
    }
  };
};

module.exports = {
  // الثوابت
  ROLES,
  PERMISSIONS,
  PERMISSION_GROUPS,
  ROLE_PERMISSIONS,
  
  // وظائف التحقق
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  hasRole,
  hasAnyRole,
  getUserPermissions,
  
  // وسطاء Express
  requirePermission,
  requireRole,
  requireOwnership,
};