/**
 * ملف إعداد وتكوين الأمان
 * يقوم بإعداد وتكوين إعدادات الأمان للتطبيق
 */

const helmet = require('helmet');
const xss = require('xss-clean');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const hpp = require('hpp');
const config = require('config');
const csrf = require('csurf');
const cookieParser = require('cookie-parser');

// الحصول على إعدادات الأمان من ملف التكوين
const securityConfig = config.get('security');

/**
 * تكوين وسائط الأمان للتطبيق
 * @param {Express} app - تطبيق Express
 */
const setupSecurity = (app) => {
  // حماية الرؤوس HTTP باستخدام Helmet
  if (securityConfig.helmet.enabled) {
    app.use(helmet());
    
    // إعدادات إضافية لـ Content Security Policy
    app.use(
      helmet.contentSecurityPolicy({
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", 'cdn.jsdelivr.net', 'unpkg.com'],
          styleSrc: ["'self'", "'unsafe-inline'", 'cdn.jsdelivr.net', 'fonts.googleapis.com'],
          imgSrc: ["'self'", 'data:', 'cdn.jsdelivr.net'],
          fontSrc: ["'self'", 'fonts.gstatic.com'],
          connectSrc: ["'self'"],
        },
      })
    );
  }
  
  // حماية من هجمات XSS
  if (securityConfig.xss.enabled) {
    app.use(xss());
  }
  
  // تنظيف بيانات MongoDB
  app.use(mongoSanitize());
  
  // حماية من تلوث المعلمات
  app.use(hpp());
  
  // محدد معدل الطلبات
  if (securityConfig.rateLimiter.enabled) {
    const limiter = rateLimit({
      windowMs: securityConfig.rateLimiter.windowMs,
      max: securityConfig.rateLimiter.max,
      message: { message: securityConfig.rateLimiter.message },
      standardHeaders: true,
      legacyHeaders: false,
    });
    
    app.use('/api/', limiter);
  }
  
  // حماية CSRF
  if (securityConfig.csrf.enabled) {
    // تأكد من استخدام cookieParser قبل csurf
    app.use(cookieParser());
    
    const csrfProtection = csrf({
      cookie: {
        key: securityConfig.csrf.cookieName,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
      },
    });
    
    // تطبيق حماية CSRF على مسارات API التي تتطلب ذلك
    app.use('/api/auth/login', csrfProtection);
    app.use('/api/auth/register', csrfProtection);
    app.use('/api/auth/reset-password', csrfProtection);
    app.use('/api/users', csrfProtection);
    
    // إنشاء رمز CSRF وإرساله للعميل
    app.get('/api/csrf-token', csrfProtection, (req, res) => {
      res.json({ csrfToken: req.csrfToken() });
    });
  }
};

/**
 * إنشاء وسيط لتقييد الوصول حسب عنوان IP
 * @param {Array} allowedIPs - قائمة عناوين IP المسموح بها
 * @returns {Function} وسيط Express
 */
const ipRestriction = (allowedIPs) => {
  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (allowedIPs.includes(clientIP) || allowedIPs.includes('*')) {
      return next();
    }
    
    return res.status(403).json({
      message: 'الوصول محظور: عنوان IP غير مصرح به',
    });
  };
};

/**
 * إنشاء وسيط لتقييد الوصول حسب المصدر
 * @param {Array} allowedOrigins - قائمة المصادر المسموح بها
 * @returns {Function} وسيط Express
 */
const originRestriction = (allowedOrigins) => {
  return (req, res, next) => {
    const origin = req.headers.origin;
    
    if (!origin || allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
      return next();
    }
    
    return res.status(403).json({
      message: 'الوصول محظور: المصدر غير مصرح به',
    });
  };
};

/**
 * إنشاء وسيط لتقييد الطرق HTTP
 * @param {Array} allowedMethods - قائمة طرق HTTP المسموح بها
 * @returns {Function} وسيط Express
 */
const methodRestriction = (allowedMethods) => {
  return (req, res, next) => {
    const method = req.method.toUpperCase();
    
    if (allowedMethods.includes(method)) {
      return next();
    }
    
    return res.status(405).json({
      message: 'طريقة HTTP غير مسموح بها',
    });
  };
};

module.exports = {
  setupSecurity,
  ipRestriction,
  originRestriction,
  methodRestriction,
};