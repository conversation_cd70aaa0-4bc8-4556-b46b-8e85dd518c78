const Invoice = require('../models/Invoice');
const Order = require('../models/Order');
const Customer = require('../models/Customer');
const { validationResult } = require('express-validator');

/**
 * @route   POST api/invoices
 * @desc    إنشاء فاتورة جديدة
 * @access  Private
 */
exports.createInvoice = async (req, res) => {
  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const {
    customer,
    order,
    dueDate,
    items,
    notes,
    paymentTerms,
    discount
  } = req.body;

  try {
    // التحقق من وجود العميل
    const customerExists = await Customer.findById(customer);
    if (!customerExists) {
      return res.status(404).json({ msg: 'العميل غير موجود' });
    }

    // التحقق من وجود الطلب إذا تم تحديده
    if (order) {
      const orderExists = await Order.findById(order);
      if (!orderExists) {
        return res.status(404).json({ msg: 'الطلب غير موجود' });
      }
    }

    // حساب المجاميع
    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
    const taxRate = 15; // ضريبة القيمة المضافة 15%
    const discountValue = discount || 0;
    const taxAmount = (subtotal - discountValue) * (taxRate / 100);
    const totalAmount = subtotal - discountValue + taxAmount;

    // إنشاء فاتورة جديدة
    const invoice = new Invoice({
      customer,
      order,
      dueDate,
      items,
      notes,
      paymentTerms,
      subtotal,
      taxRate,
      taxAmount,
      discount: discountValue,
      totalAmount,
      createdBy: req.user.id
    });

    // حفظ الفاتورة في قاعدة البيانات
    await invoice.save();

    // تحديث حالة الدفع في الطلب إذا تم تحديده
    if (order) {
      await Order.findByIdAndUpdate(order, {
        $set: { paymentStatus: 'pending' }
      });
    }

    res.json(invoice);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/invoices
 * @desc    الحصول على قائمة الفواتير
 * @access  Private
 */
exports.getInvoices = async (req, res) => {
  try {
    // البحث عن الفواتير مع إمكانية التصفية
    const query = {};
    
    // تصفية حسب العميل
    if (req.query.customer) {
      query.customer = req.query.customer;
    }
    
    // تصفية حسب الطلب
    if (req.query.order) {
      query.order = req.query.order;
    }
    
    // تصفية حسب الحالة
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    // البحث برقم الفاتورة
    if (req.query.search) {
      query.$or = [
        { invoiceNumber: { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    // تصفية حسب التاريخ
    if (req.query.startDate && req.query.endDate) {
      query.issueDate = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    }
    
    // الحصول على الفواتير مع الترتيب والتقسيم
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const invoices = await Invoice.find(query)
      .populate('customer', 'name email phone')
      .populate('order', 'orderNumber')
      .populate('createdBy', 'name')
      .sort({ issueDate: -1 })
      .skip(skip)
      .limit(limit);
    
    // الحصول على إجمالي عدد الفواتير للتصفح
    const total = await Invoice.countDocuments(query);
    
    res.json({
      invoices,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/invoices/:id
 * @desc    الحصول على فاتورة محددة
 * @access  Private
 */
exports.getInvoiceById = async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id)
      .populate('customer', 'name email phone address taxNumber')
      .populate('order', 'orderNumber orderDate')
      .populate('createdBy', 'name')
      .populate('payments.receivedBy', 'name');

    if (!invoice) {
      return res.status(404).json({ msg: 'الفاتورة غير موجودة' });
    }

    res.json(invoice);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'الفاتورة غير موجودة' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   PUT api/invoices/:id
 * @desc    تحديث فاتورة
 * @access  Private
 */
exports.updateInvoice = async (req, res) => {
  const {
    dueDate,
    items,
    notes,
    paymentTerms,
    discount
  } = req.body;

  // بناء كائن تحديث الفاتورة
  const invoiceFields = {};
  if (dueDate) invoiceFields.dueDate = dueDate;
  if (items) invoiceFields.items = items;
  if (notes) invoiceFields.notes = notes;
  if (paymentTerms) invoiceFields.paymentTerms = paymentTerms;
  if (discount !== undefined) invoiceFields.discount = discount;

  // إعادة حساب المجاميع إذا تم تغيير العناصر أو الخصم
  if (items || discount !== undefined) {
    const subtotal = items
      ? items.reduce((sum, item) => sum + item.totalPrice, 0)
      : req.body.subtotal;
    
    const taxRate = 15; // ضريبة القيمة المضافة 15%
    const discountValue = discount !== undefined ? discount : (req.body.discount || 0);
    const taxAmount = (subtotal - discountValue) * (taxRate / 100);
    const totalAmount = subtotal - discountValue + taxAmount;
    
    invoiceFields.subtotal = subtotal;
    invoiceFields.taxRate = taxRate;
    invoiceFields.taxAmount = taxAmount;
    invoiceFields.totalAmount = totalAmount;
  }

  try {
    let invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      return res.status(404).json({ msg: 'الفاتورة غير موجودة' });
    }

    // تحديث الفاتورة
    invoice = await Invoice.findByIdAndUpdate(
      req.params.id,
      { $set: invoiceFields },
      { new: true }
    );

    res.json(invoice);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'الفاتورة غير موجودة' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   POST api/invoices/payment/:id
 * @desc    إضافة دفعة للفاتورة
 * @access  Private
 */
exports.addPayment = async (req, res) => {
  const { amount, method, reference, notes } = req.body;

  // التحقق من صحة البيانات المدخلة
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      return res.status(404).json({ msg: 'الفاتورة غير موجودة' });
    }

    // التحقق من مبلغ الدفع
    const remainingAmount = invoice.getRemainingAmount();
    if (amount > remainingAmount) {
      return res.status(400).json({ msg: 'مبلغ الدفع أكبر من المبلغ المتبقي' });
    }

    // إنشاء دفعة جديدة
    const newPayment = {
      amount,
      date: Date.now(),
      method,
      reference,
      notes,
      receivedBy: req.user.id
    };

    // إضافة الدفعة إلى الفاتورة
    invoice.payments.push(newPayment);

    // تحديث حالة الفاتورة
    const totalPaid = invoice.getTotalPaid() + amount;
    if (totalPaid >= invoice.totalAmount) {
      invoice.status = 'paid';
    } else if (totalPaid > 0) {
      invoice.status = 'partial';
    }

    // حفظ التغييرات
    await invoice.save();

    // تحديث حالة الدفع في الطلب إذا كان مرتبطًا
    if (invoice.order) {
      const paymentStatus = invoice.status === 'paid' ? 'paid' : 'partial';
      await Order.findByIdAndUpdate(invoice.order, {
        $set: { paymentStatus, paidAmount: totalPaid }
      });
    }

    res.json(invoice);
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'الفاتورة غير موجودة' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   DELETE api/invoices/:id
 * @desc    حذف فاتورة
 * @access  Private
 */
exports.deleteInvoice = async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      return res.status(404).json({ msg: 'الفاتورة غير موجودة' });
    }

    // حذف الفاتورة
    await Invoice.findByIdAndRemove(req.params.id);

    res.json({ msg: 'تم حذف الفاتورة' });
  } catch (err) {
    console.error(err.message);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'الفاتورة غير موجودة' });
    }
    res.status(500).send('خطأ في الخادم');
  }
};

/**
 * @route   GET api/invoices/dashboard/stats
 * @desc    الحصول على إحصائيات الفواتير للوحة المعلومات
 * @access  Private
 */
exports.getInvoiceStats = async (req, res) => {
  try {
    // إجمالي عدد الفواتير
    const totalInvoices = await Invoice.countDocuments();
    
    // عدد الفواتير حسب الحالة
    const invoicesByStatus = await Invoice.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);
    
    // إجمالي المبالغ المستحقة
    const totalDue = await Invoice.aggregate([
      { $match: { status: { $ne: 'paid' } } },
      { $group: { _id: null, total: { $sum: '$totalAmount' } } }
    ]);
    
    // إجمالي المبالغ المدفوعة
    const totalPaid = await Invoice.aggregate([
      { $unwind: '$payments' },
      { $group: { _id: null, total: { $sum: '$payments.amount' } } }
    ]);
    
    // الفواتير المتأخرة (تاريخ الاستحقاق أقل من اليوم والحالة ليست مدفوعة)
    const overdueInvoices = await Invoice.countDocuments({
      dueDate: { $lt: new Date() },
      status: { $ne: 'paid' }
    });
    
    // الفواتير حسب الشهر (للرسم البياني)
    const invoicesByMonth = await Invoice.aggregate([
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m', date: '$issueDate' } },
          count: { $sum: 1 },
          total: { $sum: '$totalAmount' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);
    
    res.json({
      totalInvoices,
      invoicesByStatus,
      totalDue: totalDue.length > 0 ? totalDue[0].total : 0,
      totalPaid: totalPaid.length > 0 ? totalPaid[0].total : 0,
      overdueInvoices,
      invoicesByMonth
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('خطأ في الخادم');
  }
};