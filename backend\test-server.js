const express = require('express');
const cors = require('cors');

const app = express();

// إعداد الوسائط الأساسية
app.use(cors());
app.use(express.json());

// مسار اختبار
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'مرحباً بك في نظام المطبعة المتطور',
    version: '2.0.0',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'الخادم يعمل بشكل طبيعي'
  });
});

// تشغيل الخادم
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 خادم الاختبار يعمل على المنفذ ${PORT}`);
  console.log(`🌍 الرابط: http://localhost:${PORT}`);
});
