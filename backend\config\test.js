/**
 * ملف الإعدادات للبيئة الاختبارية
 * يتم دمج هذه الإعدادات مع الإعدادات الافتراضية عند تشغيل التطبيق في بيئة الاختبار
 */

module.exports = {
  // إعدادات التطبيق الأساسية
  app: {
    baseUrl: 'http://localhost:5000',
    frontendUrl: 'http://localhost:3000',
    port: process.env.PORT || 5000,
    env: 'test',
  },

  // إعدادات قاعدة البيانات
  db: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/print-system-test',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      autoIndex: true,
    },
  },

  // إعدادات المصادقة والأمان
  auth: {
    jwtSecret: 'test-jwt-secret-key',
    refreshTokenSecret: 'test-refresh-token-secret-key',
    cookieSecure: false,
  },

  // إعدادات البريد الإلكتروني
  email: {
    enabled: false, // تعطيل إرسال البريد الإلكتروني في بيئة الاختبار
  },

  // إعدادات التسجيل والمراقبة
  logging: {
    level: 'error',
    silent: true, // كتم السجلات في بيئة الاختبار
  },

  // إعدادات CORS
  cors: {
    origin: '*',
  },

  // إعدادات الأمان
  security: {
    rateLimiter: {
      enabled: false,
    },
    helmet: {
      enabled: false,
    },
    xss: {
      enabled: true,
    },
    csrf: {
      enabled: false,
    },
  },

  // إعدادات المستخدمين
  users: {
    emailVerificationRequired: false,
    maxLoginAttempts: 10, // زيادة عدد محاولات تسجيل الدخول في بيئة الاختبار
  },
};