const nodemailer = require('nodemailer');
const config = require('config');
const { logger } = require('./logger');

// الحصول على إعدادات البريد الإلكتروني من ملف التكوين
const emailConfig = config.get('emailConfig');

/**
 * إنشاء ناقل البريد الإلكتروني
 */
const transporter = nodemailer.createTransport({
  host: emailConfig.host,
  port: emailConfig.port,
  secure: emailConfig.secure,
  auth: {
    user: emailConfig.user,
    pass: emailConfig.pass
  }
});

/**
 * إرسال بريد إلكتروني
 * @param {Object} options - خيارات البريد الإلكتروني
 * @param {string} options.to - عنوان البريد الإلكتروني للمستلم
 * @param {string} options.subject - موضوع البريد الإلكتروني
 * @param {string} options.text - نص البريد الإلكتروني (اختياري)
 * @param {string} options.html - محتوى HTML للبريد الإلكتروني (اختياري)
 * @param {Array} options.attachments - مرفقات البريد الإلكتروني (اختياري)
 * @returns {Promise} وعد بنتيجة الإرسال
 */
const sendEmail = async (options) => {
  try {
    const mailOptions = {
      from: `${emailConfig.fromName} <${emailConfig.fromEmail}>`,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
      attachments: options.attachments
    };

    const info = await transporter.sendMail(mailOptions);
    logger.info(`تم إرسال البريد الإلكتروني: ${info.messageId}`);
    return info;
  } catch (error) {
    logger.error('خطأ في إرسال البريد الإلكتروني:', error);
    throw new Error('فشل في إرسال البريد الإلكتروني');
  }
};

/**
 * إرسال إشعار بريد إلكتروني للطلب الجديد
 * @param {Object} order - بيانات الطلب
 * @param {Object} customer - بيانات العميل
 * @returns {Promise} وعد بنتيجة الإرسال
 */
const sendNewOrderNotification = async (order, customer) => {
  const subject = `طلب جديد #${order.orderNumber}`;
  const html = `
    <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
      <h2>طلب جديد</h2>
      <p>تم استلام طلب جديد من العميل ${customer.name}.</p>
      <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
        <p><strong>رقم الطلب:</strong> ${order.orderNumber}</p>
        <p><strong>تاريخ الطلب:</strong> ${new Date(order.orderDate).toLocaleDateString('ar-SA')}</p>
        <p><strong>العميل:</strong> ${customer.name}</p>
        <p><strong>الهاتف:</strong> ${customer.phone}</p>
        <p><strong>البريد الإلكتروني:</strong> ${customer.email}</p>
        <p><strong>المبلغ الإجمالي:</strong> ${order.totalAmount} ${config.get('currency')}</p>
        <p><strong>تاريخ التسليم المطلوب:</strong> ${new Date(order.requiredDate).toLocaleDateString('ar-SA')}</p>
      </div>
      <p>يرجى مراجعة الطلب في نظام إدارة المطبعة.</p>
      <a href="${config.get('clientUrl')}/orders/${order._id}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">عرض تفاصيل الطلب</a>
    </div>
  `;

  // إرسال الإشعار إلى المسؤولين
  return sendEmail({
    to: emailConfig.notificationEmails.join(','),
    subject,
    html
  });
};

/**
 * إرسال إشعار بريد إلكتروني بتغيير حالة الطلب
 * @param {Object} order - بيانات الطلب
 * @param {Object} customer - بيانات العميل
 * @param {string} oldStatus - الحالة القديمة
 * @param {string} newStatus - الحالة الجديدة
 * @returns {Promise} وعد بنتيجة الإرسال
 */
const sendOrderStatusChangeNotification = async (order, customer, oldStatus, newStatus) => {
  // ترجمة حالات الطلب
  const statusTranslations = {
    'pending': 'قيد الانتظار',
    'processing': 'قيد المعالجة',
    'in-production': 'في الإنتاج',
    'ready': 'جاهز للتسليم',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغي'
  };

  const subject = `تحديث حالة الطلب #${order.orderNumber}`;
  const html = `
    <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
      <h2>تحديث حالة الطلب</h2>
      <p>تم تحديث حالة الطلب الخاص بك.</p>
      <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
        <p><strong>رقم الطلب:</strong> ${order.orderNumber}</p>
        <p><strong>تاريخ الطلب:</strong> ${new Date(order.orderDate).toLocaleDateString('ar-SA')}</p>
        <p><strong>الحالة السابقة:</strong> ${statusTranslations[oldStatus] || oldStatus}</p>
        <p><strong>الحالة الجديدة:</strong> ${statusTranslations[newStatus] || newStatus}</p>
        <p><strong>تاريخ التسليم المتوقع:</strong> ${new Date(order.estimatedDeliveryDate).toLocaleDateString('ar-SA')}</p>
      </div>
      <p>يمكنك متابعة حالة طلبك من خلال الرابط أدناه.</p>
      <a href="${config.get('clientUrl')}/track-order/${order._id}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">تتبع الطلب</a>
    </div>
  `;

  // إرسال الإشعار إلى العميل
  return sendEmail({
    to: customer.email,
    subject,
    html
  });
};

/**
 * إرسال إشعار بريد إلكتروني بالفاتورة الجديدة
 * @param {Object} invoice - بيانات الفاتورة
 * @param {Object} customer - بيانات العميل
 * @param {string} invoicePdfPath - مسار ملف PDF للفاتورة (اختياري)
 * @returns {Promise} وعد بنتيجة الإرسال
 */
const sendInvoiceNotification = async (invoice, customer, invoicePdfPath = null) => {
  const subject = `فاتورة جديدة #${invoice.invoiceNumber}`;
  const html = `
    <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
      <h2>فاتورة جديدة</h2>
      <p>مرحبًا ${customer.name}،</p>
      <p>نرفق لكم فاتورة جديدة لطلبكم.</p>
      <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
        <p><strong>رقم الفاتورة:</strong> ${invoice.invoiceNumber}</p>
        <p><strong>تاريخ الفاتورة:</strong> ${new Date(invoice.invoiceDate).toLocaleDateString('ar-SA')}</p>
        <p><strong>المبلغ:</strong> ${invoice.totalAmount} ${config.get('currency')}</p>
        <p><strong>تاريخ الاستحقاق:</strong> ${new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</p>
      </div>
      <p>يمكنك عرض الفاتورة وتنزيلها من خلال الرابط أدناه.</p>
      <a href="${config.get('clientUrl')}/invoices/${invoice._id}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">عرض الفاتورة</a>
    </div>
  `;

  // إعداد المرفقات إذا كان هناك ملف PDF
  const attachments = [];
  if (invoicePdfPath) {
    attachments.push({
      filename: `فاتورة-${invoice.invoiceNumber}.pdf`,
      path: invoicePdfPath
    });
  }

  // إرسال الإشعار إلى العميل
  return sendEmail({
    to: customer.email,
    subject,
    html,
    attachments
  });
};

/**
 * إرسال إشعار بريد إلكتروني بانخفاض المخزون
 * @param {Array} lowStockItems - قائمة العناصر منخفضة المخزون
 * @returns {Promise} وعد بنتيجة الإرسال
 */
const sendLowStockNotification = async (lowStockItems) => {
  const subject = 'تنبيه: انخفاض مستوى المخزون';
  
  // إنشاء جدول بالعناصر منخفضة المخزون
  let itemsTable = `
    <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
      <thead>
        <tr style="background-color: #f2f2f2;">
          <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">كود المنتج</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">اسم المنتج</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">الكمية الحالية</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">الحد الأدنى</th>
          <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">الفئة</th>
        </tr>
      </thead>
      <tbody>
  `;

  lowStockItems.forEach(item => {
    itemsTable += `
      <tr>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${item.itemCode}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${item.name}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${item.currentQuantity} ${item.unit}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${item.minQuantity} ${item.unit}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${item.category}</td>
      </tr>
    `;
  });

  itemsTable += `
      </tbody>
    </table>
  `;

  const html = `
    <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
      <h2>تنبيه: انخفاض مستوى المخزون</h2>
      <p>نود إعلامكم بأن العناصر التالية قد وصلت إلى مستوى منخفض في المخزون ويجب إعادة طلبها:</p>
      ${itemsTable}
      <p style="margin-top: 20px;">يرجى اتخاذ الإجراء اللازم لإعادة تعبئة المخزون.</p>
      <a href="${config.get('clientUrl')}/inventory" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px; margin-top: 15px;">إدارة المخزون</a>
    </div>
  `;

  // إرسال الإشعار إلى المسؤولين
  return sendEmail({
    to: emailConfig.notificationEmails.join(','),
    subject,
    html
  });
};

module.exports = {
  sendEmail,
  sendNewOrderNotification,
  sendOrderStatusChangeNotification,
  sendInvoiceNotification,
  sendLowStockNotification
};