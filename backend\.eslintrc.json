{"env": {"node": true, "es2021": true, "jest": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module"}, "rules": {"indent": ["error", 2], "linebreak-style": ["error", "unix"], "quotes": ["error", "single"], "semi": ["error", "always"], "no-unused-vars": ["warn"], "no-console": ["warn", {"allow": ["warn", "error", "info"]}], "no-undef": "error", "no-var": "error", "prefer-const": "warn", "eqeqeq": "warn", "curly": "error", "brace-style": ["error", "1tbs"], "comma-dangle": ["error", "never"], "max-len": ["warn", {"code": 100}], "camelcase": "warn"}}